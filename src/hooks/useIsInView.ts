'use client';

import { useState, useEffect, RefObject } from 'react';

interface Options {
    threshold?: number;
    rootMargin?: string;
}

export function useIsInView(ref: RefObject<HTMLElement | null>, options: Options = {}) {
    const [isIntersecting, setIntersecting] = useState(false);

    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                setIntersecting(entry.isIntersecting);
            },
            {
                threshold: options.threshold || 0,
                rootMargin: options.rootMargin || '0px'
            }
        );

        if (ref.current) {
            observer.observe(ref.current);
        }

        return () => {
            observer.disconnect();
        };
    }, [ref, options.threshold, options.rootMargin]);

    return isIntersecting;
} 