/**
 * Custom hook for financial data processing
 * Provides loading states, error handling, and memoization
 */

import { useState, useEffect, useMemo } from 'react';
import { FinancialParserService } from '@/services/financial-parser.service';

// Types
interface FinancialData {
    month: string;
    totalRevenue: number;
    netProfitAfterTax: number;
    netProfitMargin: number;
}

interface YearData {
    year: string;
    data: FinancialData[];
}

interface DataRoomFile {
    id: string;
    file_name: string;
    file_url: string;
}

interface UseFinancialDataState {
    data: YearData[];
    loading: boolean;
    error: string | null;
    progress: number; // 0-100 percentage
}

export function useFinancialData(files: DataRoomFile[] | undefined): UseFinancialDataState {
    const [state, setState] = useState<UseFinancialDataState>({
        data: [],
        loading: false,
        error: null,
        progress: 0
    });

    // Memoize file IDs to detect changes
    const fileIds = useMemo(() => {
        if (!files || files.length === 0) return '';
        return files.map(f => f.id).sort().join(',');
    }, [files]);

    useEffect(() => {
        // Reset state if no files
        if (!files || files.length === 0) {
            setState({
                data: [],
                loading: false,
                error: null,
                progress: 0
            });
            return;
        }

        let isCancelled = false;

        const processFiles = async () => {
            setState(prev => ({
                ...prev,
                loading: true,
                error: null,
                progress: 0
            }));

            try {
                // Use the new service method
                const results = await FinancialParserService.parseFinancialFiles(files);

                if (!isCancelled) {
                    setState({
                        data: results,
                        loading: false,
                        error: null,
                        progress: 100
                    });
                }
            } catch (error) {
                if (!isCancelled) {
                    setState({
                        data: [],
                        loading: false,
                        error: error instanceof Error ? error.message : 'Failed to process financial data',
                        progress: 0
                    });
                }
            }
        };

        // Add a small delay to prevent blocking the main thread
        const timeoutId = setTimeout(processFiles, 100);

        // Cleanup function to cancel operation
        return () => {
            isCancelled = true;
            clearTimeout(timeoutId);
        };
    }, [fileIds, files]);

    return state;
}

/**
 * Hook for getting financial summary statistics
 */
export function useFinancialSummary(yearData: YearData[]) {
    return useMemo(() => {
        if (yearData.length === 0) {
            return {
                totalYears: 0,
                totalRevenue: 0,
                totalProfit: 0,
                averageMargin: 0,
                yearlyGrowth: 0
            };
        }

        // Calculate totals from the new data structure
        const totalRevenue = yearData.reduce((sum, year) =>
            sum + year.data.reduce((yearSum, month) => yearSum + month.totalRevenue, 0), 0
        );

        const totalProfit = yearData.reduce((sum, year) =>
            sum + year.data.reduce((yearSum, month) => yearSum + month.netProfitAfterTax, 0), 0
        );

        const averageMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

        // Calculate year-over-year growth if we have multiple years
        let yearlyGrowth = 0;
        if (yearData.length >= 2) {
            const sortedYears = [...yearData].sort((a, b) => parseInt(a.year) - parseInt(b.year));
            const oldestYear = sortedYears[0];
            const newestYear = sortedYears[sortedYears.length - 1];

            const oldestYearRevenue = oldestYear.data.reduce((sum, month) => sum + month.totalRevenue, 0);
            const newestYearRevenue = newestYear.data.reduce((sum, month) => sum + month.totalRevenue, 0);

            if (oldestYearRevenue > 0) {
                yearlyGrowth = ((newestYearRevenue - oldestYearRevenue) / oldestYearRevenue) * 100;
            }
        }

        return {
            totalYears: yearData.length,
            totalRevenue: parseFloat(totalRevenue.toFixed(2)),
            totalProfit: parseFloat(totalProfit.toFixed(2)),
            averageMargin: parseFloat(averageMargin.toFixed(2)),
            yearlyGrowth: parseFloat(yearlyGrowth.toFixed(2))
        };
    }, [yearData]);
} 