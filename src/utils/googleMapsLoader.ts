// Global Google Maps loader utility
declare global {
    interface Window {
        google: {
            maps: {
                places: {
                    AutocompleteService: new () => google.maps.places.AutocompleteService
                    PlacesService: new (div: HTMLDivElement) => google.maps.places.PlacesService
                    PlacesServiceStatus: {
                        OK: string
                    }
                }
            }
        }
    }
}

class GoogleMapsLoader {
    private static instance: GoogleMapsLoader
    private isLoading = false
    private isLoaded = false
    private loadPromise: Promise<void> | null = null

    private constructor() { }

    static getInstance(): GoogleMapsLoader {
        if (!GoogleMapsLoader.instance) {
            GoogleMapsLoader.instance = new GoogleMapsLoader()
        }
        return GoogleMapsLoader.instance
    }

    async load(): Promise<void> {
        // Return existing promise if already loading
        if (this.loadPromise) {
            return this.loadPromise
        }

        // Check if already loaded
        if (this.isLoaded && typeof window !== 'undefined' && window.google?.maps?.places) {
            return Promise.resolve()
        }

        // Check if script already exists
        if (typeof window !== 'undefined') {
            const existingScript = document.querySelector('script[src*="maps.googleapis.com"]')
            if (existingScript && window.google?.maps?.places) {
                this.isLoaded = true
                return Promise.resolve()
            }
        }

        // Create new loading promise
        this.loadPromise = this.createLoadPromise()
        return this.loadPromise
    }

    private createLoadPromise(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (typeof window === 'undefined') {
                reject(new Error('Window is not available'))
                return
            }

            // Check if already loaded
            if (window.google?.maps?.places) {
                this.isLoaded = true
                this.isLoading = false
                resolve()
                return
            }

            // Check if script already exists but not loaded
            const existingScript = document.querySelector('script[src*="maps.googleapis.com"]')
            if (existingScript) {
                const handleLoad = () => {
                    this.isLoaded = true
                    this.isLoading = false
                    existingScript.removeEventListener('load', handleLoad)
                    existingScript.removeEventListener('error', handleError)
                    resolve()
                }

                const handleError = () => {
                    this.isLoading = false
                    existingScript.removeEventListener('load', handleLoad)
                    existingScript.removeEventListener('error', handleError)
                    reject(new Error('Failed to load Google Maps'))
                }

                existingScript.addEventListener('load', handleLoad)
                existingScript.addEventListener('error', handleError)
                return
            }

            // Create and load script
            this.isLoading = true

            const script = document.createElement('script')
            const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY

            if (!apiKey) {
                reject(new Error('Google Maps API key is not configured'))
                return
            }

            script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&loading=async`
            script.async = true
            script.defer = true

            script.onload = () => {
                this.isLoaded = true
                this.isLoading = false
                this.loadPromise = null
                resolve()
            }

            script.onerror = () => {
                this.isLoading = false
                this.loadPromise = null
                reject(new Error('Failed to load Google Maps'))
            }

            document.head.appendChild(script)
        })
    }

    isGoogleMapsReady(): boolean {
        return this.isLoaded && typeof window !== 'undefined' && !!window.google?.maps?.places
    }
}

export const googleMapsLoader = GoogleMapsLoader.getInstance()
export default googleMapsLoader 