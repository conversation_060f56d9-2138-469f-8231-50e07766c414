// Mapping from Google Places types to industry and sub-industry IDs
// You'll need to adjust these IDs based on your actual database values

interface IndustryMapping {
    industryId: string;
    subIndustryId?: string;
}

// This is a sample mapping - you'll need to update with your actual industry IDs
const placeTypeToIndustryMap: Record<string, IndustryMapping> = {
    // Food & Beverage
    'restaurant': { industryId: '3', subIndustryId: '301' },
    'food': { industryId: '3', subIndustryId: '301' },
    'meal_delivery': { industryId: '3', subIndustryId: '301' },
    'meal_takeaway': { industryId: '3', subIndustryId: '301' },
    'cafe': { industryId: '3', subIndustryId: '302' },
    'bakery': { industryId: '3', subIndustryId: '303' },
    'bar': { industryId: '3', subIndustryId: '304' },
    'pizza': { industryId: '3', subIndustryId: '305' },

    // Retail
    'store': { industryId: '2' },
    'clothing_store': { industryId: '2', subIndustryId: '201' },
    'shoe_store': { industryId: '2', subIndustryId: '202' },
    'jewelry_store': { industryId: '2', subIndustryId: '203' },
    'electronics_store': { industryId: '2', subIndustryId: '204' },

    // Health & Beauty
    'beauty_salon': { industryId: '3', subIndustryId: '301' },
    'hair_care': { industryId: '3', subIndustryId: '301' },
    'spa': { industryId: '3', subIndustryId: '302' },
    'health': { industryId: '3' },

    // Professional Services
    'lawyer': { industryId: '4', subIndustryId: '401' },
    'accounting': { industryId: '4', subIndustryId: '402' },
    'insurance_agency': { industryId: '4', subIndustryId: '403' },
    'real_estate_agency': { industryId: '4', subIndustryId: '404' },

    // Technology
    'point_of_interest': { industryId: '5' }, // Default for tech companies

    // Hospitality
    'lodging': { industryId: '6', subIndustryId: '601' },
    'hotel': { industryId: '6', subIndustryId: '601' },

    // Automotive
    'car_dealer': { industryId: '7', subIndustryId: '701' },
    'car_repair': { industryId: '7', subIndustryId: '702' },
    'car_wash': { industryId: '7', subIndustryId: '703' },

    // Education
    'school': { industryId: '8', subIndustryId: '801' },
    'university': { industryId: '8', subIndustryId: '802' },

    // Default
    'establishment': { industryId: '9' } // General business
};

/**
 * Find the best matching industry and sub-industry based on Google Places types
 * @param types Array of Google Places types
 * @param industries Available industries from the database
 * @param subIndustries Available sub-industries from the database
 * @param businessName Optional business name to help with classification
 * @returns Object with industryId and subIndustryId
 */
export function findIndustryFromPlaceTypes(
    types: string[],
    industries: Array<{ id: string, name: string }>,
    subIndustries: Array<{ id: string, name: string, industry_id: string }>,
    businessName?: string
): { industryId: string | null, subIndustryId: string | null } {
    // Default result
    const result: { industryId: string | null, subIndustryId: string | null } = {
        industryId: null,
        subIndustryId: null
    };

    // Priority types for specific industries
    const foodTypes = ['restaurant', 'food', 'meal_delivery', 'meal_takeaway', 'cafe', 'bakery', 'pizza'];

    // Check if this is a food business
    const isFood = types.some(type => foodTypes.includes(type));

    // Check if it's specifically a pizza place
    const isPizza = types.includes('pizza') ||
        (businessName && businessName.toLowerCase().includes('pizza')) ||
        (types.includes('restaurant') && businessName && businessName.toLowerCase().includes('pizza'));

    if (isFood) {
        // Find your restaurant industry ID
        const restaurantIndustry = industries.find(i =>
            i.name.toLowerCase().includes('restaurant') ||
            i.name.toLowerCase().includes('food') ||
            i.name.toLowerCase().includes('dining')
        );

        if (restaurantIndustry) {
            result.industryId = restaurantIndustry.id;

            // If it's a pizza place, find a pizza sub-industry
            if (isPizza) {
                const pizzaSubIndustry = subIndustries.find(si =>
                    si.industry_id === restaurantIndustry.id &&
                    (si.name.toLowerCase().includes('pizza') ||
                        si.name.toLowerCase().includes('italian'))
                );

                if (pizzaSubIndustry) {
                    result.subIndustryId = pizzaSubIndustry.id;
                }
            }

            // If we didn't find a pizza sub-industry or it's not a pizza place
            if (!result.subIndustryId) {
                // Try to find a more specific match based on types
                for (const type of types) {
                    if (type === 'cafe' || type === 'coffee') {
                        const cafeSubIndustry = subIndustries.find(si =>
                            si.industry_id === restaurantIndustry.id &&
                            si.name.toLowerCase().includes('cafe')
                        );
                        if (cafeSubIndustry) {
                            result.subIndustryId = cafeSubIndustry.id;
                            break;
                        }
                    } else if (type === 'bakery') {
                        const bakerySubIndustry = subIndustries.find(si =>
                            si.industry_id === restaurantIndustry.id &&
                            si.name.toLowerCase().includes('bakery')
                        );
                        if (bakerySubIndustry) {
                            result.subIndustryId = bakerySubIndustry.id;
                            break;
                        }
                    } else if (type === 'bar') {
                        const barSubIndustry = subIndustries.find(si =>
                            si.industry_id === restaurantIndustry.id &&
                            (si.name.toLowerCase().includes('bar') ||
                                si.name.toLowerCase().includes('pub'))
                        );
                        if (barSubIndustry) {
                            result.subIndustryId = barSubIndustry.id;
                            break;
                        }
                    }
                }

                // If still no match, fallback to a general restaurant sub-industry
                if (!result.subIndustryId) {
                    const generalRestaurant = subIndustries.find(si =>
                        si.industry_id === restaurantIndustry.id &&
                        (si.name.toLowerCase().includes('restaurant') ||
                            si.name.toLowerCase().includes('general'))
                    );

                    if (generalRestaurant) {
                        result.subIndustryId = generalRestaurant.id;
                    }
                }
            }

            // If we found both, return immediately
            if (result.industryId && result.subIndustryId) {
                return result;
            }
        }
    }

    // If we didn't find a match with the priority logic, continue with the original logic
    for (const type of types) {
        const mapping = placeTypeToIndustryMap[type];
        if (mapping) {
            // Verify the industry exists in our database
            const industryExists = industries.some(i => i.id === mapping.industryId);
            if (industryExists) {
                result.industryId = mapping.industryId;

                // If we have a sub-industry mapping, verify it exists and belongs to the industry
                if (mapping.subIndustryId) {
                    const subIndustryExists = subIndustries.some(
                        si => si.id === mapping.subIndustryId && si.industry_id === mapping.industryId
                    );
                    if (subIndustryExists) {
                        result.subIndustryId = mapping.subIndustryId;
                    }
                }

                // If we found both industry and sub-industry, return immediately
                if (result.industryId && result.subIndustryId) {
                    return result;
                }
            }
        }
    }

    // If we found an industry but no sub-industry, try to find a default sub-industry
    if (result.industryId && !result.subIndustryId) {
        // Get the first sub-industry for this industry
        const defaultSubIndustry = subIndustries.find(si => si.industry_id === result.industryId);
        if (defaultSubIndustry) {
            result.subIndustryId = defaultSubIndustry.id;
        }
    }

    // If we still don't have an industry, use a default
    if (!result.industryId && industries.length > 0) {
        // Use the first industry as default
        result.industryId = industries[0].id;

        // And its first sub-industry if available
        const defaultSubIndustry = subIndustries.find(si => si.industry_id === result.industryId);
        if (defaultSubIndustry) {
            result.subIndustryId = defaultSubIndustry.id;
        }
    }

    return result;
} 