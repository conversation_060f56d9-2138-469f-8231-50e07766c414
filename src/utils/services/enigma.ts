interface EnigmaData {
    // Support both new simplified format and original format
    monthly_revenue?: number;
    annual_growth_rate?: number;
    card_revenue?: CardRevenue[];
    card_transactions?: CardTransaction[];
    growth_rate?: GrowthRate[];
    error?: string;
}

// Define proper types instead of any
interface CardRevenue {
    "12m"?: {
        average_monthly_amount?: number;
    };
    [key: string]: unknown;
}

interface CardTransaction {
    "12m"?: {
        average_monthly_count?: number;
    };
    [key: string]: unknown;
}

interface GrowthRate {
    "12m"?: {
        rate?: number;
    };
    [key: string]: unknown;
}

interface BusinessIdentifier {
    name?: string;
    website?: string;
    postalCode?: string;
}

export async function getEnigmaData(identifier: BusinessIdentifier): Promise<EnigmaData> {
    console.log('🔍 Fetching Enigma data for:', identifier);

    if (!identifier.name && !identifier.website) {
        console.error('Either business name or website is required');
        return { error: 'Either business name or website is required' };
    }

    try {
        const response = await fetch('/api/enigma', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                businessName: identifier.name?.trim(),
                website: identifier.website?.trim(),
                postalCode: identifier.postalCode || '18901'
            })
        });

        console.log('Response status:', response.status);
        const data = await response.json();

        if (!response.ok) {
            console.error('API error:', data);
            throw new Error(data.error || 'Failed to fetch Enigma data');
        }

        console.log('Received data:', data);

        // Handle both the new simplified format and the original format
        return {
            // New simplified format
            monthly_revenue: data.monthly_revenue,
            annual_growth_rate: data.annual_growth_rate,

            // Original format for backwards compatibility
            card_revenue: data.card_revenue,
            card_transactions: data.card_transactions,
            growth_rate: data.growth_rate
        };
    } catch (error) {
        console.error('Error fetching Enigma data:', error);
        return {
            error: error instanceof Error ? error.message : 'Unknown error occurred'
        };
    }
} 