// Declare gtag function type for TypeScript
declare global {
    interface Window {
        gtag: (
            command: 'config' | 'event' | 'js',
            targetId: string | Date,
            config?: Record<string, string | number | boolean | undefined>
        ) => void;
    }
}

// GA4 Standard Event Parameters (automatically indexed)
type StandardEventParams = {
    // E-commerce parameters
    item_id?: string;
    item_name?: string;
    item_category?: string;
    item_brand?: string;
    price?: number;
    quantity?: number;
    currency?: string;
    value?: number;

    // Search parameters (automatically indexed)
    search_term?: string;

    // General parameters
    content_type?: string;
    content_id?: string;

    // Custom parameters for business listings
    location?: string;           // Instead of state_name - more semantic
    listing_type?: string;       // Instead of search_type - more specific
    has_query?: boolean;
    has_location_filter?: boolean;
};

// Enhanced event tracking with GA4 best practices
export const event = (eventName: string, params: StandardEventParams = {}) => {
    // Check if we're on the client side
    if (typeof window === 'undefined') {
        // Only log in development and avoid accessing process.env during SSR
        if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
            console.log('📊 Analytics (Server-side, skipped):', eventName, params);
        }
        return;
    }

    // Now we're safely on the client side
    const isDevelopment = process.env.NODE_ENV === 'development';

    // Check if gtag is actually available
    if (typeof window.gtag === 'undefined') {
        if (isDevelopment) {
            console.warn('⚠️ Analytics (gtag not loaded):', eventName, params);
            console.log('📊 Analytics (Development):', eventName, params);
        }
        return;
    }

    // Check if GA_ID is configured
    if (!process.env.NEXT_PUBLIC_GA_ID) {
        if (isDevelopment) {
            console.warn('⚠️ Analytics: NEXT_PUBLIC_GA_ID not configured');
            console.log('📊 Analytics (No GA_ID):', eventName, params);
        }
        return;
    }

    try {
        // Send the actual event
        window.gtag('event', eventName, params);

        if (isDevelopment) {
            console.log('✅ Analytics (Sent):', eventName, params);
        }
    } catch (error) {
        if (isDevelopment) {
            console.error('❌ Analytics Error:', error);
        }
        // You could also send this error to your error tracking service
    }
};

// Specific tracking functions for common actions
export const trackSearch = (searchTerm: string, location?: string) => {
    event('search', {
        search_term: searchTerm || '(empty)',
        location: location || 'all_locations',
        listing_type: 'business_for_sale',
        has_query: !!searchTerm,
        has_location_filter: !!location,
    });
};

export const trackListingView = (listingId: string, listingName: string, category?: string) => {
    event('view_item', {
        item_id: listingId,
        item_name: listingName,
        item_category: category || 'business',
        content_type: 'business_listing',
    });
};

export const trackListingInteraction = (action: string, listingId: string) => {
    event(action, {
        content_type: 'business_listing',
        content_id: listingId,
    });
};

// Add a function to verify analytics is working
export const verifyAnalytics = () => {
    if (typeof window === 'undefined') return false;

    const isDevelopment = process.env.NODE_ENV === 'development';
    const hasGtag = typeof window.gtag !== 'undefined';
    const hasGaId = !!process.env.NEXT_PUBLIC_GA_ID;

    if (isDevelopment) {
        console.log('🔍 Analytics Status:', {
            hasGtag,
            hasGaId,
            gaId: process.env.NEXT_PUBLIC_GA_ID || 'NOT_SET'
        });
    }

    return hasGtag && hasGaId;
}; 