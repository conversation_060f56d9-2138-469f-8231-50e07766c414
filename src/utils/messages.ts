import { createClient } from '@/utils/supabase/server';

type Message = {
  id: string;
  listing_id: string;
  sender_id: string;
  content: string;
  read: boolean;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    profile_photo: string | null;
  } | null;
};

export async function getMessagesForListing(listingId: string) {
  const supabase = await createClient();

  // First get the messages
  const { data: messages, error } = await supabase
    .from('messages')
    .select('*')
    .eq('listing_id', listingId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching messages:', error);
    throw error;
  }

  if (!messages?.length) return [];

  // Then get the profiles for these senders
  const { data: profiles, error: profilesError } = await supabase
    .from('profiles')
    .select('*')
    .in('user_id', messages.map(m => m.sender_id));

  if (profilesError) {
    console.error('Error fetching profiles:', profilesError);
    throw profilesError;
  }

  // Combine the data
  const messagesWithProfiles = messages.map(message => ({
    ...message,
    profiles: profiles?.find(p => p.user_id === message.sender_id) || null
  }));

  return messagesWithProfiles as Message[];
}

export async function sendMessage(listingId: string, content: string) {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('messages')
    .insert({
      listing_id: listingId,
      content,
      sender_id: (await supabase.auth.getUser()).data.user?.id
    })
    .select()
    .single();

  if (error) {
    console.error('Error sending message:', error);
    throw error;
  }

  return data;
} 