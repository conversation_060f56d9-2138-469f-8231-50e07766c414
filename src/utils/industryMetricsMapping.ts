import { industryMetrics } from '@/data/industryMetrics';
import { createClient } from '@/utils/supabase/client';

// This maps the industry names from our database to the industryMetrics keys
const INDUSTRY_NAME_MAPPING: Record<string, string> = {
    'Manufacturing': 'Manufacturing',
    'Wholesale & Distributors': 'Wholesale and Distributors',
    'Online & Technology': 'Online and Technology',
    'Retail': 'Retail',
    'Health Care & Fitness': 'Health Care and Fitness',
    'Transportation & Storage': 'Transportation and Storage',
    'Non-Classifiable Establishments': 'Non-Classifiable Establishments',
    'Pet Services': 'Pet Services',
    'Building & Construction': 'Building and Construction',
    'Restaurants & Food': 'Food and Restaurants',
    'Travel': 'Travel',
    'Financial Services': 'Financial Services',
    'Beauty & Personal Care': 'Beauty and Personal Care',
    'Education & Children': 'Education and Children',
    'Communication & Media': 'Communication and Media',
    'Agriculture': 'Agriculture',
    'Automotive & Boat': 'Automotive and Boat',
    'Service Businesses': 'Service Businesses',
    'Entertainment & Recreation': 'Entertainment and Recreation'
};

// Add sub-industry mapping
const SUB_INDUSTRY_MAPPING: Record<string, string> = {
    // Agriculture
    "Tree Farms & Orchards": "Tree Farms and Orchards",
    "Vineyards & Wineries": "Vineyards and Wineries",

    // Automotive & Boat
    "Auto Repair & Service Shops": "Auto Repair and Service Shops",
    "Car Washes": "Car Washes",
    "Equipment Rental & Dealers": "Equipment Rental and Dealers",
    "Gas Stations": "Gas Stations",
    "Marine/Boat Service & Dealers": "Marine/Boat Services and Dealers",
    "Other Automotive & Boat": "Other Automotive and Boat Businesses",
    "Truck Stops": "Truck Stops",
    "Trucking Companies": "Trucking Companies",

    // Beauty & Personal Care
    "Hair Salons & Barber Shops": "Hair Salons and Barber Shops",
    "Massage": "Massage Businesses",
    "Nail Salons": "Nail Salons",
    "Spas": "Spas",
    "Tanning Salons": "Tanning Salons",
    "Other Beauty & Personal Care": "Other Beauty and Personal Care Businesses",

    // Building & Construction
    "Concrete": "Concrete Businesses",
    "Glass, Stone & Concrete": "Concrete Businesses",
    "Plumbing": "Plumbing Businesses",
    "Other Building & Construction": "Other Building and Construction Businesses",

    // Entertainment & Recreation
    "Bowling Alleys": "Bowling Alleys",
    "Golf Courses & Services": "Golf Courses",
    "Nightclubs & Theaters": "Night Clubs",
    "Casinos": "Casinos and Gaming Businesses",

    // Food & Restaurants
    "American Restaurants": "American Restaurants",
    "Coffee Shops & Cafes": "Coffee Shops and Cafes",
    "Donut Shops": "Bakeries and Donut Shops",
    "Ice Cream & Frozen Yogurt Shops": "Ice Cream and Frozen Yogurt Shops",
    "Italian Restaurants": "Italian Restaurants",
    "Mexican Restaurants": "Mexican Restaurants",
    "Pizza Restaurants": "Pizza Restaurants",
    "Sushi & Japanese Restaurants": "Asian Restaurants",
    "Thai Restaurants": "Asian Restaurants",
    "Other Restaurants & Food": "Other Food Businesses",

    // Health Care & Fitness
    "Dental Practices": "Dental Practices",
    "Gyms & Fitness Centers": "Gyms and Fitness Centers",
    "Medical Practices": "Medical Practices",
    "Dance, Pilates & Yoga": "Gyms and Fitness Centers",
    "Other Health Care & Fitness": "Other Health Care Businesses",

    // Pet Services
    "Dog Daycare & Boarding": "Dog Daycare and Boarding Businesses",
    "Pet Grooming": "Pet Grooming Businesses",
    "Pet Stores & Supplies": "Pet Stores and Supply Businesses",

    // Retail
    "Bike Shops": "Bike Shops",
    "Clothing & Fabric": "Clothing and Accessory Stores",
    "Convenience Stores": "Convenience Stores",
    "Flower Shops": "Flower Shops",
    "Furniture & Furnishings Stores": "Furniture and Furnishings Stores",
    "Grocery Stores & Supermarkets": "Grocery Stores and Supermarkets",
    "Health Food & Nutrition": "Health Food and Nutrition Businesses",
    "Jewelry Stores": "Jewelry Stores",
    "Liquor Stores": "Liquor Stores",
    "Smoke Shops": "Tobacco Stores",

    // Transportation & Storage
    "Limo & Passenger Transportation": "Limo and Passenger Transportation Businesses",
    "Moving & Shipping": "Moving and Shipping Businesses",
    "Other Transportation & Storage": "Other Transportation and Storage Businesses",
    "Routes": "Routes",

    // Travel
    "Bed & Breakfasts": "Bed and Breakfasts",
    "Travel Agencies": "Travel Agencies",
    "Other Travel": "Other Travel Businesses",

    // Wholesale & Distribution
    "Durable Goods": "Durable Goods Wholesalers and Distributors",
    "Nondurable Goods": "Nondurable Goods Wholesalers and Distributors",

    // Services (map to closest categories)
    "Cell Phone & Computer Repair & Services": "Computer and Electronics Businesses",
    "Landscaping & Yard Services": "Landscaping Businesses",
    "Legal Services & Law Firms": "Professional Services Businesses",
    "Property Management": "Property Management Businesses",
    "Staffing Agencies": "Professional Services Businesses",
    "Security": "Security and Protection Businesses"
};

export interface MetricsSummary {
    reported_sales: number;
    medianPrice: number;
    medianAskingPrice: number;
    averageSalesRatio: number;
    medianRevenueMultiple: number;
    medianCashflowMultiple: number;
    medianDaysOnMarket: number;
}

export const getIndustryMetricsSummary = async (
    industryId: string,
    subIndustryId: string
): Promise<MetricsSummary | null> => {
    try {
        const supabase = createClient();

        // Fetch industry and sub-industry names from database
        const { data, error } = await supabase
            .from('industries')
            .select(`
                name,
                sub_industries!inner (
                    id,
                    name
                )
            `)
            .eq('id', industryId)
            .single();

        if (error || !data) {
            console.log('Error fetching industry:', error);
            return null;
        }

        const industryName = data.name;
        const metricsKey = INDUSTRY_NAME_MAPPING[industryName];

        if (!metricsKey || !industryMetrics[metricsKey]) {
            console.log('No metrics found for industry:', metricsKey);
            return null;
        }

        // If sub-industry is provided, try to get specific metrics
        if (subIndustryId) {
            const subIndustry = data.sub_industries.find((si: { id: string; name: string }) => si.id === subIndustryId);
            if (subIndustry) {
                const subIndustryKey = SUB_INDUSTRY_MAPPING[subIndustry.name];
                if (subIndustryKey && industryMetrics[metricsKey][subIndustryKey]) {
                    // Return metrics for specific sub-industry
                    const metrics = industryMetrics[metricsKey][subIndustryKey];
                    return {
                        reported_sales: metrics.reported_sales,
                        medianPrice: metrics.median_sale_price,
                        medianAskingPrice: metrics.median_asking_price,
                        averageSalesRatio: metrics.sales_to_asking_ratio,
                        medianRevenueMultiple: metrics.revenue_multiple,
                        medianCashflowMultiple: metrics.cashflow_multiple,
                        medianDaysOnMarket: metrics.median_days_on_market
                    };
                }
            }
        }

        // Fallback to industry average if no sub-industry match
        const subIndustries = Object.values(industryMetrics[metricsKey]);

        // Calculate aggregated metrics
        const summary: MetricsSummary = {
            reported_sales: 0,
            medianPrice: 0,
            medianAskingPrice: 0,
            averageSalesRatio: 0,
            medianRevenueMultiple: 0,
            medianCashflowMultiple: 0,
            medianDaysOnMarket: 0
        };

        const totalSubIndustries = subIndustries.length;

        // Sum up all metrics
        subIndustries.forEach(metrics => {
            summary.reported_sales += metrics.reported_sales;
            summary.medianPrice += metrics.median_sale_price;
            summary.medianAskingPrice += metrics.median_asking_price;
            summary.averageSalesRatio += metrics.sales_to_asking_ratio;
            summary.medianRevenueMultiple += metrics.revenue_multiple;
            summary.medianCashflowMultiple += metrics.cashflow_multiple;
            summary.medianDaysOnMarket += metrics.median_days_on_market;
        });

        // Calculate averages
        summary.medianPrice = Math.round(summary.medianPrice / totalSubIndustries);
        summary.medianAskingPrice = Math.round(summary.medianAskingPrice / totalSubIndustries);
        summary.averageSalesRatio = Number((summary.averageSalesRatio / totalSubIndustries).toFixed(2));
        summary.medianRevenueMultiple = Number((summary.medianRevenueMultiple / totalSubIndustries).toFixed(2));
        summary.medianCashflowMultiple = Number((summary.medianCashflowMultiple / totalSubIndustries).toFixed(2));
        summary.medianDaysOnMarket = Math.round(summary.medianDaysOnMarket / totalSubIndustries);

        return summary;
    } catch (error) {
        console.error('Error in getIndustryMetricsSummary:', error);
        return null;
    }
}

export const calculateMetrics = (): MetricsSummary => {
    return {
        reported_sales: 0,
        medianPrice: 0,
        medianAskingPrice: 0,
        averageSalesRatio: 0,
        medianRevenueMultiple: 0,
        medianCashflowMultiple: 0,
        medianDaysOnMarket: 0
    };
} 