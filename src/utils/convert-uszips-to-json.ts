// eslint-disable-next-line @typescript-eslint/no-require-imports
const fs = require('fs');
// eslint-disable-next-line @typescript-eslint/no-require-imports
const path = require('path');
// eslint-disable-next-line @typescript-eslint/no-require-imports
const csv = require('csv-parser');

const inputPath = path.join(__dirname, 'uszips.csv');
const outputPath = path.join(__dirname, 'zip-to-county.json');

const result: Record<string, { countyFips: string; stateFips: string }> = {};

fs.createReadStream(inputPath)
    .pipe(csv())
    .on('data', (row: Record<string, string>) => {
        // Try to detect the correct columns
        const zip = row.zip || row.ZIP || row.zipcode || row.ZCTA5CE10;
        const countyFips = row.county_fips || row.COUNTY || row.county || row.COUNTYFP || row.county_fips_code;
        const stateFips = row.state_fips || row.STATE || row.state || row.STATEFP || row.state_fips_code;

        if (zip && countyFips && stateFips) {
            result[zip] = {
                countyFips: countyFips.padStart(5, '0'),
                stateFips: stateFips.padStart(2, '0'),
            };
        }
    })
    .on('end', () => {
        fs.writeFileSync(outputPath, JSON.stringify(result, null, 2));
        console.log(`Done! Output written to ${outputPath}`);
    });
