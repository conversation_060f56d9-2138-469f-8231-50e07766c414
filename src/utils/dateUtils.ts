export function isListingNew(createdAt: string): boolean {
    const createdDate = new Date(createdAt);
    const now = new Date();
    const daysDifference = (now.getTime() - createdDate.getTime()) / (1000 * 3600 * 24);
    return daysDifference <= 7;
}

export function formatDateShort(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    }).format(date);
} 