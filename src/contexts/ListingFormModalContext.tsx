'use client';

import React, { createContext, useContext, useState } from 'react';

type ListingFormModalContextType = {
    isOpen: boolean;
    openModal: () => void;
    closeModal: () => void;
};

const ListingFormModalContext = createContext<ListingFormModalContextType | undefined>(undefined);

export function ListingFormModalProvider({ children }: { children: React.ReactNode }) {
    const [isOpen, setIsOpen] = useState(false);

    const openModal = () => setIsOpen(true);
    const closeModal = () => setIsOpen(false);

    return (
        <ListingFormModalContext.Provider value={{ isOpen, openModal, closeModal }}>
            {children}
        </ListingFormModalContext.Provider>
    );
}

export function useListingFormModal() {
    const context = useContext(ListingFormModalContext);
    if (context === undefined) {
        throw new Error('useListingFormModal must be used within a ListingFormModalProvider');
    }
    return context;
} 