'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

interface SavedListingContextType {
    isSaved: boolean;
    setIsSaved: (saved: boolean) => void;
}

const SavedListingContext = createContext<SavedListingContextType | undefined>(undefined);

export function SavedListingProvider({ children, initialSaved = false }: { children: ReactNode, initialSaved?: boolean }) {
    const [isSaved, setIsSaved] = useState(initialSaved);

    return (
        <SavedListingContext.Provider value={{ isSaved, setIsSaved }}>
            {children}
        </SavedListingContext.Provider>
    );
}

export function useSavedListing() {
    const context = useContext(SavedListingContext);
    if (context === undefined) {
        throw new Error('useSavedListing must be used within a SavedListingProvider');
    }
    return context;
} 