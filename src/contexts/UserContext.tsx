'use client';

import { createContext, useContext, ReactNode, useState } from 'react';

type SubscriptionPlan = 'free' | 'premium' | 'business';

interface User {
    id: string;
    subscription?: {
        plan: SubscriptionPlan;
    };
}

interface UserContextType {
    data: User | null;
    isLoading: boolean;
}

const UserContext = createContext<UserContextType>({
    data: null,
    isLoading: false
});

export function UserProvider({ children }: { children: ReactNode }) {
    // For now, return a mock user with 'free' subscription
    const [user] = useState<User | null>({
        id: 'mock-user-id',
        subscription: {
            plan: 'free',
        }
    });

    return (
        <UserContext.Provider value={{ data: user, isLoading: false }}>
            {children}
        </UserContext.Provider>
    );
}

export function useUser() {
    return useContext(UserContext);
} 