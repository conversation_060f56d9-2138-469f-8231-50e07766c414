export interface Profile {
    id: string
    user_id: string
    full_name: string | null
    headline: string | null
    profile_photo: string | null
    created_at: string
    updated_at: string
}

export interface Message {
    id: string
    sender_id: string
    recipient_id: string
    subject: string
    content: string
    read: boolean
    created_at: string
    sender: Profile
}

export interface Listing {
    id: string
    user_id: string
    title: string
    description: string
    price: number
    image_url: string | null
    created_at: string
}

export interface SavedListing {
    listing_id: string
    user_id: string
    created_at: string
    listings: Listing
} 