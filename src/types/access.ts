export interface AccessEntry {
    id: string
    user_id: string
    created_at: string
    granted_by: string // Assuming this is fetched ('*' includes it)
    listing_id: string // Assuming this is fetched ('*' includes it)
    profiles: {
        // Match fields selected in page.tsx
        id: string
        email: string | null // Profile email might be null
        first_name: string | null
        last_name: string | null
        profile_photo: string | null // Add profile_photo back
    } | null // The whole profiles object can be null if join fails
} 