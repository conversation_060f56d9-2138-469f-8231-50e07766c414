export interface Profile {
    user_id: string;
    first_name: string | null;
    last_name: string | null;
    profile_photo: string | null;
}

interface Listing {
    title: string;
    id: string;
    image_url?: string;
}

interface LastMessage {
    created_at: string;
    content: string;
    read: boolean;
    listing: Listing;
}

export interface ChatUser {
    profile: Profile;
    listingId: string;
    lastMessage: LastMessage;
}

// Base message type
export type Message = {
    id: string;
    content: string;
    created_at: string;
    sender_id: string;
    listing_id: string;
    read: boolean;
};

// Message with listings and sender profile
export type MessageWithDetails = Message & {
    sender: Profile;
    recipient_id: string;
    listings: { user_id: string; title: string; }[];
};

// Final formatted message for display
export interface FormattedMessage {
    id: string;
    conversation_id: string;
    content: string;
    created_at: string;
    sender_id: string;
    recipient_id: string;
    listing_id: string;
    read: boolean;
    sender?: Profile;
    attachments?: MessageAttachment[];
}

export interface MessageAttachment {
    id: string;
    message_id: string;
    file_name: string;
    file_url: string;
    file_size: number;
    file_type: string;
    attachment_type: 'file' | 'image' | 'letter_of_intent';
    uploaded_by: string;
    created_at: string;
}

export interface Conversation {
    id: string;
    listing_id: string;
    participant1_id: string;
    participant2_id: string;
    created_at: string;
}

export interface User {
    id: string;
    email?: string;
    created_at: string;
} 