export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      acquisition_interest_list: {
        Row: {
          company_name: string
          created_at: string | null
          email: string
          id: string
          last_month_revenue: number | null
          submitted_at: string | null
          updated_at: string | null
          utm_campaign: string | null
          utm_content: string | null
          utm_medium: string | null
          utm_source: string | null
          utm_term: string | null
          zipcode: string
        }
        Insert: {
          company_name: string
          created_at?: string | null
          email: string
          id?: string
          last_month_revenue?: number | null
          submitted_at?: string | null
          updated_at?: string | null
          utm_campaign?: string | null
          utm_content?: string | null
          utm_medium?: string | null
          utm_source?: string | null
          utm_term?: string | null
          zipcode: string
        }
        Update: {
          company_name?: string
          created_at?: string | null
          email?: string
          id?: string
          last_month_revenue?: number | null
          submitted_at?: string | null
          updated_at?: string | null
          utm_campaign?: string | null
          utm_content?: string | null
          utm_medium?: string | null
          utm_source?: string | null
          utm_term?: string | null
          zipcode?: string
        }
        Relationships: []
      }
      buyer_interest_list: {
        Row: {
          accredited_investor: boolean
          capital_allocation: Database["public"]["Enums"]["capital_range"]
          capital_max_usd: number | null
          capital_min_usd: number
          email: string
          goal: Database["public"]["Enums"]["buyer_goal"]
          id: string
          submitted_at: string
        }
        Insert: {
          accredited_investor: boolean
          capital_allocation: Database["public"]["Enums"]["capital_range"]
          capital_max_usd?: number | null
          capital_min_usd: number
          email: string
          goal: Database["public"]["Enums"]["buyer_goal"]
          id?: string
          submitted_at?: string
        }
        Update: {
          accredited_investor?: boolean
          capital_allocation?: Database["public"]["Enums"]["capital_range"]
          capital_max_usd?: number | null
          capital_min_usd?: number
          email?: string
          goal?: Database["public"]["Enums"]["buyer_goal"]
          id?: string
          submitted_at?: string
        }
        Relationships: []
      }
      conversations: {
        Row: {
          created_at: string
          id: string
          listing_id: string
          participant1_id: string
          participant2_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          listing_id: string
          participant1_id: string
          participant2_id: string
        }
        Update: {
          created_at?: string
          id?: string
          listing_id?: string
          participant1_id?: string
          participant2_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "conversations_listing_id_fkey"
            columns: ["listing_id"]
            isOneToOne: false
            referencedRelation: "listings"
            referencedColumns: ["id"]
          },
        ]
      }
      data_room_access: {
        Row: {
          created_at: string
          granted_by: string
          id: string
          listing_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          granted_by: string
          id?: string
          listing_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          granted_by?: string
          id?: string
          listing_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "data_room_access_listing_id_fkey"
            columns: ["listing_id"]
            isOneToOne: false
            referencedRelation: "listings"
            referencedColumns: ["id"]
          },
        ]
      }
      data_room_files: {
        Row: {
          category: Database["public"]["Enums"]["document_category"]
          created_at: string
          file_name: string
          file_size: number
          file_type: string
          file_url: string
          id: string
          is_public: boolean
          listing_id: string
          uploaded_by: string
          visibility: Database["public"]["Enums"]["file_visibility"]
        }
        Insert: {
          category?: Database["public"]["Enums"]["document_category"]
          created_at?: string
          file_name: string
          file_size: number
          file_type: string
          file_url: string
          id?: string
          is_public?: boolean
          listing_id: string
          uploaded_by: string
          visibility?: Database["public"]["Enums"]["file_visibility"]
        }
        Update: {
          category?: Database["public"]["Enums"]["document_category"]
          created_at?: string
          file_name?: string
          file_size?: number
          file_type?: string
          file_url?: string
          id?: string
          is_public?: boolean
          listing_id?: string
          uploaded_by?: string
          visibility?: Database["public"]["Enums"]["file_visibility"]
        }
        Relationships: [
          {
            foreignKeyName: "data_room_files_listing_id_fkey"
            columns: ["listing_id"]
            isOneToOne: false
            referencedRelation: "listings"
            referencedColumns: ["id"]
          },
        ]
      }
      industries: {
        Row: {
          id: string
          name: string
        }
        Insert: {
          id?: string
          name: string
        }
        Update: {
          id?: string
          name?: string
        }
        Relationships: []
      }
      listing_anonymized_details: {
        Row: {
          anonymous_description: string | null
          anonymous_image_url: string | null
          anonymous_title: string
          created_at: string
          id: string
          listing_id: string
        }
        Insert: {
          anonymous_description?: string | null
          anonymous_image_url?: string | null
          anonymous_title: string
          created_at?: string
          id?: string
          listing_id: string
        }
        Update: {
          anonymous_description?: string | null
          anonymous_image_url?: string | null
          anonymous_title?: string
          created_at?: string
          id?: string
          listing_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "listing_anonymized_details_listing_id_fkey"
            columns: ["listing_id"]
            isOneToOne: true
            referencedRelation: "listings"
            referencedColumns: ["id"]
          },
        ]
      }
      listing_details: {
        Row: {
          active_customers: number | null
          annual_net_profit_ttm_max: number | null
          annual_net_profit_ttm_min: number | null
          annual_revenue_ttm_max: number | null
          annual_revenue_ttm_min: number | null
          city: string | null
          created_at: string | null
          growth_rate: string | null
          id: string
          last_month_profit_max: number | null
          last_month_profit_min: number | null
          last_month_revenue_max: number | null
          last_month_revenue_min: number | null
          legal_structure: string | null
          listing_id: string
          location: string | null
          postal_code: string | null
          reason_for_selling: string | null
          recurring_revenue_max: number | null
          recurring_revenue_min: number | null
          state_id: string | null
          street_address: string | null
          team_size: number | null
          year_established: number | null
        }
        Insert: {
          active_customers?: number | null
          annual_net_profit_ttm_max?: number | null
          annual_net_profit_ttm_min?: number | null
          annual_revenue_ttm_max?: number | null
          annual_revenue_ttm_min?: number | null
          city?: string | null
          created_at?: string | null
          growth_rate?: string | null
          id?: string
          last_month_profit_max?: number | null
          last_month_profit_min?: number | null
          last_month_revenue_max?: number | null
          last_month_revenue_min?: number | null
          legal_structure?: string | null
          listing_id: string
          location?: string | null
          postal_code?: string | null
          reason_for_selling?: string | null
          recurring_revenue_max?: number | null
          recurring_revenue_min?: number | null
          state_id?: string | null
          street_address?: string | null
          team_size?: number | null
          year_established?: number | null
        }
        Update: {
          active_customers?: number | null
          annual_net_profit_ttm_max?: number | null
          annual_net_profit_ttm_min?: number | null
          annual_revenue_ttm_max?: number | null
          annual_revenue_ttm_min?: number | null
          city?: string | null
          created_at?: string | null
          growth_rate?: string | null
          id?: string
          last_month_profit_max?: number | null
          last_month_profit_min?: number | null
          last_month_revenue_max?: number | null
          last_month_revenue_min?: number | null
          legal_structure?: string | null
          listing_id?: string
          location?: string | null
          postal_code?: string | null
          reason_for_selling?: string | null
          recurring_revenue_max?: number | null
          recurring_revenue_min?: number | null
          state_id?: string | null
          street_address?: string | null
          team_size?: number | null
          year_established?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "listing_details_listing_id_fkey"
            columns: ["listing_id"]
            isOneToOne: false
            referencedRelation: "listings"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "listing_details_state_id_fkey"
            columns: ["state_id"]
            isOneToOne: false
            referencedRelation: "states"
            referencedColumns: ["id"]
          },
        ]
      }
      listing_socials: {
        Row: {
          id: string
          listing_id: string
          platform: string
          url: string
        }
        Insert: {
          id?: string
          listing_id: string
          platform: string
          url: string
        }
        Update: {
          id?: string
          listing_id?: string
          platform?: string
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "listing_socials_listing_id_fkey"
            columns: ["listing_id"]
            isOneToOne: false
            referencedRelation: "listings"
            referencedColumns: ["id"]
          },
        ]
      }
      listings: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          image_url: string | null
          industry_id: string | null
          price: number
          slug: string | null
          sub_industry_id: string | null
          title: string
          updated_at: string | null
          user_id: string
          website: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          industry_id?: string | null
          price: number
          slug?: string | null
          sub_industry_id?: string | null
          title: string
          updated_at?: string | null
          user_id: string
          website?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          industry_id?: string | null
          price?: number
          slug?: string | null
          sub_industry_id?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
          website?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "listings_industry_id_fkey"
            columns: ["industry_id"]
            isOneToOne: false
            referencedRelation: "industries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "listings_sub_industry_id_fkey"
            columns: ["sub_industry_id"]
            isOneToOne: false
            referencedRelation: "sub_industries"
            referencedColumns: ["id"]
          },
        ]
      }
      local_appeal_list: {
        Row: {
          accredited_investor: boolean
          capital_allocation: string
          created_at: string | null
          email: string
          goal: string
          id: string
          submitted_at: string | null
          updated_at: string | null
        }
        Insert: {
          accredited_investor?: boolean
          capital_allocation: string
          created_at?: string | null
          email: string
          goal: string
          id?: string
          submitted_at?: string | null
          updated_at?: string | null
        }
        Update: {
          accredited_investor?: boolean
          capital_allocation?: string
          created_at?: string | null
          email?: string
          goal?: string
          id?: string
          submitted_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      messages: {
        Row: {
          content: string
          conversation_id: string | null
          created_at: string | null
          id: string
          listing_id: string
          read: boolean | null
          recipient_id: string | null
          sender_id: string
        }
        Insert: {
          content: string
          conversation_id?: string | null
          created_at?: string | null
          id?: string
          listing_id: string
          read?: boolean | null
          recipient_id?: string | null
          sender_id: string
        }
        Update: {
          content?: string
          conversation_id?: string | null
          created_at?: string | null
          id?: string
          listing_id?: string
          read?: boolean | null
          recipient_id?: string | null
          sender_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_listing_id_fkey"
            columns: ["listing_id"]
            isOneToOne: false
            referencedRelation: "listings"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          bluesky: string | null
          company: string | null
          created_at: string
          email: string | null
          email_notifications: boolean | null
          facebook: string | null
          first_name: string | null
          id: string
          instagram: string | null
          last_name: string | null
          linkedin: string | null
          profile_photo: string | null
          title: string | null
          twitter: string | null
          updated_at: string
          user_id: string
          user_role: Database["public"]["Enums"]["user_role"] | null
          website: string | null
        }
        Insert: {
          bluesky?: string | null
          company?: string | null
          created_at?: string
          email?: string | null
          email_notifications?: boolean | null
          facebook?: string | null
          first_name?: string | null
          id?: string
          instagram?: string | null
          last_name?: string | null
          linkedin?: string | null
          profile_photo?: string | null
          title?: string | null
          twitter?: string | null
          updated_at?: string
          user_id: string
          user_role?: Database["public"]["Enums"]["user_role"] | null
          website?: string | null
        }
        Update: {
          bluesky?: string | null
          company?: string | null
          created_at?: string
          email?: string | null
          email_notifications?: boolean | null
          facebook?: string | null
          first_name?: string | null
          id?: string
          instagram?: string | null
          last_name?: string | null
          linkedin?: string | null
          profile_photo?: string | null
          title?: string | null
          twitter?: string | null
          updated_at?: string
          user_id?: string
          user_role?: Database["public"]["Enums"]["user_role"] | null
          website?: string | null
        }
        Relationships: []
      }
      saved_listings: {
        Row: {
          created_at: string
          id: string
          listing_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          listing_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          listing_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "saved_listings_listing_id_fkey"
            columns: ["listing_id"]
            isOneToOne: false
            referencedRelation: "listings"
            referencedColumns: ["id"]
          },
        ]
      }
      seller_interest_list: {
        Row: {
          active_customers: string | null
          annual_net_profit_ttm_max: number | null
          annual_net_profit_ttm_min: number | null
          annual_revenue_ttm_max: number | null
          annual_revenue_ttm_min: number | null
          business_name: string
          city: string | null
          created_at: string | null
          description: string | null
          description_alt: string | null
          email: string
          growth_rate: string | null
          id: string
          image: string | null
          image_alt: string | null
          industry_id: string | null
          last_month_profit_max: number | null
          last_month_profit_min: number | null
          last_month_revenue_max: number | null
          last_month_revenue_min: number | null
          legal_structure: string | null
          location: string | null
          price: number | null
          reason_for_selling: string | null
          recurring_revenue_max: number | null
          recurring_revenue_min: number | null
          state_id: string | null
          street_address: string | null
          sub_industry_id: string | null
          team_size: string | null
          updated_at: string | null
          valuation: string | null
          website: string | null
          year_established: string | null
          zipcode: string
        }
        Insert: {
          active_customers?: string | null
          annual_net_profit_ttm_max?: number | null
          annual_net_profit_ttm_min?: number | null
          annual_revenue_ttm_max?: number | null
          annual_revenue_ttm_min?: number | null
          business_name: string
          city?: string | null
          created_at?: string | null
          description?: string | null
          description_alt?: string | null
          email: string
          growth_rate?: string | null
          id?: string
          image?: string | null
          image_alt?: string | null
          industry_id?: string | null
          last_month_profit_max?: number | null
          last_month_profit_min?: number | null
          last_month_revenue_max?: number | null
          last_month_revenue_min?: number | null
          legal_structure?: string | null
          location?: string | null
          price?: number | null
          reason_for_selling?: string | null
          recurring_revenue_max?: number | null
          recurring_revenue_min?: number | null
          state_id?: string | null
          street_address?: string | null
          sub_industry_id?: string | null
          team_size?: string | null
          updated_at?: string | null
          valuation?: string | null
          website?: string | null
          year_established?: string | null
          zipcode: string
        }
        Update: {
          active_customers?: string | null
          annual_net_profit_ttm_max?: number | null
          annual_net_profit_ttm_min?: number | null
          annual_revenue_ttm_max?: number | null
          annual_revenue_ttm_min?: number | null
          business_name?: string
          city?: string | null
          created_at?: string | null
          description?: string | null
          description_alt?: string | null
          email?: string
          growth_rate?: string | null
          id?: string
          image?: string | null
          image_alt?: string | null
          industry_id?: string | null
          last_month_profit_max?: number | null
          last_month_profit_min?: number | null
          last_month_revenue_max?: number | null
          last_month_revenue_min?: number | null
          legal_structure?: string | null
          location?: string | null
          price?: number | null
          reason_for_selling?: string | null
          recurring_revenue_max?: number | null
          recurring_revenue_min?: number | null
          state_id?: string | null
          street_address?: string | null
          sub_industry_id?: string | null
          team_size?: string | null
          updated_at?: string | null
          valuation?: string | null
          website?: string | null
          year_established?: string | null
          zipcode?: string
        }
        Relationships: [
          {
            foreignKeyName: "seller_interest_list_industry_id_fkey"
            columns: ["industry_id"]
            isOneToOne: false
            referencedRelation: "industries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "seller_interest_list_state_id_fkey"
            columns: ["state_id"]
            isOneToOne: false
            referencedRelation: "states"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "seller_interest_list_sub_industry_id_fkey"
            columns: ["sub_industry_id"]
            isOneToOne: false
            referencedRelation: "sub_industries"
            referencedColumns: ["id"]
          },
        ]
      }
      states: {
        Row: {
          code: string
          id: string
          name: string
        }
        Insert: {
          code: string
          id?: string
          name: string
        }
        Update: {
          code?: string
          id?: string
          name?: string
        }
        Relationships: []
      }
      sub_industries: {
        Row: {
          id: string
          industry_id: string
          name: string
        }
        Insert: {
          id?: string
          industry_id: string
          name: string
        }
        Update: {
          id?: string
          industry_id?: string
          name?: string
        }
        Relationships: [
          {
            foreignKeyName: "sub_industries_industry_id_fkey"
            columns: ["industry_id"]
            isOneToOne: false
            referencedRelation: "industries"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      buyer_goal: "operate" | "invest" | "both" | "empty"
      capital_range:
      | "under_25k"
      | "25k_50k"
      | "50k_100k"
      | "100k_250k"
      | "250k_500k"
      | "500k_1m"
      | "over_1m"
      | "under_100k"
      | "1m_2m"
      | "2m_5m"
      | "over_5m"
      document_category:
      | "profit_loss"
      | "balance_sheet"
      | "tax_returns"
      | "other"
      file_visibility: "public" | "private"
      user_role: "seller" | "buyer" | "seller_buyer"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
  | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
    Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
    Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
  ? R
  : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
    DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] & DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
    Row: infer R
  }
  ? R
  : never
  : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
  | keyof DefaultSchema["Tables"]
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Insert: infer I
  }
  ? I
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
    Insert: infer I
  }
  ? I
  : never
  : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
  | keyof DefaultSchema["Tables"]
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Update: infer U
  }
  ? U
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
    Update: infer U
  }
  ? U
  : never
  : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
  | keyof DefaultSchema["Enums"]
  | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
  : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
  | keyof DefaultSchema["CompositeTypes"]
  | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
  : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never

export const Constants = {
  public: {
    Enums: {
      buyer_goal: ["operate", "invest", "both", "empty"],
      capital_range: [
        "under_25k",
        "25k_50k",
        "50k_100k",
        "100k_250k",
        "250k_500k",
        "500k_1m",
        "over_1m",
        "under_100k",
        "1m_2m",
        "2m_5m",
        "over_5m",
      ],
      document_category: [
        "profit_loss",
        "balance_sheet",
        "tax_returns",
        "other",
      ],
      file_visibility: ["public", "private"],
      user_role: ["seller", "buyer", "seller_buyer"],
    },
  },
} as const

// Export the user role type for easier usage
export type UserRole = Database["public"]["Enums"]["user_role"]

// Export profile type
export type Profile = Database["public"]["Tables"]["profiles"]["Row"]

