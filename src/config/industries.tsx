import {
    Building2, // Wholesale & Distributors
    Laptop, // Online & Technology
    Store, // Retail
    Heart, // Health Care & Fitness
    Truck, // Transportation & Storage
    HelpCircle, // Non-Classifiable Establishments
    Dog, // Pet Services
    Hammer, // Building & Construction
    Utensils, // Restaurants & Food
    Plane, // Travel
    Wallet, // Financial Services
    Scissors, // Beauty & Personal Care
    GraduationCap, // Education & Children
    Radio, // Communication & Media
    Wheat, // Agriculture
    Car, // Automotive & Boat
    Factory, // Manufacturing
    Wrench, // Service Businesses
    Music, // Entertainment & Recreation
    LucideIcon
} from 'lucide-react';

export type Industry =
    | 'wholesale_distributors'
    | 'online_technology'
    | 'retail'
    | 'healthcare_fitness'
    | 'transportation_storage'
    | 'non_classifiable'
    | 'pet_services'
    | 'building_construction'
    | 'restaurants_food'
    | 'travel'
    | 'financial_services'
    | 'beauty_personal_care'
    | 'education_children'
    | 'communication_media'
    | 'agriculture'
    | 'automotive_boat'
    | 'manufacturing'
    | 'service_businesses'
    | 'entertainment_recreation';

export interface IndustryConfig {
    value: Industry;
    label: string;
    icon: LucideIcon;
}

export const INDUSTRY_CONFIG: IndustryConfig[] = [
    { value: 'wholesale_distributors', label: 'Wholesale & Distributors', icon: Building2 },
    { value: 'online_technology', label: 'Online & Technology', icon: Laptop },
    { value: 'retail', label: 'Retail', icon: Store },
    { value: 'healthcare_fitness', label: 'Health Care & Fitness', icon: Heart },
    { value: 'transportation_storage', label: 'Transportation & Storage', icon: Truck },
    { value: 'non_classifiable', label: 'Non-Classifiable Establishments', icon: HelpCircle },
    { value: 'pet_services', label: 'Pet Services', icon: Dog },
    { value: 'building_construction', label: 'Building & Construction', icon: Hammer },
    { value: 'restaurants_food', label: 'Restaurants & Food', icon: Utensils },
    { value: 'travel', label: 'Travel', icon: Plane },
    { value: 'financial_services', label: 'Financial Services', icon: Wallet },
    { value: 'beauty_personal_care', label: 'Beauty & Personal Care', icon: Scissors },
    { value: 'education_children', label: 'Education & Children', icon: GraduationCap },
    { value: 'communication_media', label: 'Communication & Media', icon: Radio },
    { value: 'agriculture', label: 'Agriculture', icon: Wheat },
    { value: 'automotive_boat', label: 'Automotive & Boat', icon: Car },
    { value: 'manufacturing', label: 'Manufacturing', icon: Factory },
    { value: 'service_businesses', label: 'Service Businesses', icon: Wrench },
    { value: 'entertainment_recreation', label: 'Entertainment & Recreation', icon: Music }
];

export const IndustryIcon = ({
    industry,
    className = "",
    size = 16
}: {
    industry: Industry;
    className?: string;
    size?: number;
}) => {
    const config = INDUSTRY_CONFIG.find(i => i.value === industry);
    if (!config) return null;

    const Icon = config.icon;
    return <Icon className={className} size={size} />;
};

export function isValidIndustry(value: string): value is Industry {
    return INDUSTRY_CONFIG.map(i => i.value).includes(value as Industry);
} 