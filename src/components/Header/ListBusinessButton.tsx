'use client';

import { Plus } from 'lucide-react';
import { useListingFormModal } from '@/contexts/ListingFormModalContext';
import { User } from '@supabase/supabase-js';

interface ListBusinessButtonProps {
    user: User | null;
}

export default function ListBusinessButton({ user }: ListBusinessButtonProps) {
    const { openModal } = useListingFormModal();

    if (!user) return null;

    return (
        <button
            onClick={openModal}
            className="bg-neutral-800 flex items-center space-x-2 text-white px-4 py-2 rounded-md hover:bg-neutral-700"
        >
            <Plus className="w-5 h-5" />
            <span>List Your Business</span>
        </button>
    );
} 