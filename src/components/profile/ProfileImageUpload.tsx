'use client'

import { useState } from 'react'
import Image from 'next/image'
import { createClient } from '@/utils/supabase/client'
import { User } from '@supabase/supabase-js'

interface ProfileImageUploadProps {
    user: User
    profilePhotoUrl: string | null
    onImageUploaded: (url: string) => void
}

export default function ProfileImageUpload({ user, profilePhotoUrl, onImageUploaded }: ProfileImageUploadProps) {
    const [uploading, setUploading] = useState(false)
    const supabase = createClient()

    const uploadImage = async (event: React.ChangeEvent<HTMLInputElement>) => {
        try {
            setUploading(true)

            const { data: { session } } = await supabase.auth.getSession()
            console.log('Current session:', session)
            console.log('User ID from props:', user.id)
            console.log('Session user ID:', session?.user?.id)

            if (!event.target.files || event.target.files.length === 0) {
                throw new Error('You must select an image to upload.')
            }

            const file = event.target.files[0]
            const fileExt = file.name.split('.').pop()?.toLowerCase()

            // The path must start with profile-photos/ and then user.id
            // This exactly matches your policy requirements
            const filePath = `profile-photos/${user.id}-${Date.now()}.${fileExt}`

            console.log('Uploading to path:', filePath) // Add this to verify the path

            const { error: uploadError, data: uploadData } = await supabase.storage
                .from('profiles') // bucket name is 'profiles'
                .upload(filePath, file, {
                    upsert: false, // set to false to ensure we're not overwriting
                    duplex: 'half'  // Add this to ensure proper request handling
                })

            console.log('Upload response:', { uploadError, uploadData })

            if (uploadError) {
                console.error('Upload error details:', uploadError)
                throw new Error(`Upload failed: ${uploadError.message}`)
            }

            console.log('File uploaded successfully, getting public URL...')

            // Get public URL
            const { data: { publicUrl } } = supabase.storage
                .from('profiles')
                .getPublicUrl(filePath)

            console.log('Got public URL, updating profile...')

            // Update profile with new image URL
            const { error: updateError } = await supabase
                .from('profiles')
                .update({ profile_photo: publicUrl })
                .eq('user_id', user.id)
                .select()

            if (updateError) {
                console.error('Profile update error:', updateError)
                throw new Error(`Profile update failed: ${updateError.message}`)
            }

            console.log('Profile updated successfully')
            onImageUploaded(publicUrl)

        } catch (error) {
            console.error('Full error details:', error)
            alert(error instanceof Error ? error.message : 'An unexpected error occurred during upload')
        } finally {
            setUploading(false)
        }
    }

    return (
        <div className="flex flex-col items-center gap-4">
            <div className="relative w-32 h-32 rounded-full overflow-hidden border-2 border-gray-200 cursor-pointer group">
                {profilePhotoUrl ? (
                    <Image
                        src={profilePhotoUrl}
                        alt="Profile"
                        fill
                        className="object-cover"
                    />
                ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                        <span className="text-gray-400">No image</span>
                    </div>
                )}
                <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    <span className="text-white text-sm">Change Photo</span>
                </div>
                <input
                    type="file"
                    accept="image/*"
                    onChange={uploadImage}
                    disabled={uploading}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
            </div>
            {uploading && <p className="text-sm text-gray-500">Uploading...</p>}
        </div>
    )
}
