'use client';

import { useState, useEffect } from 'react';
import { Store, ShoppingCart, Building2, Check } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import type { UserRole } from '@/types/supabase';

interface UserRoleTabsProps {
    currentRole: UserRole;
    onRoleChange?: (role: UserRole) => void;
}

const roleOptions = [
    { value: 'seller' as const, label: 'Seller', icon: Store },
    { value: 'buyer' as const, label: 'Buyer', icon: ShoppingCart },
    { value: 'seller_buyer' as const, label: 'Seller+Buyer', icon: Building2 },
];

export default function UserRoleTabs({ currentRole, onRoleChange }: UserRoleTabsProps) {
    const [originalRole, setOriginalRole] = useState<UserRole>(currentRole);
    const [selectedRole, setSelectedRole] = useState<UserRole>(currentRole);
    const [isUpdating, setIsUpdating] = useState(false);
    const supabase = createClient();

    useEffect(() => {
        setOriginalRole(currentRole);
        setSelectedRole(currentRole);
    }, [currentRole]);

    const handleRoleSelection = (newRole: UserRole) => {
        if (isUpdating) return;
        setSelectedRole(newRole);
    };

    const updateUserRole = async (newRole: UserRole): Promise<boolean> => {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return false;

            const { error } = await supabase
                .from('profiles')
                .update({ user_role: newRole })
                .eq('user_id', user.id);

            if (error) {
                console.error('Error updating user role:', error);
                return false;
            }

            return true;
        } catch (error) {
            console.error('Error updating user role:', error);
            return false;
        }
    };

    const handleSaveRole = async () => {
        if (selectedRole === originalRole || isUpdating) return;

        setIsUpdating(true);

        try {
            const success = await updateUserRole(selectedRole);

            if (!success) {
                // Reset to original role on error
                setSelectedRole(originalRole);
                return;
            }

            // Update the original role to the new saved role
            setOriginalRole(selectedRole);
            // Call the optional callback
            onRoleChange?.(selectedRole);
        } catch (error) {
            console.error('Error updating user role:', error);
            // Reset to original role on error
            setSelectedRole(originalRole);
        } finally {
            setIsUpdating(false);
        }
    };

    const hasChanges = selectedRole !== originalRole;

    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6">
            <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900">User Role</h3>
                <p className="text-sm text-gray-600 mt-1">Choose how you want to use the platform</p>
            </div>

            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
                {roleOptions.map((role) => {
                    const Icon = role.icon;
                    const isSelected = selectedRole === role.value;

                    return (
                        <button
                            key={role.value}
                            onClick={() => handleRoleSelection(role.value)}
                            disabled={isUpdating}
                            className={`
                relative flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200
                ${isSelected
                                    ? 'bg-white text-blue-600 shadow-sm'
                                    : 'text-gray-600 hover:text-gray-900'
                                }
                ${isUpdating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              `}
                        >
                            <Icon className="w-4 h-4" />
                            <span className="whitespace-nowrap">{role.label}</span>
                        </button>
                    );
                })}
            </div>

            <div className="mt-3 text-xs text-gray-500">
                {selectedRole === 'seller' && 'You can list and sell businesses'}
                {selectedRole === 'buyer' && 'You can browse and buy businesses'}
                {selectedRole === 'seller_buyer' && 'You can both list businesses for sale and browse to buy'}
            </div>

            {/* Save Button - Only visible when there are changes */}
            {hasChanges && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                    <button
                        onClick={handleSaveRole}
                        disabled={isUpdating}
                        className={`
                            w-full flex items-center justify-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200
                            ${isUpdating
                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                : 'bg-neutral-800 text-white hover:bg-neutral-700 active:bg-neutral-800'
                            }
                        `}
                    >
                        {isUpdating ? (
                            <>
                                <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                                Saving...
                            </>
                        ) : (
                            <>
                                <Check className="w-4 h-4" />
                                Save Changes
                            </>
                        )}
                    </button>
                </div>
            )}
        </div>
    );
} 