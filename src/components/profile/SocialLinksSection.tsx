import { createClient } from '@/utils/supabase/server'
import { User } from '@supabase/supabase-js'
import { revalidatePath } from 'next/cache'
import { SubmitButton } from '@/components'

interface SocialLinksSectionProps {
    user: User
}

export default async function SocialLinksSection({ user }: SocialLinksSectionProps) {
    const supabase = await createClient()

    const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single()

    if (profileError) {
        return <div>Error loading social links</div>
    }

    async function updateSocialLinks(formData: FormData): Promise<void> {
        'use server'

        const twitter = formData.get('twitter') as string
        const facebook = formData.get('facebook') as string
        const linkedin = formData.get('linkedin') as string
        const instagram = formData.get('instagram') as string
        const bluesky = formData.get('bluesky') as string

        const supabase = await createClient()

        const { error } = await supabase
            .from('profiles')
            .update({
                twitter,
                facebook,
                linkedin,
                instagram,
                bluesky
            })
            .eq('user_id', user.id)

        if (error) {
            console.error('Error updating social links:', error)
            return
        }

        revalidatePath('/account')
    }

    return (
        <div className="p-6 bg-white rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-2xl font-bold mb-6">Social Links</h2>
            <form action={updateSocialLinks} className="space-y-6">
                <div className="space-y-2">
                    <label htmlFor="twitter" className="block text-sm font-medium text-gray-700">
                        Twitter
                    </label>
                    <input
                        type="url"
                        id="twitter"
                        name="twitter"
                        defaultValue={profileData.twitter || ''}
                        className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                </div>
                <div className="space-y-2">
                    <label htmlFor="facebook" className="block text-sm font-medium text-gray-700">
                        Facebook
                    </label>
                    <input
                        type="url"
                        id="facebook"
                        name="facebook"
                        defaultValue={profileData.facebook || ''}
                        className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                </div>
                <div className="space-y-2">
                    <label htmlFor="linkedin" className="block text-sm font-medium text-gray-700">
                        LinkedIn
                    </label>
                    <input
                        type="url"
                        id="linkedin"
                        name="linkedin"
                        defaultValue={profileData.linkedin || ''}
                        className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                </div>
                <div className="space-y-2">
                    <label htmlFor="instagram" className="block text-sm font-medium text-gray-700">
                        Instagram
                    </label>
                    <input
                        type="url"
                        id="instagram"
                        name="instagram"
                        defaultValue={profileData.instagram || ''}
                        className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                </div>
                <div className="space-y-2">
                    <label htmlFor="bluesky" className="block text-sm font-medium text-gray-700">
                        Bluesky
                    </label>
                    <input
                        type="url"
                        id="bluesky"
                        name="bluesky"
                        defaultValue={profileData.bluesky || ''}
                        className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                </div>
                <div className="pt-4">
                    <SubmitButton />
                </div>
            </form>
        </div>
    )
}
