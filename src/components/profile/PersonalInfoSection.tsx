import { createClient } from '@/utils/supabase/server'
import { User } from '@supabase/supabase-js'
import { revalidatePath } from 'next/cache'
import { SubmitButton } from '@/components'

interface PersonalInfoSectionProps {
    user: User
}

export default async function PersonalInfoSection({ user }: PersonalInfoSectionProps) {
    const supabase = await createClient()
    const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single()

    if (profileError) return <div>Error loading personal information</div>
    if (!profileData) return <div>Loading...</div>

    async function updatePersonalInfo(formData: FormData): Promise<void> {
        'use server'

        const first_name = formData.get('first_name') as string
        const last_name = formData.get('last_name') as string
        const email = formData.get('email') as string
        const title = formData.get('title') as string
        const company = formData.get('company') as string
        const website = formData.get('website') as string

        const supabase = await createClient()
        const { error } = await supabase
            .from('profiles')
            .update({
                first_name,
                last_name,
                email,
                title,
                company,
                website
            })
            .eq('user_id', user.id)

        if (error) {
            console.error('Error updating personal info:', error)
            return
        }

        revalidatePath('/account')
    }

    return (
        <div className="p-6 bg-white rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-2xl font-bold mb-6">Personal Information</h2>
            <form action={updatePersonalInfo} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                        <label htmlFor="first_name" className="block text-sm font-medium text-gray-700">
                            First Name
                        </label>
                        <input
                            type="text"
                            id="first_name"
                            name="first_name"
                            defaultValue={profileData.first_name || ''}
                            className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                        />
                    </div>
                    <div className="space-y-2">
                        <label htmlFor="last_name" className="block text-sm font-medium text-gray-700">
                            Last Name
                        </label>
                        <input
                            type="text"
                            id="last_name"
                            name="last_name"
                            defaultValue={profileData.last_name || ''}
                            className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                        />
                    </div>
                </div>
                <div className="space-y-2">
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                        Email
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        defaultValue={profileData.email || ''}
                        className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                </div>
                <div className="space-y-2">
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                        Title
                    </label>
                    <input
                        type="text"
                        id="title"
                        name="title"
                        defaultValue={profileData.title || ''}
                        className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                </div>
                <div className="space-y-2">
                    <label htmlFor="company" className="block text-sm font-medium text-gray-700">
                        Company
                    </label>
                    <input
                        type="text"
                        id="company"
                        name="company"
                        defaultValue={profileData.company || ''}
                        className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                </div>
                <div className="space-y-2">
                    <label htmlFor="website" className="block text-sm font-medium text-gray-700">
                        Website
                    </label>
                    <input
                        type="url"
                        id="website"
                        name="website"
                        defaultValue={profileData.website || ''}
                        className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                </div>
                <div className="pt-4">
                    <SubmitButton />
                </div>
            </form>
        </div>
    )
}
