'use client'

import { useState } from 'react'
import { User } from '@supabase/supabase-js'
import { ProfileImageUpload } from '@/components'

interface AccountImageSectionProps {
    user: User
    initialProfilePhotoUrl: string | null
}

export default function AccountImageSection({ user, initialProfilePhotoUrl }: AccountImageSectionProps) {
    const [profilePhotoUrl, setProfilePhotoUrl] = useState(initialProfilePhotoUrl)

    const handleImageUploaded = (url: string) => {
        setProfilePhotoUrl(url)
    }

    return (
        <ProfileImageUpload
            user={user}
            profilePhotoUrl={profilePhotoUrl}
            onImageUploaded={handleImageUploaded}
        />
    )
}
