import Link from 'next/link';

export default function Footer() {
    const footerSections = {
        company: {
            title: "Company",
            links: [
                { name: "About us", href: "/about" },
                { name: "Careers", href: "/careers" },
                { name: "Insights", href: "/insights" },
                { name: "Partners", href: "/partners" },
                { name: "Contact", href: "/contact" },
            ],
        },
        products: {
            title: "Products",
            links: [
                { name: "Company Listings", href: "/listings" },
                { name: "Direct Purchase", href: "/direct-purchase" },
                { name: "Business Valuation", href: "/valuation" },
                { name: "BuySell & Marketplace", href: "/marketplace" },
            ],
        },
        developers: {
            title: "Resources",
            links: [
                { name: "Documentation", href: "/docs" },
                { name: "API Reference", href: "/api" },
                { name: "Guides", href: "/guides" },
            ],
        },
    };

    return (
        <footer className="bg-gray-100 border-t">
            <div className="max-w-7xl mx-auto px-4 py-12 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                    {/* Company Info */}
                    <div className="col-span-1">
                        <h3 className="text-lg font-bold mb-4">BuySell</h3>
                        <p className="text-gray-600 mb-4">
                            5th Floor, 123 Business Street<br />
                            London, SW1 1AA
                        </p>
                        <p className="text-gray-900 font-medium">+44 (0) 123 456 7890</p>
                        <Link href="/contact" className="mt-4 border border-gray-300 rounded px-4 py-2 text-sm hover:bg-gray-100 transition-colors inline-block">
                            Get in touch →
                        </Link>
                    </div>

                    {/* Navigation Sections */}
                    {Object.values(footerSections).map((section) => (
                        <div key={section.title} className="col-span-1">
                            <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4">
                                {section.title}
                            </h3>
                            <ul className="space-y-3">
                                {section.links.map((link) => (
                                    <li key={link.name}>
                                        <Link
                                            href={link.href}
                                            className="text-base text-gray-600 hover:text-gray-900"
                                        >
                                            {link.name}
                                        </Link>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}
                </div>

                {/* Bottom Section */}
                <div className="mt-12 pt-8 border-t border-gray-200">
                    <div className="flex flex-col md:flex-row justify-between items-center">
                        <div className="flex space-x-6 mb-4 md:mb-0">
                            <p className="text-sm text-gray-500">
                                © {new Date().getFullYear()} BuySell. All rights reserved.
                            </p>
                            <Link href="/privacy" className="text-sm text-gray-500 hover:text-gray-900">
                                Privacy Policy
                            </Link>
                            <Link href="/terms" className="text-sm text-gray-500 hover:text-gray-900">
                                Terms & Conditions
                            </Link>
                        </div>
                        <div className="flex space-x-6">
                            <Link href="https://linkedin.com/company/buysell" className="text-gray-400 hover:text-gray-600">
                                <span className="sr-only">LinkedIn</span>
                                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                                </svg>
                            </Link>
                            <Link href="https://twitter.com/buysell" className="text-gray-400 hover:text-gray-600">
                                <span className="sr-only">Twitter</span>
                                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                                </svg>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    );
}
