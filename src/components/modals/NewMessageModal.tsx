'use client';

import { useState } from 'react';
import { X } from 'lucide-react';
import { useSupabase } from '@/hooks/useSupabase';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

interface NewMessageModalProps {
    isOpen: boolean;
    onClose: () => void;
    recipientId: string;
    listingId: string;
    ownerName: {
        firstName: string;
        lastName: string;
    };
    ownerAvatar: string | null;
    listingName: string;
    conversationId?: string;
}

export default function NewMessageModal({
    isOpen,
    onClose,
    recipientId,
    listingId,
    ownerName,
    ownerAvatar,
    listingName,
    conversationId
}: NewMessageModalProps) {
    const [message, setMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const supabase = useSupabase();
    const router = useRouter();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!message.trim() || isLoading) return;

        setIsLoading(true);
        console.log('📝 Starting message submission...');

        try {
            const { data: { session } } = await supabase.auth.getSession();
            if (!session?.user?.id) {
                console.log('❌ No user session found');
                return;
            }

            console.log('👤 Sender ID:', session.user.id);
            console.log('👥 Recipient ID:', recipientId);

            let finalConversationId = conversationId;

            // If no conversation exists, create one with ordered participants
            if (!conversationId) {
                const participant1Id = session.user.id < recipientId ? session.user.id : recipientId;
                const participant2Id = session.user.id < recipientId ? recipientId : session.user.id;

                console.log('🔄 Creating new conversation...');
                const { data: conversationData, error: conversationError } = await supabase
                    .from('conversations')
                    .insert({
                        listing_id: listingId,
                        participant1_id: participant1Id,
                        participant2_id: participant2Id
                    })
                    .select()
                    .single();

                if (conversationError) {
                    console.log('⚠️ Conversation creation error:', conversationError);
                    if (conversationError.code === '23505') {
                        console.log('🔍 Fetching existing conversation...');
                        const { data: existingConversation } = await supabase
                            .from('conversations')
                            .select('id')
                            .eq('listing_id', listingId)
                            .eq('participant1_id', participant1Id)
                            .eq('participant2_id', participant2Id)
                            .single();

                        finalConversationId = existingConversation?.id;
                        console.log('✅ Found existing conversation:', finalConversationId);
                    } else {
                        throw conversationError;
                    }
                } else {
                    finalConversationId = conversationData.id;
                    console.log('✅ Created new conversation:', finalConversationId);
                }
            }

            const messageData = {
                content: message,
                sender_id: session.user.id,
                recipient_id: recipientId,
                listing_id: listingId,
                conversation_id: finalConversationId,
                read: false
            };

            console.log('📨 Sending message:', messageData);

            const { data: messageResult, error: messageError } = await supabase
                .from('messages')
                .insert(messageData)
                .select()
                .single();

            if (messageError) {
                console.error('❌ Message error:', messageError);
                throw messageError;
            }

            console.log('✅ Message sent successfully:', messageResult);

            // Trigger email notification (non-blocking)
            if (messageResult) {
                try {
                    await fetch('/api/send-message-email', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            record: messageResult
                        })
                    });
                    console.log('📧 Email notification triggered');
                } catch (emailError) {
                    console.log('📧 Email notification failed (non-critical):', emailError);
                }
            }

            setMessage('');
            onClose();
            router.refresh();
        } catch (error) {
            console.error('❌ Error in message submission:', error);
        } finally {
            setIsLoading(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg w-full max-w-md relative">
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
                >
                    <X className="h-5 w-5" />
                </button>

                <div className="p-6">
                    <h2 className="text-xl font-semibold mb-4">Send Message to Owner</h2>

                    {/* Owner Information Card */}
                    <div className="bg-gray-50 rounded-lg p-4 mb-6 border border-gray-100 shadow-sm">
                        <div className="flex items-center gap-4">
                            <div className="relative h-12 w-12 rounded-full overflow-hidden">
                                {ownerAvatar ? (
                                    <Image
                                        src={ownerAvatar}
                                        alt={`${ownerName.firstName} ${ownerName.lastName}`}
                                        fill
                                        className="object-cover"
                                    />
                                ) : (
                                    <div className="w-full h-full bg-neutral-200 flex items-center justify-center">
                                        <span className="text-neutral-500 text-lg">
                                            {ownerName.firstName?.[0]}
                                            {ownerName.lastName?.[0]}
                                        </span>
                                    </div>
                                )}
                            </div>
                            <div>
                                <h3 className="font-medium text-gray-900">
                                    {ownerName.firstName} {ownerName.lastName}
                                </h3>
                                <p className="text-sm text-gray-500">
                                    Owner of <span className="font-medium">{listingName}</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <textarea
                                value={message}
                                onChange={(e) => setMessage(e.target.value)}
                                required
                                rows={4}
                                className="w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-neutral-500"
                                placeholder="Write your message..."
                            />
                        </div>

                        <div className="flex justify-end space-x-3">
                            <button
                                type="button"
                                onClick={onClose}
                                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                disabled={isLoading}
                                className="px-4 py-2 text-sm font-medium text-white bg-neutral-800 rounded-md hover:bg-neutral-700 disabled:opacity-50"
                            >
                                {isLoading ? 'Sending...' : 'Send Message'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
}