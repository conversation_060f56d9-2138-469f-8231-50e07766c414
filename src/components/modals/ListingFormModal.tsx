'use client';

import { useListingFormModal } from '@/contexts/ListingFormModalContext';
import { usePreventScroll } from '@/hooks/usePreventScroll';
import { X } from 'lucide-react';
import dynamic from 'next/dynamic';

// Dynamically import the AddListingForm to avoid loading it on the server
const AddListingForm = dynamic(
    () => import('@/app/listings/add-listing/components/AddListingForm'),
    { ssr: false }
);

export default function ListingFormModal() {
    const { isOpen, closeModal } = useListingFormModal();
    // Only prevent scroll when modal is open
    usePreventScroll(isOpen);  // Pass isOpen as the parameter

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-gray-50 rounded-lg w-full max-w-4xl my-6 mx-auto relative">
                <button
                    onClick={closeModal}
                    className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 z-10"
                >
                    <X className="h-5 w-5" />
                </button>

                <div className="max-h-[90vh] overflow-y-auto p-6">
                    <h2 className="text-xl font-semibold mb-4">List Your Business</h2>
                    <AddListingForm onSuccess={closeModal} />
                </div>
            </div>
        </div>
    );
} 