'use client'

import { signup } from '@/app/signup/actions'
import Input from '@/components/ui/Input'
import UserRoleSelection from '@/components/signup/UserRoleSelection'
import { useState } from 'react'
import Link from 'next/link'
import { Loader2, CheckCircle2, Mail } from 'lucide-react'
import type { UserRole } from '@/types/supabase'

export default function SignupForm() {
    const [error, setError] = useState<string | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [emailSent, setEmailSent] = useState(false)
    const [userEmail, setUserEmail] = useState('')
    const [selectedRole, setSelectedRole] = useState<UserRole>('seller_buyer')

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        if (isLoading || emailSent) return // Prevent multiple submissions

        setIsLoading(true)
        setError(null)

        const formData = new FormData(e.currentTarget)
        const email = formData.get('email') as string
        setUserEmail(email)

        // Add the selected role to the form data
        formData.set('user_role', selectedRole)

        const result = await signup(formData)

        setIsLoading(false)

        if (result.error) {
            setError(result.error)
        } else if (result.success) {
            setEmailSent(true)
        }
    }

    // Show success state after email is sent
    if (emailSent) {
        return (
            <div className="w-full max-w-md">
                <div className="bg-white border border-gray-100 p-8 rounded-lg shadow-md w-full text-center">
                    <div className="mb-6">
                        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <CheckCircle2 className="w-8 h-8 text-green-600" />
                        </div>
                        <h2 className="text-2xl font-bold mb-2 text-gray-900">Check Your Email</h2>
                        <p className="text-gray-600 mb-4">
                            We&apos;ve sent a confirmation email to:
                        </p>
                        <p className="font-medium text-gray-900 mb-4">{userEmail}</p>
                        <p className="text-sm text-gray-500">
                            Click the link in the email to verify your account and complete your signup.
                        </p>
                    </div>

                    <div className="space-y-3">
                        <div className="flex items-center justify-center text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
                            <Mail className="w-4 h-4 mr-2" />
                            Didn&apos;t receive it? Check your spam folder
                        </div>

                        <button
                            onClick={() => {
                                setEmailSent(false)
                                setUserEmail('')
                                setError(null)
                            }}
                            className="w-full text-neutral-600 hover:text-neutral-700 text-sm font-medium underline transition-colors"
                        >
                            Try with a different email
                        </button>
                    </div>
                </div>

                <div className="text-center mt-4 text-sm text-gray-600">
                    Already have an account?{' '}
                    <Link href="/login" className="text-neutral-600 hover:text-neutral-700 font-medium underline">
                        Log in here
                    </Link>
                </div>
            </div>
        )
    }

    return (
        <div className="w-full max-w-lg space-y-6">
            <form onSubmit={handleSubmit} className="bg-white border border-gray-100 p-8 rounded-lg shadow-md w-full">
                <h2 className="text-2xl font-bold mb-6 text-center">Create Account</h2>

                {error && (
                    <div className="mb-4 p-4 bg-red-50 text-red-500 rounded">
                        {error}
                    </div>
                )}

                <div className="space-y-4">
                    <Input
                        id="email"
                        name="email"
                        type="email"
                        label="Email"
                        required
                        placeholder="Enter your email"
                        disabled={isLoading}
                    />
                    <Input
                        id="password"
                        name="password"
                        type="password"
                        label="Password"
                        required
                        placeholder="Create a password"
                        disabled={isLoading}
                    />
                    <Input
                        id="password_confirmation"
                        name="password_confirmation"
                        type="password"
                        label="Confirm Password"
                        required
                        placeholder="Confirm your password"
                        disabled={isLoading}
                    />

                    <button
                        type="submit"
                        disabled={isLoading}
                        className="w-full bg-neutral-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-neutral-700 transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                        {isLoading ? (
                            <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Creating Account...
                            </>
                        ) : (
                            'Sign up'
                        )}
                    </button>
                </div>
            </form>

            {/* User Role Selection Card */}
            <UserRoleSelection
                selectedRole={selectedRole}
                onRoleChange={setSelectedRole}
            />

            <div className="text-center text-sm text-gray-600">
                Already have an account?{' '}
                <Link href="/login" className="text-neutral-600 hover:text-neutral-700 font-medium underline">
                    Log in here
                </Link>
            </div>
        </div>
    )
} 