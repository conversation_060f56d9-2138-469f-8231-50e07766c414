'use client'

import { login } from '@/app/login/actions'
import Input from '@/components/ui/Input'
import { useState } from 'react'
import Link from 'next/link'

export default function LoginForm() {
    const [isPending, setIsPending] = useState(false)
    const [error, setError] = useState<string | null>(null)

    return (
        <div className="w-full max-w-md">
            <form
                action={async (formData: FormData) => {
                    setIsPending(true)
                    setError(null)
                    try {
                        const result = await login(formData)
                        if (result?.error) {
                            setError(result.error)
                        }
                        // If no error, the server action will redirect
                    } catch (error) {
                        // Check if this is a Next.js redirect error - don't show these as errors
                        if (error && typeof error === 'object' && 'digest' in error &&
                            typeof error.digest === 'string' && error.digest.includes('NEXT_REDIRECT')) {
                            // This is a redirect, don't treat it as an error
                            return
                        }
                        console.error('Form submission error:', error)
                        setError('An unexpected error occurred')
                    } finally {
                        setIsPending(false)
                    }
                }}
                className="bg-white border border-gray-100 p-8 rounded-lg shadow-md w-full"
            >
                {error && (
                    <div className="mb-4 p-4 bg-red-50 text-red-500 rounded">
                        {error}
                    </div>
                )}
                <div className="space-y-4">
                    <Input
                        id="email"
                        name="email"
                        type="email"
                        label="Email"
                        required
                        placeholder="Enter your email"
                        disabled={isPending}
                    />
                    <Input
                        id="password"
                        name="password"
                        type="password"
                        label="Password"
                        required
                        placeholder="Enter your password"
                        disabled={isPending}
                    />

                    <div className="flex items-center justify-end">
                        <Link
                            href="/forgot-password"
                            className="text-sm text-neutral-600 hover:text-neutral-700 font-medium underline"
                        >
                            Forgot your password?
                        </Link>
                    </div>

                    <button
                        type="submit"
                        disabled={isPending}
                        className="w-full bg-neutral-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-neutral-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isPending ? 'Logging in...' : 'Log in'}
                    </button>
                </div>
            </form>
            <div className="text-center mt-4 text-sm text-gray-600">
                Don&apos;t have an account yet?{' '}
                <Link href="/signup" className="text-neutral-600 hover:text-neutral-700 font-medium underline">
                    Sign up here
                </Link>
            </div>
        </div>
    )
} 