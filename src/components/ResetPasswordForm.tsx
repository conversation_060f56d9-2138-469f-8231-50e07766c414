'use client'

import { resetPassword } from '@/app/reset-password/actions'
import Input from '@/components/ui/Input'
import { useState } from 'react'
import Link from 'next/link'

export default function ResetPasswordForm() {
    const [error, setError] = useState<string | null>(null)
    const [isLoading, setIsLoading] = useState(false)

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        setIsLoading(true)
        setError(null)

        try {
            const result = await resetPassword(new FormData(e.currentTarget))

            if (result?.error) {
                setError(result.error)
            }
            // If successful, the action will redirect automatically
        } catch {
            setError('An unexpected error occurred. Please try again.')
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <div className="w-full max-w-md">
            <form onSubmit={handleSubmit} className="bg-white border border-gray-100 p-8 rounded-lg shadow-md w-full">
                <div className="mb-6 text-center">
                    <h2 className="text-2xl font-bold mb-2 text-gray-900">Reset your password</h2>
                    <p className="text-gray-600">
                        Enter your new password below.
                    </p>
                </div>

                {error && (
                    <div className="mb-4 p-4 bg-red-50 text-red-500 rounded">
                        {error}
                    </div>
                )}

                <div className="space-y-4">
                    <Input
                        id="password"
                        name="password"
                        type="password"
                        label="New Password"
                        required
                        placeholder="Enter your new password"
                        disabled={isLoading}
                        minLength={8}
                    />

                    <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type="password"
                        label="Confirm New Password"
                        required
                        placeholder="Confirm your new password"
                        disabled={isLoading}
                        minLength={8}
                    />

                    <div className="text-sm text-gray-500">
                        <p>Password requirements:</p>
                        <ul className="list-disc list-inside mt-1 space-y-1">
                            <li>At least 8 characters long</li>
                            <li>Must match confirmation password</li>
                        </ul>
                    </div>

                    <button
                        type="submit"
                        disabled={isLoading}
                        className="w-full bg-neutral-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-neutral-700 transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isLoading ? 'Updating Password...' : 'Update Password'}
                    </button>
                </div>
            </form>

            <div className="text-center mt-4 text-sm text-gray-600">
                Remember your password?{' '}
                <Link href="/login" className="text-neutral-600 hover:text-neutral-700 font-medium underline">
                    Back to Login
                </Link>
            </div>
        </div>
    )
} 