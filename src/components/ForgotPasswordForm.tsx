'use client'

import { forgotPassword } from '@/app/forgot-password/actions'
import Input from '@/components/ui/Input'
import { useState } from 'react'
import { Mail, CheckCircle } from 'lucide-react'

export default function ForgotPasswordForm() {
    const [error, setError] = useState<string | null>(null)
    const [success, setSuccess] = useState(false)
    const [isLoading, setIsLoading] = useState(false)

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        setIsLoading(true)
        setError(null)

        const result = await forgotPassword(new FormData(e.currentTarget))

        if (result.error) {
            setError(result.error)
        } else if (result.success) {
            setSuccess(true)
        }

        setIsLoading(false)
    }

    if (success) {
        return (
            <div className="max-w-md mx-auto">
                <div className="text-center">
                    <div className="mx-auto flex items-center justify-center w-12 h-12 rounded-full bg-green-100 mb-4">
                        <CheckCircle className="w-6 h-6 text-green-600" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Check Your Email</h2>
                    <p className="text-gray-600 mb-6">
                        We&apos;ve sent a password reset link to your email address.
                        Please check your inbox and click the link to reset your password.
                    </p>
                    <p className="text-sm text-gray-500">
                        Didn&apos;t receive the email? Check your spam folder or try again.
                    </p>
                </div>
            </div>
        )
    }

    return (
        <div className="max-w-md mx-auto">
            <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-blue-50 rounded-lg">
                    <Mail className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                    <h2 className="text-2xl font-bold text-gray-900">Forgot Password</h2>
                    <p className="text-gray-600">
                        Enter your email to receive a password reset link
                    </p>
                </div>
            </div>

            {error && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-700 text-sm">{error}</p>
                </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
                <Input
                    id="email"
                    name="email"
                    type="email"
                    label="Email Address"
                    required
                    placeholder="Enter your email address"
                    disabled={isLoading}
                />

                <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-neutral-800 text-white py-3 px-4 rounded-lg font-medium hover:bg-neutral-700 hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    {isLoading ? 'Sending Reset Link...' : 'Send Reset Link'}
                </button>
            </form>
        </div>
    )
} 