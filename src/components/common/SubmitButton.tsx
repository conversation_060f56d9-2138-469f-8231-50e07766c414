'use client'

import { useFormStatus } from 'react-dom'
import { useState, useEffect } from 'react'

export function SubmitButton() {
    const { pending } = useFormStatus()
    const [showSaved, setShowSaved] = useState(false)
    const [hasSubmitted, setHasSubmitted] = useState(false)

    useEffect(() => {
        if (pending) {
            setShowSaved(false)
        }
    }, [pending])

    useEffect(() => {
        if (!pending && hasSubmitted) {
            setShowSaved(true)
            const timer = setTimeout(() => {
                setShowSaved(false)
                setHasSubmitted(false)
            }, 2000)
            return () => clearTimeout(timer)
        }
    }, [pending, hasSubmitted])

    useEffect(() => {
        if (pending && !hasSubmitted) {
            setHasSubmitted(true)
        }
    }, [pending, hasSubmitted])

    const buttonText = pending ? 'Saving...' : showSaved ? 'Saved!' : 'Save'

    return (
        <div className="w-full p-[2px] bg-gradient-to-b from-white/20 to-white/0 rounded-lg">
            <button
                type="submit"
                disabled={pending || showSaved}
                className="w-full h-full flex justify-center items-center px-4 py-2 rounded-lg
                    bg-[#1A1A1A] text-white text-sm font-medium
                    shadow-[0_0_0_1px_#1A1A1A,0_4px_6px_-1px_rgba(0,0,0,0.2),0_2px_4px_-1px_rgba(0,0,0,0.1)]
                    hover:bg-neutral-800 transition-colors
                    focus:outline-none focus:ring-2 focus:ring-neutral-500
                    disabled:opacity-50"
            >
                {buttonText}
            </button>
        </div>
    )
} 