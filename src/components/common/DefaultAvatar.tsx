import Image from 'next/image'

interface DefaultAvatarProps {
    src?: string | null
    alt: string
    size?: number
    className?: string
}

export default function DefaultAvatar({ src, alt, size = 40, className = '' }: DefaultAvatarProps) {
    if (src) {
        return (
            <Image
                src={src}
                alt={alt}
                width={size}
                height={size}
                className={`rounded-full ${className}`}
            />
        )
    }

    return (
        <div
            className={`bg-gray-200 rounded-full flex items-center justify-center ${className}`}
            style={{ width: size, height: size }}
        >
            <span className="text-gray-500 text-sm font-medium">
                {alt.charAt(0).toUpperCase()}
            </span>
        </div>
    )
} 