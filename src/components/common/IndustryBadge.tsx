import { Industry, INDUSTRY_CONFIG, IndustryIcon } from '@/config/industries';

interface IndustryBadgeProps {
    industry: Industry;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}

export function IndustryBadge({
    industry,
    size = 'md',
    className = ''
}: IndustryBadgeProps) {
    const sizeClasses = {
        sm: 'text-xs px-2 py-1',
        md: 'text-sm px-3 py-1.5',
        lg: 'text-base px-4 py-2'
    };

    const iconSizes = {
        sm: 14,
        md: 16,
        lg: 18
    };

    return (
        <div className={`
            inline-flex items-center gap-2 
            rounded-full bg-gray-100 
            ${sizeClasses[size]}
            ${className}
        `}>
            <IndustryIcon
                industry={industry}
                size={iconSizes[size]}
                className="text-gray-600"
            />
            <span className="font-medium text-gray-700">
                {INDUSTRY_CONFIG.find(i => i.value === industry)?.label}
            </span>
        </div>
    );
} 