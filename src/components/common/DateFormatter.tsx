'use client';

interface DateFormatterProps {
    date: string;
    className?: string;
}

export function DateFormatter({ date, className }: DateFormatterProps) {
    // Ensure consistent formatting by using a fixed format
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    return (
        <span className={className}>
            {formatDate(date)}
        </span>
    );
} 