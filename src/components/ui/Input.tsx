import { InputHTMLAttributes, forwardRef } from 'react'

interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
    label?: string
}

const Input = forwardRef<HTMLInputElement, InputProps>(
    ({ label, id, ...props }, ref) => {
        return (
            <div className="flex flex-col gap-2 w-full">
                {label && (
                    <label htmlFor={id} className="text-sm font-medium text-foreground">
                        {label}
                    </label>
                )}
                <input
                    ref={ref}
                    id={id}
                    className="w-full px-3 py-2 rounded-md border border-moss-100 border-input bg-muted/50 
          focus:outline-none focus:ring-2 focus:ring-moss-600 focus:border-transparent
          placeholder:text-muted-foreground"
                    {...props}
                />
            </div>
        )
    }
)

Input.displayName = 'Input'

export default Input
