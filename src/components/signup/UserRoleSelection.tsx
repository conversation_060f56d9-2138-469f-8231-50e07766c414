'use client';

import { Store, ShoppingCart, Building2 } from 'lucide-react';
import type { UserRole } from '@/types/supabase';

interface UserRoleSelectionProps {
    selectedRole: UserRole;
    onRoleChange: (role: UserRole) => void;
}

const roleOptions = [
    {
        value: 'seller' as const,
        label: 'Seller',
        icon: Store,
        description: 'I want to list and sell businesses'
    },
    {
        value: 'buyer' as const,
        label: 'Buyer',
        icon: ShoppingCart,
        description: 'I want to browse and buy businesses'
    },
    {
        value: 'seller_buyer' as const,
        label: 'Seller+Buyer',
        icon: Building2,
        description: 'I want to both sell and buy businesses'
    },
];

export default function UserRoleSelection({ selectedRole, onRoleChange }: UserRoleSelectionProps) {
    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6">
            <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900">How do you plan to use the platform?</h3>
                <p className="text-sm text-gray-600 mt-1">
                    Choose your primary role. You can always update this later in your account settings.
                </p>
            </div>

            <div className="space-y-3">
                {roleOptions.map((role) => {
                    const Icon = role.icon;
                    const isSelected = selectedRole === role.value;

                    return (
                        <button
                            key={role.value}
                            onClick={() => onRoleChange(role.value)}
                            className={`
                                w-full flex items-start gap-4 p-4 rounded-lg border-2 transition-all duration-200 text-left
                                ${isSelected
                                    ? 'border-blue-500 bg-blue-50'
                                    : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
                                }
                            `}
                        >
                            <div className={`
                                flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center
                                ${isSelected ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'}
                            `}>
                                <Icon className="w-5 h-5" />
                            </div>
                            <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                    <span className={`font-medium ${isSelected ? 'text-blue-900' : 'text-gray-900'}`}>
                                        {role.label}
                                    </span>
                                    {isSelected && (
                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    )}
                                </div>
                                <p className={`text-sm ${isSelected ? 'text-blue-700' : 'text-gray-600'}`}>
                                    {role.description}
                                </p>
                            </div>
                        </button>
                    );
                })}
            </div>

            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <p className="text-xs text-gray-600">
                    💡 <strong>Don&apos;t worry!</strong> You can change your role anytime in your account settings to match your evolving needs.
                </p>
            </div>
        </div>
    );
} 