'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import {
    Home,
    ListStart,
    Info,
    Mail,
    LogIn,
    Rocket,
    Menu,
    Plus,
    Search,
} from 'lucide-react'
import { LogoutButton } from '@/components'
import { useListingFormModal } from '@/contexts/ListingFormModalContext'
import type { UserRole } from '@/types/supabase'

interface MobileMenuProps {
    user: {
        id: string;
        email?: string;
        // Add other user properties you actually use
    } | null;
    userRole: UserRole | null;
    initials: string;
    isListingsActive: boolean;
    profilePhotoUrl: string | null;
}

export default function MobileMenu({ user, userRole, initials, isListingsActive, profilePhotoUrl }: MobileMenuProps) {
    const [isOpen, setIsOpen] = useState(false)
    const { openModal } = useListingFormModal()
    const isMatchActive = typeof window !== 'undefined' && window.location.pathname.startsWith('/match')

    // Helper function to check if user can access buyer features
    const canAccessBuyerFeatures = userRole === 'buyer' || userRole === 'seller_buyer';

    return (
        <div className="relative">
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="text-muted-foreground hover:text-primary p-2"
            >
                <Menu className="h-6 w-6" />
            </button>

            {isOpen && (
                <div className="absolute top-16 right-0 w-screen bg-white shadow-lg z-50 -mr-4">
                    <div className="px-2 pt-2 pb-3 space-y-1">
                        <div className="px-2">
                            <Link href="/" className="flex items-center space-x-2 w-full px-3 py-2 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50">
                                <Home className="w-5 h-5" />
                                <span>Home</span>
                            </Link>
                        </div>
                        <div className="px-2">
                            <Link
                                href="/listings"
                                className={`flex items-center space-x-2 w-full px-3 py-2 text-base font-medium
                                    ${isListingsActive
                                        ? 'text-moss-500 hover:text-moss-600 bg-gray-50'
                                        : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                                    }`}
                            >
                                <ListStart className="w-5 h-5" />
                                <span>Listings</span>
                            </Link>
                        </div>
                        {/* Only show Match for buyers and seller+buyers */}
                        {(!user || canAccessBuyerFeatures) && (
                            <div className="px-2">
                                <Link
                                    href="/match"
                                    className={`flex items-center space-x-2 w-full px-3 py-2 text-base font-medium
                                        ${isMatchActive
                                            ? 'text-moss-500 hover:text-moss-600 bg-gray-50'
                                            : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                                        }`}
                                >
                                    <Search className="w-5 h-5" />
                                    <span>Match</span>
                                </Link>
                            </div>
                        )}

                        {/* Only show Deals Dashboard for authenticated users */}
                        {user && (
                            <div className="px-2">
                                <Link
                                    href="/deals-dashboard"
                                    className="flex items-center space-x-2 w-full px-3 py-2 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50"
                                >
                                    <ListStart className="w-5 h-5" />
                                    <span>Deals Dashboard</span>
                                </Link>
                            </div>
                        )}

                        <div className="px-2">
                            <Link href="/about" className="flex items-center space-x-2 w-full px-3 py-2 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50">
                                <Info className="w-5 h-5" />
                                <span>About</span>
                            </Link>
                        </div>
                        <div className="px-2">
                            <Link href="/contact" className="flex items-center space-x-2 w-full px-3 py-2 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50">
                                <Mail className="w-5 h-5" />
                                <span>Contact</span>
                            </Link>
                        </div>
                    </div>
                    <div className="pt-4 pb-3 border-t border-gray-200">
                        <div className="px-2 space-y-1">
                            {!user ? (
                                <>
                                    <div className="px-2">
                                        <Link
                                            href="/login"
                                            className="flex items-center space-x-2 w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50"
                                        >
                                            <LogIn className="w-5 h-5" />
                                            <span>Sign in</span>
                                        </Link>
                                    </div>
                                    <div className="px-2">
                                        <Link
                                            href="/signup"
                                            className="flex items-center space-x-2 w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50"
                                        >
                                            <Rocket className="w-5 h-5" />
                                            <span>Get started</span>
                                        </Link>
                                    </div>
                                </>
                            ) : (
                                <>
                                    <div className="px-2">
                                        <button
                                            onClick={() => {
                                                setIsOpen(false)
                                                openModal()
                                            }}
                                            className="flex items-center w-full px-4 py-2 text-sm text-left hover:bg-gray-100"
                                        >
                                            <Plus className="w-4 h-4 mr-3" />
                                            List Your Business
                                        </button>
                                    </div>
                                    <div className="px-2">
                                        <Link
                                            href="/account"
                                            className="flex items-center space-x-2 w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50"
                                        >
                                            {profilePhotoUrl ? (
                                                <div className="relative w-6 h-6 rounded-full overflow-hidden">
                                                    <Image
                                                        src={profilePhotoUrl}
                                                        alt="Profile"
                                                        fill
                                                        className="object-cover"
                                                    />
                                                </div>
                                            ) : (
                                                <div className="w-6 h-6 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center text-sm font-medium">
                                                    {initials}
                                                </div>
                                            )}
                                            <span>Account</span>
                                        </Link>
                                    </div>
                                    <div className="px-2">
                                        <LogoutButton>
                                            <div className="flex items-center space-x-2 w-full text-left px-3 py-2 text-base font-medium text-red-600 hover:text-red-700 hover:bg-gray-50">
                                                <LogIn className="w-5 h-5" />
                                                <span>Logout</span>
                                            </div>
                                        </LogoutButton>
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
} 