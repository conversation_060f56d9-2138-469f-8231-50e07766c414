'use client';

import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';

interface LogoutButtonProps {
    children: React.ReactNode;
}

export default function LogoutButton({ children }: LogoutButtonProps) {
    const router = useRouter();
    const supabase = createClient();

    const handleLogout = async () => {
        const { error } = await supabase.auth.signOut();
        if (!error) {
            router.push('/');
            router.refresh();
        }
    };

    return (
        <div onClick={handleLogout}>
            {children}
        </div>
    );
}
