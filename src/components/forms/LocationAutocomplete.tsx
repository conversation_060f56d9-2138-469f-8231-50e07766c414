'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { MapPin, X } from 'lucide-react'
import googleMapsLoader from '@/utils/googleMapsLoader'

interface LocationSuggestion {
    place_id: string
    description: string
    structured_formatting: {
        main_text: string
        secondary_text: string
    }
    terms: Array<{
        offset: number
        value: string
    }>
}

interface LocationDetails {
    city: string | null
    state: string | null
    stateCode: string | null
    county: string | null
    country: string | null
    postalCode: string | null
    formattedAddress: string
    latitude: number | null
    longitude: number | null
}

interface LocationAutocompleteProps {
    value: string
    onChange: (value: string, details?: LocationDetails) => void
    placeholder?: string
    className?: string
    disabled?: boolean
}

// Simple debounce utility
function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = useState<T>(value)

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value)
        }, delay)

        return () => {
            clearTimeout(handler)
        }
    }, [value, delay])

    return debouncedValue
}

export default function LocationAutocomplete({
    value,
    onChange,
    placeholder = "City, State, Country",
    className = "",
    disabled = false
}: LocationAutocompleteProps) {
    const [inputValue, setInputValue] = useState(value)
    const [suggestions, setSuggestions] = useState<LocationSuggestion[]>([])
    const [isOpen, setIsOpen] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const [isGoogleMapsReady, setIsGoogleMapsReady] = useState(false)
    const [selectedIndex, setSelectedIndex] = useState(-1)
    const inputRef = useRef<HTMLInputElement>(null)
    const containerRef = useRef<HTMLDivElement>(null)

    const debouncedInputValue = useDebounce(inputValue, 300)

    // Load Google Places API
    useEffect(() => {
        googleMapsLoader.load()
            .then(() => {
                setIsGoogleMapsReady(true)
            })
            .catch((error) => {
                console.error('Failed to load Google Maps:', error)
            })
    }, [])

    // Fetch suggestions when input changes
    useEffect(() => {
        if (!debouncedInputValue.trim() || debouncedInputValue.length < 3 || !isGoogleMapsReady) {
            setSuggestions([])
            setIsOpen(false)
            return
        }

        setIsLoading(true)

        const service = new window.google.maps.places.AutocompleteService()

        service.getPlacePredictions(
            {
                input: debouncedInputValue,
                types: ['(cities)'],
                componentRestrictions: { country: 'us' } // Restrict to US for now
            },
            (predictions, status) => {
                setIsLoading(false)

                if (status === window.google.maps.places.PlacesServiceStatus.OK && predictions) {
                    setSuggestions(predictions)
                    setIsOpen(true)
                    setSelectedIndex(-1)
                } else {
                    setSuggestions([])
                    setIsOpen(false)
                }
            }
        )
    }, [debouncedInputValue, isGoogleMapsReady])

    // Get place details when suggestion is selected
    const getPlaceDetails = useCallback((placeId: string, description: string) => {
        if (!isGoogleMapsReady) {
            onChange(description)
            return
        }

        const service = new window.google.maps.places.PlacesService(
            document.createElement('div')
        )

        service.getDetails(
            {
                placeId,
                fields: ['address_components', 'geometry', 'formatted_address']
            },
            (place, status) => {
                if (status === window.google.maps.places.PlacesServiceStatus.OK && place) {
                    const addressComponents = place.address_components || []

                    const getComponent = (type: string) => {
                        const component = addressComponents.find(comp =>
                            comp.types.includes(type)
                        )
                        return component?.long_name || null
                    }

                    const getShortComponent = (type: string) => {
                        const component = addressComponents.find(comp =>
                            comp.types.includes(type)
                        )
                        return component?.short_name || null
                    }

                    const details: LocationDetails = {
                        city: getComponent('locality') || getComponent('sublocality_level_1'),
                        state: getComponent('administrative_area_level_1'),
                        stateCode: getShortComponent('administrative_area_level_1'),
                        county: getComponent('administrative_area_level_2'),
                        country: getComponent('country'),
                        postalCode: getComponent('postal_code'),
                        formattedAddress: place.formatted_address || description,
                        latitude: place.geometry?.location?.lat() || null,
                        longitude: place.geometry?.location?.lng() || null
                    }

                    onChange(description, details)
                } else {
                    onChange(description)
                }
            }
        )
    }, [onChange, isGoogleMapsReady])

    const handleSelectSuggestion = (suggestion: LocationSuggestion) => {
        setInputValue(suggestion.description)
        setIsOpen(false)
        setSuggestions([])
        getPlaceDetails(suggestion.place_id, suggestion.description)
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value
        setInputValue(newValue)
        onChange(newValue)

        if (!newValue.trim()) {
            setIsOpen(false)
            setSuggestions([])
        }
    }

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (!isOpen || suggestions.length === 0) return

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault()
                setSelectedIndex(prev =>
                    prev < suggestions.length - 1 ? prev + 1 : prev
                )
                break
            case 'ArrowUp':
                e.preventDefault()
                setSelectedIndex(prev => prev > 0 ? prev - 1 : prev)
                break
            case 'Enter':
                e.preventDefault()
                if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
                    handleSelectSuggestion(suggestions[selectedIndex])
                }
                break
            case 'Escape':
                setIsOpen(false)
                setSelectedIndex(-1)
                inputRef.current?.blur()
                break
        }
    }

    const clearInput = () => {
        setInputValue('')
        onChange('')
        setIsOpen(false)
        setSuggestions([])
        inputRef.current?.focus()
    }

    // Close suggestions when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
                setIsOpen(false)
                setSelectedIndex(-1)
            }
        }

        document.addEventListener('mousedown', handleClickOutside)
        return () => document.removeEventListener('mousedown', handleClickOutside)
    }, [])

    // Update input value when prop value changes
    useEffect(() => {
        setInputValue(value)
    }, [value])

    return (
        <div ref={containerRef} className="relative">
            <div className="relative">
                <input
                    ref={inputRef}
                    type="text"
                    value={inputValue}
                    onChange={handleInputChange}
                    onKeyDown={handleKeyDown}
                    onFocus={() => {
                        if (suggestions.length > 0) {
                            setIsOpen(true)
                        }
                    }}
                    placeholder={placeholder}
                    disabled={disabled}
                    className={`${className} pr-10`}
                />

                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    {inputValue && (
                        <button
                            type="button"
                            onClick={clearInput}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <X size={16} />
                        </button>
                    )}
                    {!inputValue && (
                        <MapPin className="text-gray-400" size={16} />
                    )}
                </div>
            </div>

            {/* Suggestions dropdown */}
            {isOpen && (
                <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {isLoading && (
                        <div className="px-4 py-3 text-sm text-gray-500">
                            Searching for locations...
                        </div>
                    )}

                    {!isLoading && suggestions.length === 0 && debouncedInputValue.length >= 3 && (
                        <div className="px-4 py-3 text-sm text-gray-500">
                            No locations found
                        </div>
                    )}

                    {!isLoading && suggestions.map((suggestion, index) => (
                        <button
                            key={suggestion.place_id}
                            type="button"
                            onClick={() => handleSelectSuggestion(suggestion)}
                            className={`w-full px-4 py-3 text-left text-sm hover:bg-gray-50 focus:bg-gray-50 focus:outline-none ${index === selectedIndex ? 'bg-blue-50 text-blue-700' : 'text-gray-900'
                                }`}
                        >
                            <div className="flex items-center space-x-2">
                                <MapPin size={16} className="text-gray-400 flex-shrink-0" />
                                <div>
                                    <div className="font-medium">
                                        {suggestion.structured_formatting.main_text}
                                    </div>
                                    <div className="text-gray-500 text-xs">
                                        {suggestion.structured_formatting.secondary_text}
                                    </div>
                                </div>
                            </div>
                        </button>
                    ))}
                </div>
            )}
        </div>
    )
} 