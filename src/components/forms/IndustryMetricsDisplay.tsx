'use client';

import { motion } from 'framer-motion';
import { MetricsSummary } from '@/utils/industryMetricsMapping';

interface IndustryMetricsDisplayProps {
    metrics: MetricsSummary;
    industryName?: string;
    subIndustryName?: string;
}

export default function IndustryMetricsDisplay({
    metrics,
    industryName,
    subIndustryName
}: IndustryMetricsDisplayProps) {
    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(value);
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
            className="bg-white border border-neutral-200 rounded-xl p-6 shadow-sm"
        >
            <div className="space-y-4">
                <div>
                    <h3 className="text-sm font-semibold text-neutral-900">Industry Metrics</h3>
                    <p className="text-sm text-neutral-600 leading-relaxed">
                        To help you estimate your price, we&apos;ve compiled the following metrics based on{' '}
                        <span className="inline-flex items-center px-2 py-0.5 bg-neutral-100 text-neutral-700 rounded-full">
                            {metrics.reported_sales || 0} reported sales
                        </span>
                        {' '}in{' '}
                        <span className="inline-flex items-center px-2 py-0.5 bg-neutral-100 text-neutral-700 rounded-full">
                            {subIndustryName || "this industry"}
                            {industryName && ` (${industryName})`}
                        </span>
                    </p>
                </div>

                <hr className="border-neutral-200 my-6" />

                <motion.div
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                    variants={{
                        show: {
                            transition: {
                                staggerChildren: 0.1
                            }
                        }
                    }}
                    initial="hidden"
                    animate="show"
                >
                    <motion.div
                        variants={{
                            hidden: { opacity: 0, y: 20 },
                            show: { opacity: 1, y: 0 }
                        }}
                        className="space-y-2"
                    >
                        <p className="text-sm text-neutral-600">Median Sale Price</p>
                        <div className="inline-flex items-center px-3 py-1 bg-blue-50 text-blue-700 rounded-full">
                            {formatCurrency(metrics.medianPrice)}
                        </div>
                    </motion.div>

                    <motion.div
                        variants={{
                            hidden: { opacity: 0, y: 20 },
                            show: { opacity: 1, y: 0 }
                        }}
                        className="space-y-2"
                    >
                        <p className="text-sm text-neutral-600">Median Asking Price</p>
                        <div className="inline-flex items-center px-3 py-1 bg-green-50 text-green-700 rounded-full">
                            {formatCurrency(metrics.medianAskingPrice)}
                        </div>
                    </motion.div>

                    <motion.div
                        variants={{
                            hidden: { opacity: 0, y: 20 },
                            show: { opacity: 1, y: 0 }
                        }}
                        className="space-y-2"
                    >
                        <p className="text-sm text-neutral-600">Average Sales Ratio</p>
                        <div className="inline-flex items-center px-3 py-1 bg-purple-50 text-purple-700 rounded-full">
                            {metrics.averageSalesRatio}x
                        </div>
                    </motion.div>

                    <motion.div
                        variants={{
                            hidden: { opacity: 0, y: 20 },
                            show: { opacity: 1, y: 0 }
                        }}
                        className="space-y-2"
                    >
                        <p className="text-sm text-neutral-600">Revenue Multiple</p>
                        <div className="inline-flex items-center px-3 py-1 bg-orange-50 text-orange-700 rounded-full">
                            {metrics.medianRevenueMultiple}x
                        </div>
                    </motion.div>

                    <motion.div
                        variants={{
                            hidden: { opacity: 0, y: 20 },
                            show: { opacity: 1, y: 0 }
                        }}
                        className="space-y-2"
                    >
                        <p className="text-sm text-neutral-600">Cash Flow Multiple</p>
                        <div className="inline-flex items-center px-3 py-1 bg-pink-50 text-pink-700 rounded-full">
                            {metrics.medianCashflowMultiple}x
                        </div>
                    </motion.div>

                    <motion.div
                        variants={{
                            hidden: { opacity: 0, y: 20 },
                            show: { opacity: 1, y: 0 }
                        }}
                        className="space-y-2"
                    >
                        <p className="text-sm text-neutral-600">Days on Market</p>
                        <div className="inline-flex items-center px-3 py-1 bg-indigo-50 text-indigo-700 rounded-full">
                            {metrics.medianDaysOnMarket} days
                        </div>
                    </motion.div>
                </motion.div>
            </div>
        </motion.div>
    );
} 