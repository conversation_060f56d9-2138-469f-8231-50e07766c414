'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { motion, useMotionValue, useTransform, animate } from 'framer-motion';
import { MetricsSummary } from '@/utils/industryMetricsMapping';
import { InfoIcon, CheckCircle2 } from 'lucide-react';

interface PriceRangeSliderProps {
    metrics: MetricsSummary;
    onPriceChange: (price: number) => void;
    annualCashFlow?: string | null;
    verifiedDataUsed?: boolean;
    annualRevenue?: number | null;
}

// Add this new component for the animated counter
function AnimatedPrice({ value, format }: { value: number, format: (n: number) => string }) {
    const motionValue = useMotionValue(value);
    const roundedValue = useTransform(motionValue, (latest) => Math.round(latest));
    const displayValue = useTransform(roundedValue, (latest) => format(latest));

    useEffect(() => {
        if (motionValue.get() !== value) {
            const animation = animate(motionValue, value, {
                type: "spring",
                stiffness: 80,
                damping: 15
            });
            return animation.stop;
        }
    }, [motionValue, value]);

    useEffect(() => {
        motionValue.set(value);
    }, [value, motionValue]);

    return <motion.span>{displayValue}</motion.span>;
}

export default function PriceRangeSlider({
    metrics,
    onPriceChange,
    verifiedDataUsed = false,
    annualRevenue,
}: PriceRangeSliderProps) {
    const [sliderValue, setSliderValue] = useState(66.6);
    const [currentPrice, setCurrentPrice] = useState<number>(0);
    const [isManuallySet, setIsManuallySet] = useState(false);
    const isInitialized = useRef(false);
    const prevAnnualRevenue = useRef(annualRevenue);

    const formatCurrency = useCallback((value: number) => {
        if (isNaN(value) || !isFinite(value)) {
            return '$--';
        }
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(value);
    }, []);

    const calculatePriceFromSlider = useCallback((value: number) => {
        const basePrice = metrics.medianAskingPrice || 0;
        if (basePrice === 0) return 0;

        const averageSalesRatio = metrics.averageSalesRatio || 1;

        const safeAverageSalesRatio = Math.max(0.1, Math.min(averageSalesRatio, 2.0));

        let multiplier: number;
        if (value <= 33.3) {
            multiplier = safeAverageSalesRatio * (0.8 + (value / 33.3) * 0.2);
        } else if (value <= 66.6) {
            multiplier = safeAverageSalesRatio + (1 - safeAverageSalesRatio) * ((value - 33.3) / 33.3);
        } else {
            multiplier = 1 + (0.2 * ((value - 66.6) / 33.3));
        }

        multiplier = Math.max(0.5, Math.min(multiplier, 1.5));

        return Math.round(basePrice * multiplier);
    }, [metrics.medianAskingPrice, metrics.averageSalesRatio]);

    const findSliderValueForPrice = useCallback((targetPrice: number) => {
        const basePrice = metrics.medianAskingPrice || 0;
        if (basePrice <= 0 || targetPrice <= 0) return 66.6;

        let closestValue = 50;
        let minDifference = Number.MAX_VALUE;

        for (let testValue = 0; testValue <= 100; testValue += 0.5) {
            const testPrice = calculatePriceFromSlider(testValue);
            const difference = Math.abs(testPrice - targetPrice);

            if (difference < minDifference) {
                minDifference = difference;
                closestValue = testValue;
            }
            if (minDifference < 10) break;
        }
        return closestValue;
    }, [calculatePriceFromSlider, metrics.medianAskingPrice]);

    useEffect(() => {
        console.log("PriceRangeSlider: Initializing with metrics", metrics);
        const initialBasePrice = metrics.medianAskingPrice || 0;
        if (initialBasePrice > 0) {
            setCurrentPrice(initialBasePrice);
            setSliderValue(66.6);
        }
        requestAnimationFrame(() => {
            isInitialized.current = true;
        });
    }, [metrics]);

    useEffect(() => {
        console.log("PriceRangeSlider: Checking annualRevenue effect", { annualRevenue, isManuallySet, medianRevenueMultiple: metrics?.medianRevenueMultiple });
        if (
            !isManuallySet &&
            annualRevenue !== undefined &&
            annualRevenue !== null &&
            annualRevenue > 0 &&
            metrics?.medianRevenueMultiple &&
            metrics.medianRevenueMultiple > 0
        ) {
            const revenueBasedPrice = Math.round(annualRevenue * metrics.medianRevenueMultiple);
            console.log("PriceRangeSlider: Calculating price from annualRevenue", { annualRevenue, medianRevenueMultiple: metrics.medianRevenueMultiple, revenueBasedPrice });

            if (revenueBasedPrice !== currentPrice) {
                console.log("PriceRangeSlider: Updating price based on annualRevenue change.");
                setCurrentPrice(revenueBasedPrice);
                const newSliderValue = findSliderValueForPrice(revenueBasedPrice);
                setSliderValue(newSliderValue);

                if (isInitialized.current) {
                    onPriceChange(revenueBasedPrice);
                }
            }
        }
        prevAnnualRevenue.current = annualRevenue;
    }, [annualRevenue, metrics?.medianRevenueMultiple, isManuallySet, findSliderValueForPrice, onPriceChange, currentPrice]);

    const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = Number(e.target.value);
        console.log("PriceRangeSlider: Manual slider change detected", { value });
        setIsManuallySet(true);
        setSliderValue(value);
        const newPrice = calculatePriceFromSlider(value);
        setCurrentPrice(newPrice);
        if (isInitialized.current) {
            onPriceChange(newPrice);
        }
    };

    console.log("PriceRangeSlider rendering with state:", { sliderValue, currentPrice, isManuallySet, annualRevenue, metrics });

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white border border-neutral-200 rounded-xl p-6 shadow-sm mt-6"
        >
            <div className="space-y-4">
                <div>
                    <h3 className="text-sm font-semibold text-neutral-900">Price Range Calculator</h3>
                    <p className="text-sm text-neutral-600">
                        Adjust the slider to see different price points based on market data
                    </p>
                </div>

                <div className="space-y-6">
                    <div className="flex justify-between text-sm">
                        <span
                            title={`This price point represents a conservative valuation at ${(metrics.averageSalesRatio - ((100 - (metrics.averageSalesRatio * 100)) / 100)).toFixed(2)}x multiplier, calculated by reducing the market average multiplier (${metrics.averageSalesRatio?.toFixed(2)}x) by ${((100 - (metrics.averageSalesRatio * 100)) / 100).toFixed(2)}`}
                            className="cursor-help flex items-center gap-1 text-blue-700"
                        >
                            Conservative
                            <InfoIcon className="h-4 w-4" />
                        </span>
                        <span
                            title={`This is the average multiplier (${metrics.averageSalesRatio?.toFixed(2)}x) based on actual market sales data, indicating what similar businesses typically sell for relative to their asking price`}
                            className="cursor-help flex items-center gap-1 text-green-700"
                        >
                            Market Average
                            <InfoIcon className="h-4 w-4" />
                        </span>
                        <span
                            title={`This represents the median asking price at 1.00x multiplier without any adjustments`}
                            className="cursor-help flex items-center gap-1 text-amber-700"
                        >
                            Median Price
                            <InfoIcon className="h-4 w-4" />
                        </span>
                        <span
                            title={`This price point represents a premium valuation at ${(1 + ((100 - (metrics.averageSalesRatio * 100)) / 100)).toFixed(2)}x multiplier, calculated by increasing the market average multiplier (${metrics.averageSalesRatio?.toFixed(2)}x) by ${((100 - (metrics.averageSalesRatio * 100)) / 100).toFixed(2)}`}
                            className="cursor-help flex items-center gap-1 text-purple-700"
                        >
                            Premium
                            <InfoIcon className="h-4 w-4" />
                        </span>
                    </div>

                    <div className="relative mt-8">
                        <div className="absolute w-full h-6 flex justify-between items-center px-[2px]">
                            <div className="w-0.5 h-4 bg-blue-500"></div>
                            <div className="w-0.5 h-4 bg-green-500"></div>
                            <div className="w-0.5 h-4 bg-amber-500"></div>
                            <div className="w-0.5 h-4 bg-purple-500"></div>
                        </div>

                        <input
                            type="range"
                            min="0"
                            max="100"
                            value={sliderValue}
                            onChange={handleSliderChange}
                            className="w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer relative z-10"
                            style={{
                                background: `linear-gradient(to right, 
                                    #93c5fd 0%, #22c55e 33.3%,
                                    #22c55e 33.3%, #f59e0b 66.6%,
                                    #f59e0b 66.6%, #7e22ce 100%)`
                            }}
                        />

                        <style jsx>{`
                            input[type='range'] {
                                -webkit-appearance: none;
                                background: transparent;
                            }

                            input[type='range']::-webkit-slider-thumb {
                                -webkit-appearance: none;
                                height: 16px;
                                width: 16px;
                                border-radius: 50%;
                                background: #1f2937;
                                cursor: pointer;
                                margin-top: -7px;
                                position: relative;
                                z-index: 2;
                            }

                            input[type='range']::-webkit-slider-runnable-track {
                                height: 2px;
                                border-radius: 999px;
                            }

                            input[type='range']::-moz-range-thumb {
                                height: 16px;
                                width: 16px;
                                border-radius: 50%;
                                background: #1f2937;
                                cursor: pointer;
                                border: none;
                                position: relative;
                                z-index: 2;
                            }

                            input[type='range']::-moz-range-track {
                                height: 2px;
                                border-radius: 999px;
                            }
                        `}</style>
                    </div>

                    <div className="text-center">
                        <div className="text-2xl font-semibold text-neutral-900">
                            <AnimatedPrice value={currentPrice} format={formatCurrency} />
                        </div>
                        <div className="text-sm text-neutral-600 flex items-center justify-center">
                            Suggested asking price
                            {verifiedDataUsed && (
                                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <CheckCircle2 className="w-3 h-3 mr-1" />
                                    Based on verified data
                                </span>
                            )}
                        </div>
                        {verifiedDataUsed && annualRevenue && (
                            <div className="mt-2 text-sm text-green-600">
                                Calculated using verified annual revenue of {formatCurrency(annualRevenue)} and a revenue multiple of {metrics.medianRevenueMultiple?.toFixed(2)}x
                            </div>
                        )}
                        <div className="mt-4 text-sm text-neutral-500 italic">
                            These estimates are based on market data from similar businesses. For a more accurate valuation,
                            please ensure your revenue and cash flow details are entered correctly in the Financial Information step below.
                        </div>
                    </div>
                </div>
            </div>
        </motion.div>
    );
} 