'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';

// Define the revenue ranges with their display values and actual numeric values
const REVENUE_RANGES = [
    { display: '0-$50K', min: 0, max: 50000, value: 50000 },
    { display: '$50K-$100K', min: 50000, max: 100000, value: 100000 },
    { display: '$100K-$250K', min: 100000, max: 250000, value: 250000 },
    { display: '$250K-$500K', min: 250000, max: 500000, value: 500000 },
    { display: '$500K-$1M', min: 500000, max: 1000000, value: 1000000 },
    { display: '$1M-$2.5M', min: 1000000, max: 2500000, value: 2500000 },
    { display: '$2.5M-$5M', min: 2500000, max: 5000000, value: 5000000 },
    { display: '$5M+', min: 5000000, max: null, value: 5000000 }
];

interface RevenueRangeSliderProps {
    initialValue: {
        min: number;
        max: number;
    };
    onRevenueChange: (range: { min: number; max: number }) => void;
}

export default function RevenueRangeSlider({ onRevenueChange, initialValue }: RevenueRangeSliderProps) {
    // Find the initial step based on the initialValue
    const findInitialStep = useCallback((): number => {
        if (!initialValue) return 0;

        for (let i = 0; i < REVENUE_RANGES.length; i++) {
            const range = REVENUE_RANGES[i];
            if (range.max === null || initialValue.max <= range.max) {
                return i;
            }
        }
        return REVENUE_RANGES.length - 1; // Default to the highest range if no match
    }, [initialValue]);

    const [sliderValue, setSliderValue] = useState(findInitialStep());
    const [selectedRange, setSelectedRange] = useState(REVENUE_RANGES[sliderValue]);

    // Handle slider changes
    const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = Number(e.target.value);
        setSliderValue(value);
        const newRange = REVENUE_RANGES[value];
        setSelectedRange(newRange);
        onRevenueChange({ min: newRange.min, max: newRange.max || 0 });
    };

    // Update when initialValue changes
    useEffect(() => {
        const step = findInitialStep();
        setSliderValue(step);
        setSelectedRange(REVENUE_RANGES[step]);
    }, [initialValue, findInitialStep]);

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white border border-neutral-200 rounded-xl p-6 shadow-sm mt-6"
        >
            <div className="space-y-4">
                <div>
                    <h3 className="text-sm font-semibold text-neutral-900">Annual Revenue (TTM)</h3>
                    <p className="text-sm text-neutral-600">
                        Select your annual revenue range for the trailing twelve months
                    </p>
                </div>

                <div className="space-y-6">
                    <div className="flex justify-between text-xs">
                        {REVENUE_RANGES.map((range, index) => (
                            <span
                                key={index}
                                className={`cursor-pointer ${sliderValue === index ? 'font-semibold text-neutral-900' : 'text-neutral-500'}`}
                                onClick={() => {
                                    setSliderValue(index);
                                    setSelectedRange(range);
                                    onRevenueChange({ min: range.min, max: range.max || 0 });
                                }}
                            >
                                {index === 0 ? '0' :
                                    index === REVENUE_RANGES.length - 1 ? '5M+' :
                                        range.display.split('-')[0]}
                            </span>
                        ))}
                    </div>

                    <div className="relative mt-8">
                        {/* Marker lines container */}
                        <div className="absolute w-full h-6 flex justify-between items-center px-[2px]">
                            {REVENUE_RANGES.map((_, index) => (
                                <div key={index} className="w-0.5 h-4 bg-neutral-300"></div>
                            ))}
                        </div>

                        {/* The actual slider */}
                        <input
                            type="range"
                            min="0"
                            max={REVENUE_RANGES.length - 1}
                            step="1"
                            value={sliderValue}
                            onChange={handleSliderChange}
                            className="w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer relative z-10"
                        />

                        {/* Custom thumb and track styles */}
                        <style jsx>{`
              input[type='range'] {
                -webkit-appearance: none;
                background: transparent;
              }

              input[type='range']::-webkit-slider-thumb {
                -webkit-appearance: none;
                height: 16px;
                width: 16px;
                border-radius: 50%;
                background: #1f2937;
                cursor: pointer;
                margin-top: -7px;
                position: relative;
                z-index: 2;
              }

              input[type='range']::-webkit-slider-runnable-track {
                height: 2px;
                border-radius: 999px;
                background: linear-gradient(to right, #d1d5db, #1f2937);
              }

              input[type='range']::-moz-range-thumb {
                height: 16px;
                width: 16px;
                border-radius: 50%;
                background: #1f2937;
                cursor: pointer;
                border: none;
                position: relative;
                z-index: 2;
              }

              input[type='range']::-moz-range-track {
                height: 2px;
                border-radius: 999px;
                background: linear-gradient(to right, #d1d5db, #1f2937);
              }
            `}</style>
                    </div>

                    <div className="text-center">
                        <div className="text-2xl font-semibold text-neutral-900">
                            {selectedRange.display}
                        </div>
                        <div className="text-sm text-neutral-600">
                            Selected revenue range
                        </div>
                    </div>
                </div>
            </div>
        </motion.div>
    );
} 