import Link from 'next/link'
import { AlertCircle, Home, ArrowLeft } from 'lucide-react'
import { <PERSON><PERSON>, Footer } from '@/components'

export default function ErrorPage() {
    return (
        <>
            <Header />
            <main className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="w-full max-w-md">
                    <div className="bg-white border border-gray-100 p-8 rounded-lg shadow-md w-full text-center">
                        <div className="mb-6">
                            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                                <AlertCircle className="w-8 h-8 text-red-600" />
                            </div>
                            <h1 className="text-2xl font-bold mb-2 text-gray-900">Authentication Error</h1>
                            <p className="text-gray-600">
                                We encountered an issue with your authentication request.
                                This could be due to an expired or invalid link.
                            </p>
                        </div>

                        <div className="space-y-3">
                            <Link
                                href="/forgot-password"
                                className="block w-full bg-neutral-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-neutral-700 transition-colors text-center"
                            >
                                Request New Password Reset
                            </Link>
                            <Link
                                href="/login"
                                className="flex items-center justify-center w-full border border-gray-300 bg-white text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 hover:border-gray-400 transition-colors"
                            >
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Login
                            </Link>
                            <Link
                                href="/"
                                className="flex items-center justify-center w-full text-gray-600 hover:text-gray-800 px-4 py-2 text-sm font-medium transition-colors"
                            >
                                <Home className="w-4 h-4 mr-2" />
                                Go to Homepage
                            </Link>
                        </div>

                        <div className="mt-6 pt-6 border-t border-gray-200">
                            <p className="text-xs text-gray-500">
                                If you continue to experience issues, please contact support.
                            </p>
                        </div>
                    </div>
                </div>
            </main>
            <Footer />
        </>
    )
}