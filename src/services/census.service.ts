/**
 * Census Service
 * Handles all US Census Bureau API interactions
 */

// Types
interface CensusPopulationData {
    population: number;
    isPopulationMock: boolean;
}

interface CensusIncomeData {
    medianIncome: number;
    isMedianIncomeMock: boolean;
}

interface CensusUnemploymentData {
    unemploymentRate: number;
    isUnemploymentRateMock: boolean;
}

interface CensusBusinessData {
    numBusinesses: number;
    isNumBusinessesMock: boolean;
}

interface CensusAgeData {
    medianAge: number;
    isMedianAgeMock: boolean;
}

interface CensusEducationData {
    educationBachelorPlusPercent: number;
    isEducationBachelorPlusPercentMock: boolean;
}

interface CensusPopulationGrowthData {
    populationCAGR: number;
    isPopulationCAGRMock: boolean;
}

interface CountyFipsData {
    stateFips: string;
    countyFips: string;
}

export class CensusService {
    private static readonly API_BASE = 'https://api.census.gov/data';
    private static readonly TIMEOUT_MS = 8000;

    /**
     * Timeout wrapper for API calls
     */
    private static async withTimeout<T>(promise: Promise<T>): Promise<T> {
        return Promise.race([
            promise,
            new Promise<T>((_, reject) =>
                setTimeout(() => reject(new Error(`Census API timeout after ${this.TIMEOUT_MS}ms`)), this.TIMEOUT_MS)
            ),
        ]);
    }

    /**
     * Make a Census API call with error handling
     */
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private static async fetchCensusData(url: string): Promise<any[]> {
        const response = await this.withTimeout(fetch(url));

        if (!response.ok) {
            throw new Error(`Census API error: ${response.status} - ${await response.text()}`);
        }

        const data = await response.json();

        if (!data || !Array.isArray(data) || data.length < 2) {
            throw new Error('Invalid Census API response format');
        }

        return data;
    }

    /**
     * Get population data for a county
     */
    static async getPopulationData(countyFips: CountyFipsData, apiKey: string): Promise<CensusPopulationData> {
        if (!apiKey) {
            return {
                population: Math.floor(Math.random() * 700000) + 300000,
                isPopulationMock: true
            };
        }

        try {
            const url = `${this.API_BASE}/2022/acs/acs5?get=B01003_001E&for=county:${countyFips.countyFips}&in=state:${countyFips.stateFips}&key=${apiKey}`;
            const data = await this.fetchCensusData(url);

            const populationValue = parseInt(data[1][0], 10);

            if (isNaN(populationValue)) {
                throw new Error('Invalid population data from Census API');
            }

            return {
                population: populationValue,
                isPopulationMock: false
            };
        } catch (error) {
            console.error('Error fetching population data:', error);
            return {
                population: Math.floor(Math.random() * 700000) + 300000,
                isPopulationMock: true
            };
        }
    }

    /**
     * Get median income data for a county
     */
    static async getMedianIncomeData(countyFips: CountyFipsData, apiKey: string): Promise<CensusIncomeData> {
        if (!apiKey) {
            return {
                medianIncome: Math.floor(Math.random() * 50000) + 70000,
                isMedianIncomeMock: true
            };
        }

        try {
            const url = `${this.API_BASE}/2022/acs/acs5?get=B19013_001E&for=county:${countyFips.countyFips}&in=state:${countyFips.stateFips}&key=${apiKey}`;
            const data = await this.fetchCensusData(url);

            const incomeValue = parseInt(data[1][0], 10);

            if (isNaN(incomeValue)) {
                throw new Error('Invalid income data from Census API');
            }

            return {
                medianIncome: incomeValue,
                isMedianIncomeMock: false
            };
        } catch (error) {
            console.error('Error fetching median income data:', error);
            return {
                medianIncome: Math.floor(Math.random() * 50000) + 70000,
                isMedianIncomeMock: true
            };
        }
    }

    /**
     * Get unemployment data for a ZIP code (ZCTA)
     */
    static async getUnemploymentData(postalCode: string, apiKey: string): Promise<CensusUnemploymentData> {
        if (!apiKey || !postalCode) {
            return {
                unemploymentRate: parseFloat((Math.random() * 5 + 2).toFixed(1)),
                isUnemploymentRateMock: true
            };
        }

        try {
            const url = `${this.API_BASE}/2022/acs/acs5?get=B23025_005E,B23025_003E&for=zip%20code%20tabulation%20area:${postalCode}&key=${apiKey}`;
            const data = await this.fetchCensusData(url);

            const unemployedCount = parseInt(data[1][0], 10);
            const laborForceCount = parseInt(data[1][1], 10);

            if (isNaN(unemployedCount) || isNaN(laborForceCount)) {
                throw new Error('Invalid unemployment data from Census API');
            }

            if (laborForceCount === 0) {
                return {
                    unemploymentRate: 0,
                    isUnemploymentRateMock: false
                };
            }

            const unemploymentRate = parseFloat(((unemployedCount / laborForceCount) * 100).toFixed(1));

            return {
                unemploymentRate,
                isUnemploymentRateMock: false
            };
        } catch (error) {
            console.error('Error fetching unemployment data:', error);
            return {
                unemploymentRate: parseFloat((Math.random() * 5 + 2).toFixed(1)),
                isUnemploymentRateMock: true
            };
        }
    }

    /**
     * Get number of businesses data (CBP - County Business Patterns)
     */
    static async getBusinessData(countyFips: CountyFipsData, apiKey: string): Promise<CensusBusinessData> {
        if (!apiKey) {
            return {
                numBusinesses: Math.floor(Math.random() * 10000) + 5000,
                isNumBusinessesMock: true
            };
        }

        try {
            const url = `${this.API_BASE}/2021/cbp?get=ESTAB&for=county:${countyFips.countyFips}&in=state:${countyFips.stateFips}&key=${apiKey}`;
            const data = await this.fetchCensusData(url);

            const businessCount = parseInt(data[1][0], 10);

            if (isNaN(businessCount)) {
                throw new Error('Invalid business count data from Census API');
            }

            return {
                numBusinesses: businessCount,
                isNumBusinessesMock: false
            };
        } catch (error) {
            console.error('Error fetching business data:', error);
            return {
                numBusinesses: Math.floor(Math.random() * 10000) + 5000,
                isNumBusinessesMock: true
            };
        }
    }

    /**
     * Get median age data for a county
     */
    static async getMedianAgeData(countyFips: CountyFipsData, apiKey: string): Promise<CensusAgeData> {
        if (!apiKey) {
            return {
                medianAge: Math.floor(Math.random() * 10) + 35,
                isMedianAgeMock: true
            };
        }

        try {
            const url = `${this.API_BASE}/2022/acs/acs5?get=B01002_001E&for=county:${countyFips.countyFips}&in=state:${countyFips.stateFips}&key=${apiKey}`;
            const data = await this.fetchCensusData(url);

            const ageValue = parseFloat(data[1][0]);

            if (isNaN(ageValue)) {
                throw new Error('Invalid median age data from Census API');
            }

            return {
                medianAge: ageValue,
                isMedianAgeMock: false
            };
        } catch (error) {
            console.error('Error fetching median age data:', error);
            return {
                medianAge: Math.floor(Math.random() * 10) + 35,
                isMedianAgeMock: true
            };
        }
    }

    /**
     * Get education level data (Bachelor's degree or higher) for a county
     */
    static async getEducationData(countyFips: CountyFipsData, apiKey: string): Promise<CensusEducationData> {
        if (!apiKey) {
            return {
                educationBachelorPlusPercent: Math.floor(Math.random() * 30) + 20,
                isEducationBachelorPlusPercentMock: true
            };
        }

        try {
            const url = `${this.API_BASE}/2022/acs/acs5/profile?get=DP02_0068PE&for=county:${countyFips.countyFips}&in=state:${countyFips.stateFips}&key=${apiKey}`;
            const data = await this.fetchCensusData(url);

            const educationValue = parseFloat(data[1][0]);

            if (isNaN(educationValue)) {
                throw new Error('Invalid education data from Census API');
            }

            return {
                educationBachelorPlusPercent: educationValue,
                isEducationBachelorPlusPercentMock: false
            };
        } catch (error) {
            console.error('Error fetching education data:', error);
            return {
                educationBachelorPlusPercent: Math.floor(Math.random() * 30) + 20,
                isEducationBachelorPlusPercentMock: true
            };
        }
    }

    /**
     * Calculate population growth rate (CAGR) between two years
     */
    static async getPopulationGrowthData(countyFips: CountyFipsData, apiKey: string): Promise<CensusPopulationGrowthData> {
        if (!apiKey) {
            return {
                populationCAGR: parseFloat(((Math.random() * 4) - 1).toFixed(2)),
                isPopulationCAGRMock: true
            };
        }

        try {
            const currentYear = '2022';
            const previousYear = '2017';

            // Fetch both years in parallel
            const [currentData, previousData] = await Promise.all([
                this.fetchCensusData(`${this.API_BASE}/${currentYear}/acs/acs5?get=B01003_001E&for=county:${countyFips.countyFips}&in=state:${countyFips.stateFips}&key=${apiKey}`),
                this.fetchCensusData(`${this.API_BASE}/${previousYear}/acs/acs5?get=B01003_001E&for=county:${countyFips.countyFips}&in=state:${countyFips.stateFips}&key=${apiKey}`)
            ]);

            const currentPop = parseInt(currentData[1][0], 10);
            const previousPop = parseInt(previousData[1][0], 10);

            if (isNaN(currentPop) || isNaN(previousPop) || previousPop === 0) {
                throw new Error('Invalid population growth data from Census API');
            }

            const numYears = parseInt(currentYear) - parseInt(previousYear);
            const populationCAGR = (Math.pow(currentPop / previousPop, 1 / numYears) - 1) * 100;

            return {
                populationCAGR: parseFloat(populationCAGR.toFixed(2)),
                isPopulationCAGRMock: false
            };
        } catch (error) {
            console.error('Error fetching population growth data:', error);
            return {
                populationCAGR: parseFloat(((Math.random() * 4) - 1).toFixed(2)),
                isPopulationCAGRMock: true
            };
        }
    }

    /**
     * Get all census data for a location in parallel
     */
    static async getAllCensusData(
        countyFips: CountyFipsData | null,
        postalCode: string | null,
        apiKey: string
    ) {
        if (!countyFips) {
            // Return all mock data if no county FIPS
            return {
                population: Math.floor(Math.random() * 700000) + 300000,
                isPopulationMock: true,
                medianIncome: Math.floor(Math.random() * 50000) + 70000,
                isMedianIncomeMock: true,
                unemploymentRate: parseFloat((Math.random() * 5 + 2).toFixed(1)),
                isUnemploymentRateMock: true,
                numBusinesses: Math.floor(Math.random() * 10000) + 5000,
                isNumBusinessesMock: true,
                populationCAGR: parseFloat(((Math.random() * 4) - 1).toFixed(2)),
                isPopulationCAGRMock: true,
                medianAge: Math.floor(Math.random() * 10) + 35,
                isMedianAgeMock: true,
                educationBachelorPlusPercent: Math.floor(Math.random() * 30) + 20,
                isEducationBachelorPlusPercentMock: true
            };
        }

        // Fetch all data in parallel for better performance
        const [
            populationData,
            incomeData,
            unemploymentData,
            businessData,
            ageData,
            educationData,
            growthData
        ] = await Promise.allSettled([
            this.getPopulationData(countyFips, apiKey),
            this.getMedianIncomeData(countyFips, apiKey),
            this.getUnemploymentData(postalCode || '', apiKey),
            this.getBusinessData(countyFips, apiKey),
            this.getMedianAgeData(countyFips, apiKey),
            this.getEducationData(countyFips, apiKey),
            this.getPopulationGrowthData(countyFips, apiKey)
        ]);

        // Extract results with fallbacks
        const population = populationData.status === 'fulfilled' ? populationData.value : { population: Math.floor(Math.random() * 700000) + 300000, isPopulationMock: true };
        const income = incomeData.status === 'fulfilled' ? incomeData.value : { medianIncome: Math.floor(Math.random() * 50000) + 70000, isMedianIncomeMock: true };
        const unemployment = unemploymentData.status === 'fulfilled' ? unemploymentData.value : { unemploymentRate: parseFloat((Math.random() * 5 + 2).toFixed(1)), isUnemploymentRateMock: true };
        const business = businessData.status === 'fulfilled' ? businessData.value : { numBusinesses: Math.floor(Math.random() * 10000) + 5000, isNumBusinessesMock: true };
        const age = ageData.status === 'fulfilled' ? ageData.value : { medianAge: Math.floor(Math.random() * 10) + 35, isMedianAgeMock: true };
        const education = educationData.status === 'fulfilled' ? educationData.value : { educationBachelorPlusPercent: Math.floor(Math.random() * 30) + 20, isEducationBachelorPlusPercentMock: true };
        const growth = growthData.status === 'fulfilled' ? growthData.value : { populationCAGR: parseFloat(((Math.random() * 4) - 1).toFixed(2)), isPopulationCAGRMock: true };

        return {
            ...population,
            ...income,
            ...unemployment,
            ...business,
            ...age,
            ...education,
            ...growth
        };
    }
} 