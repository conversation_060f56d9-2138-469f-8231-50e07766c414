/**
 * Economic Census Service
 * Handles Economic Census and County Business Patterns API interactions
 * for similar companies and industry data
 */

// Types
interface SimilarCompaniesData {
    numEstablishments?: number | null;
    totalAnnualPayroll?: number | null;
    totalEmployment?: number | null;
    averageAnnualWage?: number | null;
    countyName?: string | null;
    naicsDescription?: string | null;
    numEstablishmentsPreviousYear?: number | null;
    establishmentGrowthRate?: number | null;
    totalSalesReceipts?: number | null;
    avgSalesPerEstablishment?: number | null;
    isSalesDataMock?: boolean;
}

interface CountyFipsData {
    stateFips: string;
    countyFips: string;
}

export class EconomicCensusService {
    private static readonly API_BASE = 'https://api.census.gov/data';
    private static readonly TIMEOUT_MS = 8000;
    private static readonly CURRENT_YEAR_CBP = '2021';
    private static readonly PREVIOUS_YEAR_CBP = '2016';
    private static readonly ECONOMIC_CENSUS_YEAR = '2017';

    /**
     * Timeout wrapper for API calls
     */
    private static async withTimeout<T>(promise: Promise<T>): Promise<T> {
        return Promise.race([
            promise,
            new Promise<T>((_, reject) =>
                setTimeout(() => reject(new Error(`Economic Census API timeout after ${this.TIMEOUT_MS}ms`)), this.TIMEOUT_MS)
            ),
        ]);
    }

    /**
     * Make an Economic Census API call with error handling
     */
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private static async fetchEconomicData(url: string): Promise<any[]> {
        const response = await this.withTimeout(fetch(url));

        if (!response.ok) {
            const responseText = await response.text();
            throw new Error(`Economic Census API error: ${response.status} - ${responseText}`);
        }

        const data = await response.json();

        if (!data || !Array.isArray(data) || data.length < 2) {
            throw new Error('Invalid Economic Census API response format');
        }

        return data;
    }

    /**
     * Generate mock similar companies data
     */
    private static generateMockSimilarCompaniesData(): SimilarCompaniesData {
        const establishments = Math.floor(Math.random() * 500) + 100;
        const previousEstablishments = Math.floor(Math.random() * 480) + 95;

        return {
            numEstablishments: establishments,
            totalAnnualPayroll: Math.floor(Math.random() * 50000) + 10000,
            totalEmployment: Math.floor(Math.random() * 2000) + 500,
            averageAnnualWage: Math.floor(Math.random() * 30000) + 40000,
            countyName: "Demo County",
            naicsDescription: "Demo Industry",
            numEstablishmentsPreviousYear: previousEstablishments,
            establishmentGrowthRate: parseFloat((((establishments - previousEstablishments) / previousEstablishments) * 100).toFixed(1)),
            totalSalesReceipts: Math.floor(Math.random() * 1000000) + 500000,
            avgSalesPerEstablishment: Math.floor(Math.random() * 500000) + 100000,
            isSalesDataMock: true,
        };
    }

    /**
     * Fetch current year County Business Patterns data
     */
    private static async getCurrentYearCBPData(
        countyFips: CountyFipsData,
        naicsCode: string,
        apiKey: string
    ): Promise<{
        establishments: number | null;
        payroll: number | null;
        employment: number | null;
        naicsLabel: string | null;
        countyName: string | null;
    }> {
        try {
            const url = `${this.API_BASE}/${this.CURRENT_YEAR_CBP}/cbp?get=ESTAB,PAYANN,EMP,NAICS2017_LABEL,NAME&for=county:${countyFips.countyFips}&in=state:${countyFips.stateFips}&NAICS2017=${naicsCode}&key=${apiKey}`;

            const data = await this.fetchEconomicData(url);
            const resultRow = data[1];

            return {
                establishments: resultRow[0] ? parseInt(resultRow[0], 10) : null,
                payroll: resultRow[1] ? parseInt(resultRow[1], 10) : null,
                employment: resultRow[2] ? parseInt(resultRow[2], 10) : null,
                naicsLabel: resultRow[3] || null,
                countyName: resultRow[4] || null,
            };
        } catch (error) {
            console.error(`Error fetching current year CBP data (${this.CURRENT_YEAR_CBP}):`, error);
            return {
                establishments: null,
                payroll: null,
                employment: null,
                naicsLabel: null,
                countyName: null,
            };
        }
    }

    /**
     * Fetch previous year County Business Patterns data for establishments
     */
    private static async getPreviousYearCBPData(
        countyFips: CountyFipsData,
        naicsCode: string,
        apiKey: string
    ): Promise<number | null> {
        try {
            // Use NAICS2012 for 2016 data
            const url = `${this.API_BASE}/${this.PREVIOUS_YEAR_CBP}/cbp?get=ESTAB&for=county:${countyFips.countyFips}&in=state:${countyFips.stateFips}&NAICS2012=${naicsCode}&key=${apiKey}`;

            const data = await this.fetchEconomicData(url);
            const establishments = parseInt(data[1][0], 10);

            return isNaN(establishments) ? null : establishments;
        } catch (error) {
            console.error(`Error fetching previous year CBP data (${this.PREVIOUS_YEAR_CBP}):`, error);
            return null;
        }
    }

    /**
     * Fetch Economic Census data for sales/receipts
     */
    private static async getEconomicCensusData(
        countyFips: CountyFipsData,
        naicsCode: string,
        apiKey: string
    ): Promise<{
        totalSalesReceipts: number | null;
        establishments: number | null;
    }> {
        try {
            const url = `${this.API_BASE}/${this.ECONOMIC_CENSUS_YEAR}/ecnbasic?get=RCPTOT,ESTAB&for=county:${countyFips.countyFips}&in=state:${countyFips.stateFips}&NAICS2017=${naicsCode}&key=${apiKey}`;

            const data = await this.fetchEconomicData(url);
            const salesStr = data[1][0];
            const estabStr = data[1][1];

            let totalSalesReceipts: number | null = null;
            let establishments: number | null = null;

            if (salesStr) {
                const salesValue = parseInt(salesStr, 10);
                totalSalesReceipts = isNaN(salesValue) ? null : salesValue * 1000; // RCPTOT is in thousands
            }

            if (estabStr) {
                const estabValue = parseInt(estabStr, 10);
                establishments = isNaN(estabValue) ? null : estabValue;
            }

            return {
                totalSalesReceipts,
                establishments,
            };
        } catch (error) {
            console.error(`Error fetching Economic Census data (${this.ECONOMIC_CENSUS_YEAR}):`, error);
            return {
                totalSalesReceipts: null,
                establishments: null,
            };
        }
    }

    /**
     * Get similar companies data for a specific NAICS code and location
     */
    static async getSimilarCompaniesData(
        fullFipsCode: string,
        naicsCode: string,
        apiKey: string
    ): Promise<SimilarCompaniesData> {
        // Validate inputs
        if (!fullFipsCode || fullFipsCode.length !== 5) {
            console.warn(`Invalid fullFipsCode format: "${fullFipsCode}". Expected 5 digits.`);
            return this.generateMockSimilarCompaniesData();
        }

        if (!naicsCode || !/^\d{2,6}$/.test(naicsCode)) {
            console.warn(`Invalid NAICS code format: "${naicsCode}". Expected 2-6 digits.`);
            return this.generateMockSimilarCompaniesData();
        }

        if (!apiKey) {
            console.warn('Economic Census API key missing, returning mock data');
            return this.generateMockSimilarCompaniesData();
        }

        const countyFips: CountyFipsData = {
            stateFips: fullFipsCode.substring(0, 2),
            countyFips: fullFipsCode.substring(2, 5),
        };

        try {
            // Fetch all data in parallel for better performance
            const [currentYearData, previousYearEstablishments, economicCensusData] = await Promise.allSettled([
                this.getCurrentYearCBPData(countyFips, naicsCode, apiKey),
                this.getPreviousYearCBPData(countyFips, naicsCode, apiKey),
                this.getEconomicCensusData(countyFips, naicsCode, apiKey),
            ]);

            // Extract current year data
            const current = currentYearData.status === 'fulfilled' ? currentYearData.value : {
                establishments: null,
                payroll: null,
                employment: null,
                naicsLabel: null,
                countyName: null,
            };

            // Extract previous year establishments
            const previousEstablishments = previousYearEstablishments.status === 'fulfilled' ? previousYearEstablishments.value : null;

            // Extract economic census data
            const economicData = economicCensusData.status === 'fulfilled' ? economicCensusData.value : {
                totalSalesReceipts: null,
                establishments: null,
            };

            // Calculate derived metrics
            let averageAnnualWage: number | null = null;
            if (current.payroll !== null && current.employment !== null && current.employment > 0) {
                averageAnnualWage = (current.payroll * 1000) / current.employment; // Payroll is in thousands
            }

            let establishmentGrowthRate: number | null = null;
            if (current.establishments !== null && previousEstablishments !== null && previousEstablishments > 0) {
                establishmentGrowthRate = ((current.establishments - previousEstablishments) / previousEstablishments) * 100;
            }

            let avgSalesPerEstablishment: number | null = null;
            if (economicData.totalSalesReceipts !== null && economicData.establishments !== null && economicData.establishments > 0) {
                avgSalesPerEstablishment = economicData.totalSalesReceipts / economicData.establishments;
            }

            // Format county name
            let displayCountyName = current.countyName;
            if (current.countyName && (!isNaN(Number(current.countyName)) || current.countyName.length <= 3)) {
                displayCountyName = `County ${countyFips.countyFips} in State ${countyFips.stateFips}`;
            }

            return {
                numEstablishments: current.establishments,
                totalAnnualPayroll: current.payroll,
                totalEmployment: current.employment,
                averageAnnualWage: averageAnnualWage !== null ? parseFloat(averageAnnualWage.toFixed(0)) : null,
                naicsDescription: current.naicsLabel,
                countyName: displayCountyName,
                numEstablishmentsPreviousYear: previousEstablishments,
                establishmentGrowthRate: establishmentGrowthRate !== null ? parseFloat(establishmentGrowthRate.toFixed(1)) : null,
                totalSalesReceipts: economicData.totalSalesReceipts,
                avgSalesPerEstablishment: avgSalesPerEstablishment !== null ? parseFloat(avgSalesPerEstablishment.toFixed(0)) : null,
                isSalesDataMock: false,
            };

        } catch (error) {
            console.error('Error fetching similar companies data:', error);
            return this.generateMockSimilarCompaniesData();
        }
    }

    /**
     * Get cached similar companies data
     * This method can be enhanced with Next.js unstable_cache in the future
     */
    static async getCachedSimilarCompaniesData(
        fullFipsCode: string,
        naicsCode: string,
        apiKey: string
    ): Promise<SimilarCompaniesData> {
        // For now, just call the regular method
        // TODO: Add caching with unstable_cache or Redis
        return this.getSimilarCompaniesData(fullFipsCode, naicsCode, apiKey);
    }

    /**
     * Validate NAICS code format
     */
    static validateNaicsCode(naicsCode: string): boolean {
        return /^\d{2,6}$/.test(naicsCode);
    }

    /**
     * Validate FIPS code format
     */
    static validateFipsCode(fipsCode: string): boolean {
        return /^\d{5}$/.test(fipsCode);
    }
} 