/**
 * WalkScore Service
 * Handles all WalkScore API interactions for walkability, transit, and bike scores
 */

// Types
interface WalkabilityData {
    walkScore?: number | null;
    isWalkScoreMock?: boolean;
    walkDescription?: string | null;
    transitScore?: number | null;
    isTransitScoreMock?: boolean;
    transitDescription?: string | null;
    bikeScore?: number | null;
    isBikeScoreMock?: boolean;
    bikeDescription?: string | null;
    wsLink?: string | null;
}

interface WalkScoreParams {
    address?: string | null;
    city?: string | null;
    state?: string | null;
    postalCode?: string | null;
    latitude?: number | null;
    longitude?: number | null;
}

export class WalkScoreService {
    private static readonly API_BASE = 'https://api.walkscore.com/score';
    private static readonly TIMEOUT_MS = 10000;

    /**
     * Timeout wrapper for API calls
     */
    private static async withTimeout<T>(promise: Promise<T>): Promise<T> {
        return Promise.race([
            promise,
            new Promise<T>((_, reject) =>
                setTimeout(() => reject(new Error(`WalkScore API timeout after ${this.TIMEOUT_MS}ms`)), this.TIMEOUT_MS)
            ),
        ]);
    }

    /**
     * Helper function to derive walkability descriptions based on scores
     */
    private static getDerivedWalkabilityDescription(
        score: number | null | undefined,
        type: 'walk' | 'transit' | 'bike',
        isMock: boolean
    ): string | null {
        if (score === null || score === undefined) return null;

        const suffix = isMock ? " (mock data)" : "";

        if (type === 'walk') {
            if (score >= 90) return "Walker's Paradise®" + suffix;
            if (score >= 70) return "Very Walkable" + suffix;
            if (score >= 50) return "Somewhat Walkable" + suffix;
            return "Car-Dependent" + suffix;
        } else if (type === 'transit') {
            if (score >= 70) return "Excellent Transit" + suffix;
            if (score >= 50) return "Good Transit" + suffix;
            if (score >= 25) return "Some Transit" + suffix;
            return "Minimal Transit" + suffix;
        } else if (type === 'bike') {
            if (score >= 90) return "Biker's Paradise®" + suffix;
            if (score >= 70) return "Very Bikeable" + suffix;
            if (score >= 50) return "Bikeable" + suffix;
            return "Somewhat Bikeable" + suffix;
        }

        return null;
    }

    /**
     * Generate mock walkability data
     */
    private static generateMockWalkabilityData(): WalkabilityData {
        const walkScore = Math.floor(Math.random() * 40) + 60;
        const transitScore = Math.floor(Math.random() * 50) + 40;
        const bikeScore = Math.floor(Math.random() * 40) + 60;

        return {
            walkScore,
            isWalkScoreMock: true,
            walkDescription: this.getDerivedWalkabilityDescription(walkScore, 'walk', true),
            transitScore,
            isTransitScoreMock: true,
            transitDescription: this.getDerivedWalkabilityDescription(transitScore, 'transit', true),
            bikeScore,
            isBikeScoreMock: true,
            bikeDescription: this.getDerivedWalkabilityDescription(bikeScore, 'bike', true),
            wsLink: null,
        };
    }

    /**
     * Fetch walkability data from WalkScore API
     */
    static async getWalkabilityData(params: WalkScoreParams): Promise<WalkabilityData> {
        const apiKey = process.env.WALKSCORE_API;

        // Return mock data if no API key or required parameters
        if (!apiKey || !params.latitude || !params.longitude) {
            console.warn('WalkScore API key or coordinates missing, returning mock data');
            return this.generateMockWalkabilityData();
        }

        try {
            // Build the API URL
            const urlParams = new URLSearchParams({
                format: 'json',
                address: `${params.address ? params.address + ', ' : ''}${params.city}, ${params.state} ${params.postalCode}`,
                lat: params.latitude.toString(),
                lon: params.longitude.toString(),
                transit: '1',
                bike: '1',
                wsapikey: apiKey,
            });

            const url = `${this.API_BASE}?${urlParams.toString()}`;

            // Make the API call with timeout
            const response = await this.withTimeout(fetch(url));

            if (!response.ok) {
                throw new Error(`WalkScore API error: ${response.status} - ${await response.text()}`);
            }

            const data = await response.json();

            // Validate response structure
            if (!data || typeof data !== 'object') {
                throw new Error('Invalid WalkScore API response format');
            }

            // Extract scores, handling various response formats
            const walkScore = typeof data.walkscore === 'number' ? data.walkscore : null;
            const transitScore = data.transit?.score ?? null;
            const bikeScore = data.bike?.score ?? null;

            // Check for API errors in response
            if (data.status && data.status !== 1) {
                console.warn(`WalkScore API returned status ${data.status}:`, data);
                return this.generateMockWalkabilityData();
            }

            return {
                walkScore,
                isWalkScoreMock: walkScore === null,
                walkDescription: data.description || this.getDerivedWalkabilityDescription(walkScore, 'walk', walkScore === null),
                transitScore,
                isTransitScoreMock: transitScore === null,
                transitDescription: data.transit?.description || this.getDerivedWalkabilityDescription(transitScore, 'transit', transitScore === null),
                bikeScore,
                isBikeScoreMock: bikeScore === null,
                bikeDescription: data.bike?.description || this.getDerivedWalkabilityDescription(bikeScore, 'bike', bikeScore === null),
                wsLink: data.ws_link || null,
            };

        } catch (error) {
            console.error('Error fetching WalkScore data:', error);
            return this.generateMockWalkabilityData();
        }
    }

    /**
     * Get walkability data with caching support
     * This method can be enhanced with Next.js unstable_cache in the future
     */
    static async getCachedWalkabilityData(params: WalkScoreParams): Promise<WalkabilityData> {
        // For now, just call the regular method
        // TODO: Add caching with unstable_cache or Redis
        return this.getWalkabilityData(params);
    }

    /**
     * Validate coordinates are within reasonable bounds
     */
    static validateCoordinates(latitude: number, longitude: number): boolean {
        return (
            latitude >= -90 && latitude <= 90 &&
            longitude >= -180 && longitude <= 180
        );
    }

    /**
     * Get default coordinates for a location (fallback method)
     */
    static getDefaultCoordinates(): { latitude: number; longitude: number } {
        // Default to NYC coordinates as fallback
        // In a real implementation, you might have a lookup table for major cities
        return {
            latitude: 40.7128,
            longitude: -74.0060
        };
    }
} 