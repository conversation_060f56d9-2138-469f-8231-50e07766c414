/**
 * Financial Data Parser Service
 * Extracts financial parsing logic from UI components
 */

import <PERSON> from 'papaparse';

// Types
interface FinancialData {
    month: string;
    totalRevenue: number;
    netProfitAfterTax: number;
    netProfitMargin: number;
}

interface YearData {
    year: string;
    data: FinancialData[];
}

interface DataRoomFile {
    id: string;
    file_name: string;
    file_url: string;
}

interface CsvRow {
    [key: string]: string | number | null | undefined;
}

export class FinancialParserService {
    /**
     * Parse numeric value from CSV data, handling various formats
     */
    static parseNumericValue(value: string | number | null | undefined): number {
        if (typeof value === 'number') return value;
        if (!value) return 0;

        // Convert string to number, removing currency symbols and commas
        const cleanValue = String(value).replace(/[^0-9.-]+/g, '');
        return parseFloat(cleanValue) || 0;
    }

    /**
     * Extract year from filename
     */
    static extractYearFromFilename(filename: string): string {
        const yearMatch = filename.match(/(\d{4})/);
        return yearMatch ? yearMatch[1] : 'Unknown';
    }

    /**
     * Format month value with fallbacks
     */
    static formatMonthValue(rawMonth: string | number | null | undefined, rowIndex?: number): string {
        let monthValue = String(rawMonth || '').trim();

        // If month is empty or just a number, try to format it
        if (!monthValue || /^\d+$/.test(monthValue)) {
            // Try to interpret as a month number
            const monthNum = parseInt(monthValue || '0', 10);
            if (monthNum > 0 && monthNum <= 12) {
                // Convert to month name
                const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'];
                monthValue = monthNames[monthNum - 1];
            } else if (rowIndex !== undefined) {
                // Use row number as fallback
                monthValue = `Month ${rowIndex + 1}`;
            }
        }

        return monthValue;
    }

    /**
     * Find required columns in CSV headers
     */
    static findRequiredColumns(headers: string[]): {
        monthCol: string | null;
        revenueCol: string | null;
        profitCol: string | null;
        marginCol: string | null;
    } {
        // Normalize headers for better matching
        const normalizedHeaders = headers.map(h => String(h || '').toLowerCase().trim());

        // Find month column (usually first column or contains 'month')
        const monthCol = headers.find((_, index) =>
            index === 0 ||
            normalizedHeaders[index].includes('month') ||
            normalizedHeaders[index].includes('period')
        ) || null;

        // Find revenue column
        const revenueCol = headers.find((_, index) =>
            normalizedHeaders[index] === 'total revenue' ||
            normalizedHeaders[index].includes('revenue')
        ) || null;

        // Find profit column
        const profitCol = headers.find((_, index) =>
            normalizedHeaders[index] === 'net profit after tax' ||
            normalizedHeaders[index].includes('net profit')
        ) || null;

        // Find margin column
        const marginCol = headers.find((_, index) =>
            normalizedHeaders[index] === 'net profit margin' ||
            normalizedHeaders[index].includes('profit margin') ||
            normalizedHeaders[index].includes('margin')
        ) || null;

        return { monthCol, revenueCol, profitCol, marginCol };
    }

    /**
     * Process a single CSV row into FinancialData
     */
    static processRow(
        row: CsvRow,
        columns: ReturnType<typeof FinancialParserService.findRequiredColumns>,
        rowIndex?: number
    ): FinancialData | null {
        const { monthCol, revenueCol, profitCol, marginCol } = columns;

        if (!revenueCol || !profitCol || !marginCol) {
            return null;
        }

        const month = this.formatMonthValue(monthCol ? row[monthCol] : null, rowIndex);
        const totalRevenue = this.parseNumericValue(row[revenueCol]);
        const netProfitAfterTax = this.parseNumericValue(row[profitCol]);
        const netProfitMargin = this.parseNumericValue(row[marginCol]);

        return {
            month,
            totalRevenue,
            netProfitAfterTax,
            netProfitMargin
        };
    }

    /**
     * Parse a single CSV file
     */
    static async parseFinancialFile(file: DataRoomFile): Promise<YearData | null> {
        try {
            const response = await fetch(file.file_url);
            if (!response.ok) {
                throw new Error(`Failed to fetch ${file.file_name}: ${response.status}`);
            }

            const csvText = await response.text();
            const year = this.extractYearFromFilename(file.file_name);

            return new Promise<YearData | null>((resolve) => {
                Papa.parse(csvText, {
                    header: true,
                    skipEmptyLines: true,
                    complete: (results) => {
                        try {
                            if (!results.data || results.data.length === 0) {
                                console.warn(`No data found in ${file.file_name}`);
                                resolve(null);
                                return;
                            }

                            const headers = Object.keys(results.data[0] as CsvRow);
                            const columns = this.findRequiredColumns(headers);

                            // Check if we have required columns
                            if (!columns.revenueCol || !columns.profitCol || !columns.marginCol) {
                                console.error(`Missing required columns in ${file.file_name}. Required: 'Total Revenue', 'Net Profit After Tax', 'Net Profit Margin'`);
                                resolve(null);
                                return;
                            }

                            // Process each row
                            const processedData = (results.data as CsvRow[])
                                .map((row, rowIndex) => this.processRow(row, columns, rowIndex))
                                .filter((item): item is FinancialData => item !== null);

                            if (processedData.length === 0) {
                                console.error(`No usable data found in ${file.file_name}`);
                                resolve(null);
                                return;
                            }

                            console.log(`Successfully processed ${processedData.length} rows from ${file.file_name}`);

                            resolve({
                                year,
                                data: processedData
                            });
                        } catch (error) {
                            console.error(`Error processing ${file.file_name}:`, error);
                            resolve(null);
                        }
                    },
                    error: (parseError: Error) => {
                        console.error(`Error parsing ${file.file_name}:`, parseError);
                        resolve(null);
                    }
                });
            });
        } catch (error) {
            console.error(`Error fetching ${file.file_name}:`, error);
            return null;
        }
    }

    /**
     * Parse multiple financial files in parallel
     */
    static async parseFinancialFiles(files: DataRoomFile[]): Promise<YearData[]> {
        // Filter for CSV files that might contain financial data
        const csvFiles = files.filter(file =>
            file.file_name.toLowerCase().endsWith('.csv') &&
            /\d{4}/.test(file.file_name) // Must contain a year
        );

        if (csvFiles.length === 0) {
            throw new Error('No CSV files with years found in the uploaded files.');
        }

        // Parse all files in parallel
        const yearDataPromises = csvFiles.map(file => this.parseFinancialFile(file));
        const yearDataResults = await Promise.all(yearDataPromises);
        const validYearData = yearDataResults.filter(Boolean) as YearData[];

        if (validYearData.length === 0) {
            throw new Error('Your CSV files were found but could not be processed. Please ensure your P&L CSV files have the following columns: "Total Revenue", "Net Profit After Tax", and "Net Profit Margin". The first column should contain months.');
        }

        // Sort by year, most recent first
        validYearData.sort((a, b) => parseInt(b.year) - parseInt(a.year));

        return validYearData;
    }
} 