@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 120 20% 98%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 120 18% 33%;
    --primary-foreground: 0 0% 98%;

    --secondary: 120 12% 90%;
    --secondary-foreground: 120 18% 33%;

    --muted: 120 12% 90%;
    --muted-foreground: 120 10% 45%;

    --accent: 120 5% 96%;
    --accent-foreground: 120 18% 33%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 120 10% 85%;
    --input: 120 10% 85%;
    --ring: 120 14% 45%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 120 10% 5%;
    --foreground: 0 0% 98%;

    --card: 120 10% 5%;
    --card-foreground: 0 0% 98%;

    --popover: 120 10% 5%;
    --popover-foreground: 0 0% 98%;

    --primary: 120 12% 70%;
    --primary-foreground: 120 10% 5%;

    --secondary: 120 10% 15%;
    --secondary-foreground: 0 0% 98%;

    --muted: 120 10% 15%;
    --muted-foreground: 120 10% 65%;

    --accent: 120 10% 15%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 120 10% 15%;
    --input: 120 10% 15%;
    --ring: 120 14% 45%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}