export default function Terms() {
    return (
        <main className="max-w-4xl mx-auto px-4 py-12">
            <h1 className="text-3xl font-bold mb-8">Terms & Conditions</h1>

            <div className="prose prose-gray max-w-none">
                <p className="mb-6">Last updated: {new Date().toLocaleDateString()}</p>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">1. Agreement to Terms</h2>
                    <p>By accessing or using BuySell, you agree to be bound by these Terms. If you disagree with any part of the terms, you may not access the service.</p>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">2. Use License</h2>
                    <p>Permission is granted to temporarily access the materials (information or software) on BuySell for personal, non-commercial transitory viewing only.</p>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">3. User Accounts</h2>
                    <ul className="list-disc pl-6 mb-4">
                        <li>You must be 18 years or older to use this service</li>
                        <li>You are responsible for maintaining the security of your account</li>
                        <li>You are responsible for all activities under your account</li>
                    </ul>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">4. Limitations</h2>
                    <p>BuySell shall not be held liable for any damages arising out of the use or inability to use the materials on our website.</p>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">5. Governing Law</h2>
                    <p>These terms and conditions are governed by and construed in accordance with the laws of the United Kingdom.</p>
                </section>
            </div>
        </main>
    )
} 