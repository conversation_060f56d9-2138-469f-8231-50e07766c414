// src/app/deals-dashboard/page.tsx
import { Metadata } from 'next';
import { Header, Footer } from '@/components';
import EnhancedDealsDashboardClient from './components/EnhancedDealsDashboardClient';
import DealStatusDebugger from './components/DealStatusDebugger';

export const metadata: Metadata = {
    title: 'Deals Dashboard | Business Marketplace',
    description: 'Track your deals and their progress through the acquisition pipeline',
};

export default function DealsDashboardPage() {
    return (
        <>
            <Header />
            <EnhancedDealsDashboardClient />

            {/* Debug component - remove in production */}
            {process.env.NODE_ENV === 'development' && (
                <div className="mt-12 mb-8">
                    <DealStatusDebugger />
                </div>
            )}

            <Footer />
        </>
    );
}