'use server'

import { createClient } from '@/utils/supabase/server'
import { revalidatePath } from 'next/cache'

/**
 * Updates deal status when a buyer uploads a letter of intent
 * This function should be called after a successful LOI upload
 */
export async function updateDealStatusToInitialOffer(
    listingId: string,
    buyerId: string
): Promise<{ success: boolean; error?: string }> {
    try {
        const supabase = await createClient()

        // First get the listing owner (seller)
        const { data: listing, error: listingError } = await supabase
            .from('listings')
            .select('user_id')
            .eq('id', listingId)
            .single()

        if (listingError || !listing) {
            return { success: false, error: 'Listing not found' }
        }

        // Upsert deal status to initial_offer
        const { error: dealStatusError } = await supabase
            .from('deal_statuses')
            .upsert({
                listing_id: listingId,
                buyer_id: buyerId,
                seller_id: listing.user_id,
                status: 'initial_offer',
                status_changed_at: new Date().toISOString()
            }, {
                onConflict: 'listing_id,buyer_id'
            })

        if (dealStatusError) {
            console.error('Deal status update error:', dealStatusError)
            return { success: false, error: dealStatusError.message }
        }

        // Revalidate relevant pages
        revalidatePath('/deals-dashboard')
        revalidatePath('/messages')

        console.log('✅ Deal status updated to initial_offer via server action')
        return { success: true }

    } catch (error) {
        console.error('Error in updateDealStatusToInitialOffer:', error)
        return { success: false, error: 'Failed to update deal status' }
    }
}

/**
 * Generic function to update deal status
 */
export async function updateDealStatus(
    listingId: string,
    buyerId: string,
    newStatus: string
): Promise<{ success: boolean; error?: string }> {
    try {
        const supabase = await createClient()

        // Get the listing owner (seller)
        const { data: listing, error: listingError } = await supabase
            .from('listings')
            .select('user_id')
            .eq('id', listingId)
            .single()

        if (listingError || !listing) {
            return { success: false, error: 'Listing not found' }
        }

        // Update deal status
        const { error: dealStatusError } = await supabase
            .from('deal_statuses')
            .upsert({
                listing_id: listingId,
                buyer_id: buyerId,
                seller_id: listing.user_id,
                status: newStatus,
                status_changed_at: new Date().toISOString()
            }, {
                onConflict: 'listing_id,buyer_id'
            })

        if (dealStatusError) {
            console.error('Deal status update error:', dealStatusError)
            return { success: false, error: dealStatusError.message }
        }

        // Revalidate relevant pages
        revalidatePath('/deals-dashboard')
        revalidatePath('/messages')

        console.log(`✅ Deal status updated to ${newStatus}`)
        return { success: true }

    } catch (error) {
        console.error('Error in updateDealStatus:', error)
        return { success: false, error: 'Failed to update deal status' }
    }
} 