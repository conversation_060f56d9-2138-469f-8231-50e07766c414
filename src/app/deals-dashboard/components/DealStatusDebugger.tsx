'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/utils/supabase/client';
import { updateDealStatusToInitialOffer } from '../actions';

interface DealStatusRow {
    id: string;
    listing_id: string;
    buyer_id: string;
    seller_id: string;
    status: string;
    status_changed_at: string;
    listing_title?: string;
    buyer_name?: string;
}

interface LOIAttachment {
    id: string;
    file_name: string;
    attachment_type: string;
    uploaded_by: string;
    listing_id: string;
    sender_id: string;
    deal_status?: string;
}

export default function DealStatusDebugger() {
    const [dealStatuses, setDealStatuses] = useState<DealStatusRow[]>([]);
    const [loiAttachments, setLOIAttachments] = useState<LOIAttachment[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const supabase = createClient();

    const fetchData = useCallback(async () => {
        setIsLoading(true);
        try {
            // Fetch deal statuses with related data
            const { data: deals } = await supabase
                .from('deal_statuses')
                .select(`
                    *,
                    listings (title),
                    profiles!buyer_id (first_name, last_name)
                `)
                .order('status_changed_at', { ascending: false })
                .limit(10);

            // Fetch LOI attachments with deal status info
            const { data: lois } = await supabase
                .from('message_attachments')
                .select(`
                    *,
                    messages (
                        listing_id,
                        sender_id
                    )
                `)
                .eq('attachment_type', 'letter_of_intent')
                .order('created_at', { ascending: false });

            // Process LOI data with deal status lookup
            if (lois) {
                const loiWithStatus = await Promise.all(
                    lois.map(async (loi) => {
                        const message = Array.isArray(loi.messages) ? loi.messages[0] : loi.messages;
                        if (!message) return null;

                        const { data: dealStatus } = await supabase
                            .from('deal_statuses')
                            .select('status')
                            .eq('listing_id', message.listing_id)
                            .eq('buyer_id', message.sender_id)
                            .single();

                        return {
                            id: loi.id,
                            file_name: loi.file_name,
                            attachment_type: loi.attachment_type,
                            uploaded_by: loi.uploaded_by,
                            listing_id: message.listing_id,
                            sender_id: message.sender_id,
                            deal_status: dealStatus?.status || 'unknown'
                        };
                    })
                );

                setLOIAttachments(loiWithStatus.filter(Boolean) as LOIAttachment[]);
            }

            if (deals) {
                const processedDeals = deals.map(deal => ({
                    ...deal,
                    listing_title: Array.isArray(deal.listings)
                        ? deal.listings[0]?.title
                        : deal.listings?.title,
                    buyer_name: Array.isArray(deal.profiles)
                        ? `${deal.profiles[0]?.first_name} ${deal.profiles[0]?.last_name}`
                        : `${deal.profiles?.first_name} ${deal.profiles?.last_name}`
                }));
                setDealStatuses(processedDeals);
            }

        } catch (error) {
            console.error('Error fetching debug data:', error);
        } finally {
            setIsLoading(false);
        }
    }, [supabase]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const handleTestStatusUpdate = async (listingId: string, buyerId: string) => {
        try {
            const result = await updateDealStatusToInitialOffer(listingId, buyerId);
            if (result.success) {
                console.log('✅ Test status update successful');
                await fetchData(); // Refresh data
            } else {
                console.error('❌ Test status update failed:', result.error);
            }
        } catch (error) {
            console.error('Error testing status update:', error);
        }
    };

    if (isLoading) {
        return <div className="p-4">Loading debug data...</div>;
    }

    return (
        <div className="max-w-6xl mx-auto p-6 bg-white rounded-lg shadow">
            <h2 className="text-2xl font-bold mb-6">Deal Status Flow Debugger</h2>

            {/* LOI Attachments Section */}
            <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">Letter of Intent Uploads</h3>
                <div className="overflow-x-auto">
                    <table className="min-w-full border border-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-4 py-2 text-left">File Name</th>
                                <th className="px-4 py-2 text-left">Listing ID</th>
                                <th className="px-4 py-2 text-left">Buyer ID</th>
                                <th className="px-4 py-2 text-left">Current Deal Status</th>
                                <th className="px-4 py-2 text-left">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {loiAttachments.map((loi) => (
                                <tr key={loi.id} className="border-b">
                                    <td className="px-4 py-2">{loi.file_name}</td>
                                    <td className="px-4 py-2 font-mono text-xs">{loi.listing_id.slice(0, 8)}...</td>
                                    <td className="px-4 py-2 font-mono text-xs">{loi.sender_id.slice(0, 8)}...</td>
                                    <td className="px-4 py-2">
                                        <span className={`px-2 py-1 rounded text-xs ${loi.deal_status === 'initial_offer'
                                            ? 'bg-green-100 text-green-800'
                                            : 'bg-yellow-100 text-yellow-800'
                                            }`}>
                                            {loi.deal_status}
                                        </span>
                                    </td>
                                    <td className="px-4 py-2">
                                        {loi.deal_status !== 'initial_offer' && (
                                            <button
                                                onClick={() => handleTestStatusUpdate(loi.listing_id, loi.sender_id)}
                                                className="px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
                                            >
                                                Fix Status
                                            </button>
                                        )}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Deal Statuses Section */}
            <div>
                <h3 className="text-lg font-semibold mb-4">Current Deal Statuses</h3>
                <div className="overflow-x-auto">
                    <table className="min-w-full border border-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-4 py-2 text-left">Listing</th>
                                <th className="px-4 py-2 text-left">Buyer</th>
                                <th className="px-4 py-2 text-left">Status</th>
                                <th className="px-4 py-2 text-left">Changed At</th>
                            </tr>
                        </thead>
                        <tbody>
                            {dealStatuses.map((deal) => (
                                <tr key={deal.id} className="border-b">
                                    <td className="px-4 py-2">{deal.listing_title || 'Unknown'}</td>
                                    <td className="px-4 py-2">{deal.buyer_name || 'Unknown'}</td>
                                    <td className="px-4 py-2">
                                        <span className={`px-2 py-1 rounded text-xs ${deal.status === 'initial_offer'
                                            ? 'bg-green-100 text-green-800'
                                            : deal.status === 'active_conversation'
                                                ? 'bg-blue-100 text-blue-800'
                                                : 'bg-gray-100 text-gray-800'
                                            }`}>
                                            {deal.status}
                                        </span>
                                    </td>
                                    <td className="px-4 py-2 text-xs">
                                        {new Date(deal.status_changed_at).toLocaleString()}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded">
                <h4 className="font-semibold mb-2">Expected Flow:</h4>
                <ol className="list-decimal list-inside space-y-1 text-sm">
                    <li>Buyer uploads Letter of Intent in conversation</li>
                    <li>Deal status automatically updates to <code className="bg-blue-100 px-1 rounded">initial_offer</code></li>
                    <li>Buyer&apos;s dashboard moves business from &quot;Active Conversations&quot; to &quot;Initial Offer&quot;</li>
                    <li>Seller&apos;s dashboard moves buyer from &quot;Interested Buyers&quot; to &quot;Offers Received&quot;</li>
                </ol>
            </div>
        </div>
    );
} 