import { ReactNode } from 'react';
import { LoadingSpinner } from '@/components';

interface DealColumnProps {
    title: string;
    count: number;
    children?: ReactNode;
    isLoading?: boolean;
}

export default function DealColumn({
    title,
    count,
    children,
    isLoading = false
}: DealColumnProps) {
    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 overflow-hidden flex flex-col h-full">
            {/* Column Header */}
            <div className="p-4 bg-gradient-to-r from-gray-50 to-gray-100/50 border-b border-gray-200/50">
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-gray-900">{title}</h2>
                    <span className="bg-gray-200/80 text-gray-700 text-sm px-2.5 py-1 rounded-full font-medium">
                        {count}
                    </span>
                </div>
            </div>

            {/* Column Content */}
            <div className="flex-grow p-5 space-y-5 min-h-[70vh] overflow-y-auto">
                {isLoading ? (
                    <div className="flex justify-center items-center h-32">
                        <LoadingSpinner />
                    </div>
                ) : count === 0 ? (
                    <div className="flex flex-col justify-center items-center h-32 text-center">
                        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3">
                            <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
                        </div>
                        <p className="text-gray-500 text-sm">No deals in this stage</p>
                    </div>
                ) : (
                    children
                )}
            </div>
        </div>
    );
}
