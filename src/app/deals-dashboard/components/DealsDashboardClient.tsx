'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import DealColumn from './DealColumn';
import DealCard from './DealCard';
import { Search, Filter, TrendingUp, BarChart3 } from 'lucide-react';

interface DealItem {
    id: string;
    title: string;
    imageUrl: string | null;
    location: string;
    category: string;
    price: number;
}

export default function DealsDashboardClient() {
    const [savedDeals, setSavedDeals] = useState<DealItem[]>([]);
    const [activeConversations, setActiveConversations] = useState<DealItem[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const supabase = createClient();

    useEffect(() => {
        async function fetchDeals() {
            setIsLoading(true);

            try {
                const { data: { session } } = await supabase.auth.getSession();
                if (!session?.user?.id) return;

                // Fetch saved listings
                const { data: savedListings } = await supabase
                    .from('saved_listings')
                    .select(`
            listing_id,
            listings (
              id,
              title,
              image_url,
              price,
              listing_details (
                city,
                state_id
              ),
              industries (
                name
              )
            )
          `)
                    .eq('user_id', session.user.id);

                // Fetch active conversations (listings where user has sent/received messages)
                const { data: conversationDeals } = await supabase
                    .from('messages')
                    .select(`
            listing_id,
            listings (
              id,
              title,
              image_url,
              price,
              listing_details (
                city,
                state_id
              ),
              industries (
                name
              )
            )
          `)
                    .or(`sender_id.eq.${session.user.id},recipient_id.eq.${session.user.id}`)
                    .order('created_at', { ascending: false });

                console.log('Debug savedListings first item:', savedListings?.[0]);
                console.log('Debug conversationDeals first item:', conversationDeals?.[0]);

                // Process saved listings
                const savedItems = savedListings?.map(item => {
                    // Handle both possible data shapes
                    const listing = Array.isArray(item.listings)
                        ? item.listings[0]
                        : item.listings;

                    return {
                        id: item.listing_id,
                        title: listing?.title || 'Unknown Listing',
                        imageUrl: listing?.image_url,
                        location:
                            (Array.isArray(listing?.listing_details)
                                ? (listing?.listing_details[0] as { city?: string })?.city
                                : (listing?.listing_details as { city?: string })?.city) || 'Unknown',
                        category:
                            (Array.isArray(listing?.industries)
                                ? (listing?.industries[0] as { name?: string })?.name
                                : (listing?.industries as { name?: string })?.name) || 'General',
                        price: listing?.price || 0,
                    };
                }) || [];

                // Process active conversations (deduplicate by listing_id)
                const conversationItems = conversationDeals ?
                    [...new Map(conversationDeals.map(item => {
                        // Handle both possible data shapes
                        const listing = Array.isArray(item.listings)
                            ? item.listings[0]
                            : item.listings;

                        return [item.listing_id, {
                            id: item.listing_id,
                            title: listing?.title || 'Unknown Listing',
                            imageUrl: listing?.image_url,
                            location:
                                (Array.isArray(listing?.listing_details)
                                    ? (listing?.listing_details[0] as { city?: string })?.city
                                    : (listing?.listing_details as { city?: string })?.city) || 'Unknown',
                            category:
                                (Array.isArray(listing?.industries)
                                    ? (listing?.industries[0] as { name?: string })?.name
                                    : (listing?.industries as { name?: string })?.name) || 'General',
                            price: listing?.price || 0,
                        }];
                    })).values()] : [];

                setSavedDeals(savedItems);
                setActiveConversations(conversationItems);
            } catch (error) {
                console.error('Error fetching deals:', error);
            } finally {
                setIsLoading(false);
            }
        }

        fetchDeals();

        // Set up subscription for real-time updates
        const messagesChannel = supabase
            .channel('public:messages')
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'messages'
            }, () => {
                fetchDeals();
            })
            .subscribe();

        const savedListingsChannel = supabase
            .channel('public:saved_listings')
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'saved_listings'
            }, () => {
                fetchDeals();
            })
            .subscribe();

        return () => {
            supabase.removeChannel(messagesChannel);
            supabase.removeChannel(savedListingsChannel);
        };
    }, [supabase]);

    return (
        <main className="min-h-screen py-8 bg-gray-50">
            <div className="max-w-[1800px] mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header Card */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <div className="p-2 bg-blue-50 rounded-lg">
                                <TrendingUp className="w-5 h-5 text-blue-600" />
                            </div>
                            <div>
                                <h1 className="text-2xl font-semibold text-gray-900">Deals Dashboard</h1>
                                <p className="text-gray-600 text-sm mt-1">Track your deals and their progress</p>
                            </div>
                        </div>
                        <div className="flex gap-3">
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="Search deals..."
                                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                />
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                            </div>
                            <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                                <Filter className="h-4 w-4" />
                                <span>Filter</span>
                            </button>
                        </div>
                    </div>
                </div>

                {/* Stats Overview Card */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                    <div className="flex items-center space-x-3 mb-6">
                        <div className="p-2 bg-green-50 rounded-lg">
                            <BarChart3 className="w-5 h-5 text-green-600" />
                        </div>
                        <h2 className="text-xl font-semibold text-gray-900">Deal Overview</h2>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                        <div className="text-center">
                            <div className="text-2xl font-bold text-gray-900">{savedDeals.length}</div>
                            <div className="text-sm text-gray-600">Saved Listings</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-gray-900">{activeConversations.length}</div>
                            <div className="text-sm text-gray-600">Active Conversations</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-gray-900">0</div>
                            <div className="text-sm text-gray-600">Initial Offers</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-gray-900">0</div>
                            <div className="text-sm text-gray-600">Acquired</div>
                        </div>
                    </div>
                </div>

                {/* Deals Pipeline */}
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-5 gap-6 lg:gap-8">
                    {/* Saved Listings Column */}
                    <DealColumn
                        title="Saved Listing"
                        count={savedDeals.length}
                        isLoading={isLoading}
                    >
                        {savedDeals.map(deal => (
                            <DealCard
                                key={deal.id}
                                id={deal.id}
                                title={deal.title}
                                imageUrl={deal.imageUrl}
                                location={deal.location}
                                category={deal.category}
                                price={deal.price}
                            />
                        ))}
                    </DealColumn>

                    {/* Active Conversations Column */}
                    <DealColumn
                        title="Active Conversations"
                        count={activeConversations.length}
                        isLoading={isLoading}
                    >
                        {activeConversations.map(deal => (
                            <DealCard
                                key={deal.id}
                                id={deal.id}
                                title={deal.title}
                                imageUrl={deal.imageUrl}
                                location={deal.location}
                                category={deal.category}
                                price={deal.price}
                            />
                        ))}
                    </DealColumn>

                    {/* Additional columns for future implementation */}
                    <DealColumn title="Buyer/Seller Meeting" count={0} isLoading={isLoading} />
                    <DealColumn title="Initial Offer" count={0} isLoading={isLoading} />
                    <DealColumn title="Acquired" count={0} isLoading={isLoading} />
                </div>
            </div>
        </main>
    );
}
