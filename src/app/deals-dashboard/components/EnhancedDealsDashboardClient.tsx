'use client';

import { useEffect, useState, useRef, useCallback } from 'react';
import { createClient } from '@/utils/supabase/client';
import DealColumn from './DealColumn';
import DealCard from './DealCard';
import BuyerProfileCard from './BuyerProfileCard';
import { Search, Filter, TrendingUp, BarChart3, ChevronDown, Store, Eye } from 'lucide-react';
import type { UserRole } from '@/types/supabase';

interface DealItem {
    id: string;
    title: string;
    imageUrl: string | null;
    location: string;
    category: string;
    price: number;
    dealStatus?: string;
    statusUpdated?: string;
}

interface BuyerProfile {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string | null;
    company: string | null;
    profilePhoto: string | null;
    listingId: string;
    lastMessageDate: string;
    dealStatus?: string;
}

interface UserListing {
    id: string;
    title: string;
}

interface ListingData {
    id?: string;
    title?: string;
    image_url?: string;
    price?: number;
    user_id?: string;
    listing_details?: Array<{ city?: string }> | { city?: string };
    industries?: Array<{ name?: string }> | { name?: string };
}

type ViewMode = 'buyer' | 'seller';

export default function EnhancedDealsDashboardClient() {
    const [viewMode, setViewMode] = useState<ViewMode>('buyer');
    const [selectedListingId, setSelectedListingId] = useState<string | null>(null);
    const [userListings, setUserListings] = useState<UserListing[]>([]);
    const [savedDeals, setSavedDeals] = useState<DealItem[]>([]);
    const [activeConversations, setActiveConversations] = useState<DealItem[]>([]);
    const [meetingScheduled, setMeetingScheduled] = useState<DealItem[]>([]);
    const [initialOffers, setInitialOffers] = useState<DealItem[]>([]);
    const [acquired, setAcquired] = useState<DealItem[]>([]);
    const [interestedBuyers, setInterestedBuyers] = useState<BuyerProfile[]>([]);

    // Seller-specific states
    const [offersReceived, setOffersReceived] = useState<BuyerProfile[]>([]);
    const [underNegotiation, setUnderNegotiation] = useState<BuyerProfile[]>([]);

    const [isLoading, setIsLoading] = useState(true);
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [hasInitialized, setHasInitialized] = useState(false);
    const [userRole, setUserRole] = useState<UserRole | null>(null);
    const supabase = createClient();
    const dropdownRef = useRef<HTMLDivElement>(null);

    // Handle click outside dropdown to close it
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsDropdownOpen(false);
            }
        }

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Fetch user role from profile
    const fetchUserRole = useCallback(async (userId: string) => {
        try {
            const { data: profile } = await supabase
                .from('profiles')
                .select('user_role')
                .eq('user_id', userId)
                .single();

            setUserRole(profile?.user_role || null);
            return profile?.user_role || null;
        } catch (error) {
            console.error('Error fetching user role:', error);
            return null;
        }
    }, [supabase]);

    // Fetch buyer deals (updated to include deal statuses)
    const fetchBuyerDeals = useCallback(async (userId: string) => {
        try {
            // Fetch deals with statuses from deal_statuses table
            const { data: dealsWithStatus } = await supabase
                .from('deal_statuses')
                .select(`
                    *,
                    listings (
                        id,
                        title,
                        image_url,
                        price,
                        user_id,
                        listing_details (
                            city,
                            state_id
                        ),
                        industries (
                            name
                        )
                    )
                `)
                .eq('buyer_id', userId)
                .order('updated_at', { ascending: false });

            // Fetch saved listings (for items not yet in deal_statuses)
            const { data: savedListings } = await supabase
                .from('saved_listings')
                .select(`
                    listing_id,
                    listings (
                        id,
                        title,
                        image_url,
                        price,
                        user_id,
                        listing_details (
                            city,
                            state_id
                        ),
                        industries (
                            name
                        )
                    )
                `)
                .eq('user_id', userId);

            // Fetch active conversations (listings where user has sent/received messages but may not have deal status)
            const { data: conversationDeals } = await supabase
                .from('messages')
                .select(`
                    listing_id,
                    listings (
                        id,
                        title,
                        image_url,
                        price,
                        user_id,
                        listing_details (
                            city,
                            state_id
                        ),
                        industries (
                            name
                        )
                    )
                `)
                .or(`sender_id.eq.${userId},recipient_id.eq.${userId}`)
                .order('created_at', { ascending: false });

            // Helper function to format listing data
            const formatListing = (listing: ListingData) => {
                const listingData = Array.isArray(listing) ? listing[0] : listing;
                return {
                    id: listingData?.id || '',
                    title: listingData?.title || 'Unknown Listing',
                    imageUrl: listingData?.image_url,
                    location: (Array.isArray(listingData?.listing_details)
                        ? (listingData?.listing_details[0] as { city?: string })?.city
                        : (listingData?.listing_details as { city?: string })?.city) || 'Unknown',
                    category: (Array.isArray(listingData?.industries)
                        ? (listingData?.industries[0] as { name?: string })?.name
                        : (listingData?.industries as { name?: string })?.name) || 'General',
                    price: listingData?.price || 0,
                };
            };

            // Process deals with statuses
            const statusDeals = dealsWithStatus?.map(deal => ({
                ...formatListing(deal.listings),
                dealStatus: deal.status,
                statusUpdated: deal.status_changed_at
            })).filter(deal => deal.id) || [];

            // Get listing IDs that already have deal statuses
            const dealsWithStatusIds = new Set(statusDeals.map(deal => deal.id));

            // Process saved listings (exclude those with deal statuses and user's own listings)
            const savedItems: DealItem[] = [];
            if (savedListings) {
                for (const item of savedListings) {
                    const listing = Array.isArray(item.listings) ? item.listings[0] : item.listings;
                    // Skip if this is user's own listing or already has deal status
                    if (listing?.user_id === userId || dealsWithStatusIds.has(item.listing_id)) continue;

                    savedItems.push({
                        ...formatListing(listing),
                        dealStatus: 'saved'
                    });
                }
            }

            // Process active conversations (exclude those with deal statuses, deduplicate by listing_id, exclude user's own listings)
            const conversationMap = new Map<string, DealItem>();
            if (conversationDeals) {
                for (const item of conversationDeals) {
                    const listing = Array.isArray(item.listings) ? item.listings[0] : item.listings;
                    // Skip if this is user's own listing or already has deal status
                    if (listing?.user_id === userId || dealsWithStatusIds.has(item.listing_id)) continue;

                    conversationMap.set(item.listing_id, {
                        ...formatListing(listing),
                        dealStatus: 'active_conversation'
                    });
                }
            }
            const conversationItems = Array.from(conversationMap.values());

            // Combine all deals
            const allDeals = [...statusDeals, ...savedItems, ...conversationItems];

            // Categorize deals by status (filter out null values)
            const savedDealsFiltered = allDeals.filter((deal): deal is DealItem =>
                deal !== null && deal.dealStatus === 'saved'
            );
            const activeConversationsFiltered = allDeals.filter((deal): deal is DealItem =>
                deal !== null && deal.dealStatus === 'active_conversation'
            );
            const meetingScheduled = allDeals.filter((deal): deal is DealItem =>
                deal !== null && deal.dealStatus === 'meeting_scheduled'
            );
            const initialOffers = allDeals.filter((deal): deal is DealItem =>
                deal !== null && deal.dealStatus === 'initial_offer'
            );
            const acquired = allDeals.filter((deal): deal is DealItem =>
                deal !== null && deal.dealStatus === 'closed'
            );

            console.log('🎯 Buyer deal categorization:', {
                saved: savedDealsFiltered.length,
                activeConversations: activeConversationsFiltered.length,
                meetingScheduled: meetingScheduled.length,
                initialOffers: initialOffers.length,
                acquired: acquired.length,
                allDeals: allDeals.map(d => ({ id: d.id, title: d.title, status: d.dealStatus }))
            });

            // Update state with categorized deals
            setSavedDeals(savedDealsFiltered);
            setActiveConversations(activeConversationsFiltered);

            // Store additional deal categories in state
            setMeetingScheduled(meetingScheduled);
            setInitialOffers(initialOffers);
            setAcquired(acquired);

        } catch (error) {
            console.error('Error fetching buyer deals:', error);
        }
    }, [supabase]);

    // Fetch interested buyers for specific listing
    const fetchInterestedBuyers = useCallback(async (listingId: string) => {
        try {
            // Get all unique users who have messaged about this specific listing
            const { data: messageData } = await supabase
                .from('messages')
                .select(`
                    sender_id,
                    recipient_id,
                    created_at,
                    listings!inner (
                        user_id
                    )
                `)
                .eq('listing_id', listingId)
                .order('created_at', { ascending: false });

            if (!messageData || messageData.length === 0) {
                setInterestedBuyers([]);
                setOffersReceived([]);
                setUnderNegotiation([]);
                return;
            }

            // Find the listing owner
            const firstMessage = messageData[0];
            const listings = firstMessage?.listings;
            const listingOwnerId = Array.isArray(listings)
                ? (listings[0] as { user_id: string })?.user_id
                : (listings as { user_id: string })?.user_id;

            if (!listingOwnerId) {
                setInterestedBuyers([]);
                setOffersReceived([]);
                setUnderNegotiation([]);
                return;
            }

            // Get unique buyer IDs (exclude the listing owner)
            const buyerIds = [...new Set(
                messageData
                    .map(msg => msg.sender_id === listingOwnerId ? msg.recipient_id : msg.sender_id)
                    .filter(id => id !== listingOwnerId)
            )];

            if (buyerIds.length === 0) {
                setInterestedBuyers([]);
                setOffersReceived([]);
                setUnderNegotiation([]);
                return;
            }

            // Check deal statuses for these buyers
            const { data: dealStatuses } = await supabase
                .from('deal_statuses')
                .select('buyer_id, status')
                .eq('listing_id', listingId)
                .in('buyer_id', buyerIds);

            // Fetch buyer profiles
            const { data: profiles } = await supabase
                .from('profiles')
                .select('id, user_id, first_name, last_name, email, company, profile_photo')
                .in('user_id', buyerIds);

            // Get last message date for each buyer and categorize by deal status
            const allBuyers = await Promise.all(
                buyerIds.map(async (buyerId) => {
                    const { data: lastMessage } = await supabase
                        .from('messages')
                        .select('created_at')
                        .eq('listing_id', listingId)
                        .or(`sender_id.eq.${buyerId},recipient_id.eq.${buyerId}`)
                        .order('created_at', { ascending: false })
                        .limit(1);

                    const profile = profiles?.find(p => p.user_id === buyerId);
                    const dealStatus = dealStatuses?.find(ds => ds.buyer_id === buyerId);

                    return {
                        id: buyerId,
                        firstName: profile?.first_name || null,
                        lastName: profile?.last_name || null,
                        email: profile?.email || null,
                        company: profile?.company || null,
                        profilePhoto: profile?.profile_photo || null,
                        listingId,
                        lastMessageDate: lastMessage?.[0]?.created_at || new Date().toISOString(),
                        dealStatus: dealStatus?.status || 'interested'
                    };
                })
            );

            // Categorize buyers by deal status
            const interestedBuyersFiltered = allBuyers.filter(buyer =>
                !buyer.dealStatus || buyer.dealStatus === 'interested' || buyer.dealStatus === 'saved' || buyer.dealStatus === 'active_conversation'
            );
            const offersReceivedFiltered = allBuyers.filter(buyer =>
                buyer.dealStatus === 'initial_offer'
            );
            const underNegotiationFiltered = allBuyers.filter(buyer =>
                buyer.dealStatus === 'letter_of_intent' || buyer.dealStatus === 'due_diligence' || buyer.dealStatus === 'final_negotiation'
            );

            console.log('📊 Buyer categorization:', {
                interested: interestedBuyersFiltered.length,
                offersReceived: offersReceivedFiltered.length,
                underNegotiation: underNegotiationFiltered.length,
                allBuyers: allBuyers.map(b => ({ id: b.id, status: b.dealStatus }))
            });

            setInterestedBuyers(interestedBuyersFiltered);
            setOffersReceived(offersReceivedFiltered);
            setUnderNegotiation(underNegotiationFiltered);

        } catch (error) {
            console.error('Error fetching interested buyers:', error);
            setInterestedBuyers([]);
            setOffersReceived([]);
            setUnderNegotiation([]);
        }
    }, [supabase]);

    // Initial data fetch
    useEffect(() => {
        async function fetchUserData() {
            setIsLoading(true);
            try {
                const { data: { session } } = await supabase.auth.getSession();
                if (!session?.user?.id) return;

                // Fetch user role first
                const role = await fetchUserRole(session.user.id);

                // Fetch user's listings to determine if they're a seller
                const { data: listings } = await supabase
                    .from('listings')
                    .select('id, title')
                    .eq('user_id', session.user.id)
                    .order('created_at', { ascending: false });

                setUserListings(listings || []);

                // Auto-default view based on user role and available data
                if (!hasInitialized) {
                    if (role === 'seller' && listings && listings.length > 0) {
                        setViewMode('seller');
                        setSelectedListingId(listings[0].id);
                    } else if (role === 'seller_buyer') {
                        // For seller_buyer, default to seller if they have listings, otherwise buyer
                        if (listings && listings.length > 0) {
                            setViewMode('seller');
                            setSelectedListingId(listings[0].id);
                        } else {
                            setViewMode('buyer');
                        }
                    } else {
                        // For buyer or users without role, default to buyer view
                        setViewMode('buyer');
                    }
                }

                // Always fetch buyer deals (for the "Deals I'm exploring" view)
                await fetchBuyerDeals(session.user.id);

                setHasInitialized(true);
            } catch (error) {
                console.error('Error fetching user data:', error);
            } finally {
                setIsLoading(false);
            }
        }

        fetchUserData();
    }, [supabase, fetchBuyerDeals, fetchUserRole, hasInitialized]);

    // Fetch interested buyers when seller view and listing changes
    useEffect(() => {
        if (viewMode === 'seller' && selectedListingId) {
            fetchInterestedBuyers(selectedListingId);
        }
    }, [viewMode, selectedListingId, fetchInterestedBuyers]);

    // Set up real-time subscription for deal status changes
    useEffect(() => {
        if (!selectedListingId) return;

        const dealStatusSubscription = supabase
            .channel('deal_status_changes')
            .on(
                'postgres_changes',
                {
                    event: '*',
                    schema: 'public',
                    table: 'deal_statuses',
                    filter: `listing_id=eq.${selectedListingId}`,
                },
                (payload) => {
                    console.log('🔄 Deal status changed:', payload);

                    // Refresh data based on current view mode
                    if (viewMode === 'seller' && selectedListingId) {
                        fetchInterestedBuyers(selectedListingId);
                    }

                    // Also refresh buyer deals if they exist
                    supabase.auth.getUser().then(({ data }) => {
                        if (data.user?.id) {
                            fetchBuyerDeals(data.user.id);
                        }
                    });
                }
            )
            .subscribe();

        return () => {
            supabase.removeChannel(dealStatusSubscription);
        };
    }, [viewMode, selectedListingId, fetchInterestedBuyers, fetchBuyerDeals, supabase]);

    const handleViewModeChange = (mode: ViewMode, listingId?: string) => {
        setViewMode(mode);
        if (mode === 'seller' && listingId) {
            setSelectedListingId(listingId);
        } else if (mode === 'buyer') {
            setSelectedListingId(null);
        }
        setIsDropdownOpen(false);
    };

    const selectedListingTitle = selectedListingId ?
        userListings.find(l => l.id === selectedListingId)?.title : null;

    // Determine what should be shown in dropdown based on user role
    const shouldShowSellerOptions = userRole === 'seller' || userRole === 'seller_buyer';
    const shouldShowBuyerOptions = userRole === 'buyer' || userRole === 'seller_buyer';
    const shouldShowDropdown = shouldShowSellerOptions || shouldShowBuyerOptions;
    const isDropdownDisabled = userRole === 'buyer'; // Disable for buyer-only users

    return (
        <main className="min-h-screen py-8 bg-gray-50">
            <div className="max-w-[1800px] mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header Card */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <div className="p-2 bg-blue-50 rounded-lg">
                                <TrendingUp className="w-5 h-5 text-blue-600" />
                            </div>
                            <div>
                                <h1 className="text-2xl font-semibold text-gray-900">Deals Dashboard</h1>
                                <p className="text-gray-600 text-sm mt-1">
                                    {viewMode === 'seller'
                                        ? `Viewing: ${selectedListingTitle || 'Select a listing'}`
                                        : 'Track your deals and their progress'
                                    }
                                </p>
                            </div>
                        </div>

                        <div className="flex gap-3">
                            {/* Role-Aware Listing Dropdown */}
                            {shouldShowDropdown && (
                                <div className="relative" ref={dropdownRef}>
                                    <button
                                        onClick={() => !isDropdownDisabled && setIsDropdownOpen(!isDropdownOpen)}
                                        disabled={isDropdownDisabled}
                                        className={`flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg transition-colors font-medium ${isDropdownDisabled
                                            ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                                            : 'hover:bg-gray-50 cursor-pointer'
                                            }`}
                                    >
                                        <Store className="h-4 w-4" />
                                        <span>
                                            {viewMode === 'seller' && selectedListingTitle
                                                ? selectedListingTitle
                                                : viewMode === 'buyer'
                                                    ? 'Deals I\'m exploring'
                                                    : 'Switch View'
                                            }
                                        </span>
                                        <ChevronDown className={`h-4 w-4 ${isDropdownDisabled ? 'text-gray-400' : ''}`} />
                                    </button>

                                    {isDropdownOpen && !isDropdownDisabled && (
                                        <div className="absolute top-full left-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                                            <div className="p-2">
                                                {/* Show seller options if user has seller role */}
                                                {shouldShowSellerOptions && userListings.length > 0 && (
                                                    <div className="mb-2">
                                                        <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                                                            Your Listings
                                                        </div>
                                                        {userListings.map((listing) => (
                                                            <button
                                                                key={listing.id}
                                                                onClick={() => handleViewModeChange('seller', listing.id)}
                                                                className={`w-full text-left px-3 py-2 rounded-md text-sm hover:bg-gray-100 transition-colors ${viewMode === 'seller' && selectedListingId === listing.id
                                                                    ? 'bg-blue-50 text-blue-700 font-medium'
                                                                    : 'text-gray-700'
                                                                    }`}
                                                            >
                                                                {listing.title}
                                                            </button>
                                                        ))}
                                                    </div>
                                                )}

                                                {/* Show divider if both sections are visible */}
                                                {shouldShowSellerOptions && shouldShowBuyerOptions && userListings.length > 0 && (
                                                    <div className="border-t border-gray-200 my-2"></div>
                                                )}

                                                {/* Show buyer options if user has buyer role */}
                                                {shouldShowBuyerOptions && (
                                                    <button
                                                        onClick={() => handleViewModeChange('buyer')}
                                                        className={`w-full text-left px-3 py-2 rounded-md text-sm hover:bg-gray-100 transition-colors ${viewMode === 'buyer'
                                                            ? 'bg-blue-50 text-blue-700 font-medium'
                                                            : 'text-gray-700'
                                                            }`}
                                                    >
                                                        <div className="flex items-center gap-2">
                                                            <Eye className="w-4 h-4" />
                                                            Deals I&apos;m Exploring
                                                        </div>
                                                    </button>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}

                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder={viewMode === 'seller' ? "Search buyers..." : "Search deals..."}
                                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                />
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                            </div>
                            <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                                <Filter className="h-4 w-4" />
                                <span>Filter</span>
                            </button>
                        </div>
                    </div>
                </div>

                {/* Stats Overview Card */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                    <div className="flex items-center space-x-3 mb-6">
                        <div className="p-2 bg-green-50 rounded-lg">
                            <BarChart3 className="w-5 h-5 text-green-600" />
                        </div>
                        <h2 className="text-xl font-semibold text-gray-900">
                            {viewMode === 'seller' ? 'Seller Overview' : 'Deal Overview'}
                        </h2>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                        {viewMode === 'seller' ? (
                            <>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-gray-900">{interestedBuyers.length}</div>
                                    <div className="text-sm text-gray-600">Interested Buyers</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-gray-900">0</div>
                                    <div className="text-sm text-gray-600">Scheduled Meetings</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-gray-900">{offersReceived.length}</div>
                                    <div className="text-sm text-gray-600">Offers Received</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-gray-900">{underNegotiation.length}</div>
                                    <div className="text-sm text-gray-600">Under Negotiation</div>
                                </div>
                            </>
                        ) : (
                            <>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-gray-900">{savedDeals.length}</div>
                                    <div className="text-sm text-gray-600">Saved Listings</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-gray-900">{activeConversations.length}</div>
                                    <div className="text-sm text-gray-600">Active Conversations</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-gray-900">{initialOffers.length}</div>
                                    <div className="text-sm text-gray-600">Initial Offers</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-gray-900">{acquired.length}</div>
                                    <div className="text-sm text-gray-600">Acquired</div>
                                </div>
                            </>
                        )}
                    </div>
                </div>

                {/* Content based on view mode */}
                {viewMode === 'seller' ? (
                    // Seller View - Show buyers categorized by deal status
                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-5 gap-6 lg:gap-8">
                        <DealColumn
                            title="Interested Buyers"
                            count={interestedBuyers.length}
                            isLoading={isLoading}
                        >
                            {interestedBuyers.map(buyer => (
                                <BuyerProfileCard
                                    key={buyer.id}
                                    id={buyer.id}
                                    firstName={buyer.firstName}
                                    lastName={buyer.lastName}
                                    email={buyer.email}
                                    company={buyer.company}
                                    profilePhoto={buyer.profilePhoto}
                                    listingId={buyer.listingId}
                                    lastMessageDate={buyer.lastMessageDate}
                                />
                            ))}
                        </DealColumn>

                        <DealColumn title="Scheduled Meetings" count={0} isLoading={isLoading} />

                        <DealColumn
                            title="Offers Received"
                            count={offersReceived.length}
                            isLoading={isLoading}
                        >
                            {offersReceived.map(buyer => (
                                <BuyerProfileCard
                                    key={buyer.id}
                                    id={buyer.id}
                                    firstName={buyer.firstName}
                                    lastName={buyer.lastName}
                                    email={buyer.email}
                                    company={buyer.company}
                                    profilePhoto={buyer.profilePhoto}
                                    listingId={buyer.listingId}
                                    lastMessageDate={buyer.lastMessageDate}
                                />
                            ))}
                        </DealColumn>

                        <DealColumn
                            title="Under Negotiation"
                            count={underNegotiation.length}
                            isLoading={isLoading}
                        >
                            {underNegotiation.map(buyer => (
                                <BuyerProfileCard
                                    key={buyer.id}
                                    id={buyer.id}
                                    firstName={buyer.firstName}
                                    lastName={buyer.lastName}
                                    email={buyer.email}
                                    company={buyer.company}
                                    profilePhoto={buyer.profilePhoto}
                                    listingId={buyer.listingId}
                                    lastMessageDate={buyer.lastMessageDate}
                                />
                            ))}
                        </DealColumn>

                        <DealColumn title="Completed Sales" count={0} isLoading={isLoading} />
                    </div>
                ) : (
                    // Buyer View - Updated with deal status columns
                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-5 gap-6 lg:gap-8">
                        <DealColumn
                            title="Saved Listing"
                            count={savedDeals.length}
                            isLoading={isLoading}
                        >
                            {savedDeals.map(deal => (
                                <DealCard
                                    key={deal.id}
                                    id={deal.id}
                                    title={deal.title}
                                    imageUrl={deal.imageUrl}
                                    location={deal.location}
                                    category={deal.category}
                                    price={deal.price}
                                />
                            ))}
                        </DealColumn>

                        <DealColumn
                            title="Active Conversations"
                            count={activeConversations.length}
                            isLoading={isLoading}
                        >
                            {activeConversations.map(deal => (
                                <DealCard
                                    key={deal.id}
                                    id={deal.id}
                                    title={deal.title}
                                    imageUrl={deal.imageUrl}
                                    location={deal.location}
                                    category={deal.category}
                                    price={deal.price}
                                />
                            ))}
                        </DealColumn>

                        <DealColumn
                            title="Buyer/Seller Meeting"
                            count={meetingScheduled.length}
                            isLoading={isLoading}
                        >
                            {meetingScheduled.map(deal => (
                                <DealCard
                                    key={deal.id}
                                    id={deal.id}
                                    title={deal.title}
                                    imageUrl={deal.imageUrl}
                                    location={deal.location}
                                    category={deal.category}
                                    price={deal.price}
                                />
                            ))}
                        </DealColumn>

                        <DealColumn
                            title="Initial Offer"
                            count={initialOffers.length}
                            isLoading={isLoading}
                        >
                            {initialOffers.map(deal => (
                                <DealCard
                                    key={deal.id}
                                    id={deal.id}
                                    title={deal.title}
                                    imageUrl={deal.imageUrl}
                                    location={deal.location}
                                    category={deal.category}
                                    price={deal.price}
                                />
                            ))}
                        </DealColumn>

                        <DealColumn
                            title="Acquired"
                            count={acquired.length}
                            isLoading={isLoading}
                        >
                            {acquired.map(deal => (
                                <DealCard
                                    key={deal.id}
                                    id={deal.id}
                                    title={deal.title}
                                    imageUrl={deal.imageUrl}
                                    location={deal.location}
                                    category={deal.category}
                                    price={deal.price}
                                />
                            ))}
                        </DealColumn>
                    </div>
                )}
            </div>
        </main>
    );
} 