'use client';

import { Eye, MessageCircle, MapPin, Building2 } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { PriceFormatter } from '@/components';
import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { NewMessageModal } from '@/components';

interface DealCardProps {
    id: string;
    title: string;
    imageUrl: string | null;
    location: string;
    category: string;
    price: number;
}

interface ListingOwner {
    user_id: string;
    profile: {
        first_name: string | null;
        last_name: string | null;
        profile_photo: string | null;
    };
}

export default function DealCard({
    id,
    title,
    imageUrl,
    location,
    category,
    price
}: DealCardProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
    const [listingOwner, setListingOwner] = useState<ListingOwner | null>(null);
    const supabase = createClient();

    // Fetch listing owner information
    useEffect(() => {
        const fetchListingOwner = async () => {
            console.log('🔍 Fetching listing owner for listing ID:', id);

            // First get the listing with user_id
            const { data: listing, error: listingError } = await supabase
                .from('listings')
                .select('user_id')
                .eq('id', id)
                .single();

            if (listingError) {
                console.error('❌ Error fetching listing:', listingError);
                return;
            }

            if (listing) {
                console.log('📋 Found listing with user_id:', listing.user_id);

                // Then get the profile for that user_id
                const { data: profile, error: profileError } = await supabase
                    .from('profiles')
                    .select('first_name, last_name, profile_photo')
                    .eq('user_id', listing.user_id)
                    .single();

                if (profileError) {
                    console.error('❌ Error fetching profile:', profileError);
                    return;
                }

                if (profile) {
                    console.log('👤 Found profile:', profile);
                    setListingOwner({
                        user_id: listing.user_id,
                        profile: profile
                    });
                } else {
                    console.warn('⚠️ No profile found for user_id:', listing.user_id);
                }
            } else {
                console.warn('⚠️ No listing found for ID:', id);
            }
        };

        fetchListingOwner();
    }, [id, supabase]);

    const handleNewMessage = async () => {
        if (!listingOwner) return;

        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.user?.id) return;

        // Create conversation if it doesn't exist
        const participants = [session.user.id, listingOwner.user_id].sort();
        const { data: existingConv } = await supabase
            .from('conversations')
            .select('id')
            .eq('listing_id', id)
            .eq('participant1_id', participants[0])
            .eq('participant2_id', participants[1])
            .single();

        let conversationId;

        if (!existingConv) {
            const { data: newConv } = await supabase
                .from('conversations')
                .insert({
                    listing_id: id,
                    participant1_id: participants[0],
                    participant2_id: participants[1]
                })
                .select('id')
                .single();

            conversationId = newConv?.id;
        } else {
            conversationId = existingConv.id;
        }

        setIsModalOpen(true);
        setCurrentConversationId(conversationId);
    };

    return (
        <>
            <div className="bg-white rounded-xl border border-gray-200/60 shadow-sm overflow-hidden hover:shadow-lg transition-all duration-300 group">
                {/* Image Section with Overlaid Labels */}
                <div className="relative h-48 w-full overflow-hidden">
                    <Image
                        src={imageUrl || '/images/placeholder.png'}
                        alt={title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                    />

                    {/* Dark gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>

                    {/* Location and Category Labels */}
                    <div className="absolute bottom-3 left-3 right-3 space-y-2">
                        <div className="flex items-center gap-2 flex-wrap">
                            {location && (
                                <div className="flex items-center gap-1 px-2 py-1 bg-black/40 backdrop-blur-sm rounded-md text-white text-xs font-medium">
                                    <MapPin className="w-3 h-3" />
                                    <span>{location}</span>
                                </div>
                            )}
                            {category && (
                                <div className="flex items-center gap-1 px-2 py-1 bg-black/40 backdrop-blur-sm rounded-md text-white text-xs font-medium">
                                    <Building2 className="w-3 h-3" />
                                    <span>{category}</span>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Content Section */}
                <div className="p-5">
                    <h3 className="font-semibold text-gray-900 line-clamp-2 mb-6 text-base leading-tight">{title}</h3>

                    <PriceFormatter price={price} className="text-xl font-bold text-gray-900 mb-8" />

                    {/* Improved Action Buttons */}
                    <div className="flex gap-2.5">
                        <Link
                            href={`/listings/${id}`}
                            className="flex items-center justify-center gap-2 flex-1 px-4 py-2.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm font-medium transition-all duration-200 group/btn border border-gray-200 hover:border-gray-300"
                        >
                            <Eye className="w-4 h-4 group-hover/btn:scale-110 transition-transform duration-200" />
                            <span>View</span>
                        </Link>
                        <button
                            onClick={handleNewMessage}
                            disabled={!listingOwner}
                            className="flex items-center justify-center gap-2 flex-1 px-4 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 group/btn shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <MessageCircle className="w-4 h-4 group-hover/btn:scale-110 transition-transform duration-200" />
                            <span>Message</span>
                        </button>
                    </div>
                </div>
            </div>

            {listingOwner && (
                <NewMessageModal
                    isOpen={isModalOpen}
                    onClose={() => setIsModalOpen(false)}
                    recipientId={listingOwner.user_id}
                    listingId={id}
                    ownerName={{
                        firstName: listingOwner.profile.first_name || '',
                        lastName: listingOwner.profile.last_name || ''
                    }}
                    ownerAvatar={listingOwner.profile.profile_photo}
                    listingName={title}
                    conversationId={currentConversationId || undefined}
                />
            )}
        </>
    );
}
