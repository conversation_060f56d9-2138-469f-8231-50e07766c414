@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

/* Remove old gradient animation */
/* @layer utilities {
  @keyframes gradient { ... } 
  .animate-gradient { ... }
} */

/* Add blob animation styles */
@layer utilities {
  .blob-btn {
    /* Required for positioning the pseudo-element */
    position: relative;
    /* Clips the pseudo-element */
    overflow: hidden;
    z-index: 1;
    /* Ensure button content is above blob */
  }

  .blob-btn::before {
    content: '';
    position: absolute;
    /* Position using CSS variables updated by JS */
    top: var(--y, 50%);
    left: var(--x, 50%);
    transform: translate(-50%, -50%);

    /* Initial state: invisible circle */
    width: 0;
    height: 0;
    background: radial-gradient(circle closest-side, rgb(107 33 168 / 0.6), transparent);
    border-radius: 50%;
    opacity: 0.8;
    /* Slightly transparent blob */

    /* Smooth transition for size change */
    transition: width 0.4s ease-out, height 0.4s ease-out;
    z-index: -1;
    /* Position blob behind button content */
  }

  /* State when hovered or focused: Expand the blob */
  .blob-btn:hover::before,
  .blob-btn:focus::before {
    width: 300px;
    /* Adjust size as needed */
    height: 300px;
  }

  /* Add slow gradient animation */
  @keyframes slowGradientShift {
    0% {
      background-position: 0% 50%;
    }

    50% {
      background-position: 100% 50%;
    }

    100% {
      background-position: 0% 50%;
    }
  }

  .animate-slow-gradient {
    /* Use a long duration for subtle effect */
    animation: slowGradientShift 25s ease infinite;
  }
}

/* New animation for sliding background */
@keyframes slide-border {
  0% {
    background-position: 0% 50%;
  }

  /* Start left */
  25% {
    background-position: 50% 0%;
  }

  /* Move top */
  50% {
    background-position: 100% 50%;
  }

  /* Move right */
  75% {
    background-position: 50% 100%;
  }

  /* Move bottom */
  100% {
    background-position: 0% 50%;
  }

  /* Back to start */
}

/* Alternative simpler left-to-right sliding animation */
@keyframes slide-lr {
  0% {
    background-position: -100% 0;
  }

  /* Start fully left */
  100% {
    background-position: 200% 0;
  }

  /* Move fully right */
}

.animated-border-container {
  position: relative;
  /* Needed for pseudo-element positioning */
  border-radius: 9999px;
  /* Ensure container is rounded */
  padding: 2px;
  /* Space for the border effect */
  overflow: hidden;
  /* Keep gradient contained */
  background: white;
  /* Ensure background is white behind input */
}

.animated-border-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* Make pseudo-element rounded */
  border-radius: inherit;
  /* Create the gradient line */
  background: linear-gradient(90deg,
      /* Gradient direction (left to right) */
      transparent,
      #6A0DAD,
      /* Deep purple */
      transparent);
  /* Set background size - the purple part is 20% width */
  background-size: 50% 100%;
  /* Prevent repeating */
  background-repeat: no-repeat;
  /* Apply the simpler slide animation */
  animation: slide-lr 3s linear infinite;
  z-index: 1;
  /* Behind the inner content */
}

/* Adjust the inner container styles */
.inner-input-container {
  position: relative;
  z-index: 10;
  /* Above the pseudo-element */
  display: flex;
  align-items: center;
  background-color: white;
  /* Match container background */
  border-radius: 9999px;
  /* Match outer rounding */
  /* Removed padding here, handled by outer container */
  /* padding: 0.25rem; */
  width: 100%;
}

/* Update animation for pulsing dots (Google Maps style) */
@keyframes pulse-dot {
  0% {
    /* Start with a fainter shadow */
    box-shadow: 0 0 0 0 rgba(168, 85, 247, 0.5);
    /* Opacity 0.5 instead of 0.7 */
  }

  70% {
    /* Expand less */
    box-shadow: 0 0 0 6px rgba(168, 85, 247, 0);
    /* Spread 6px instead of 10px */
  }

  100% {
    /* End with no shadow */
    box-shadow: 0 0 0 0 rgba(168, 85, 247, 0);
  }
}

.animate-pulse-dot {
  /* Apply the new box-shadow animation */
  animation: pulse-dot 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  /* The base dot should remain visible */
  opacity: 1 !important;
  /* Ensure base dot is opaque */
}

/* Add this to your globals.css file */
.hide-scrollbar {
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
  scrollbar-width: none;
  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
  /* Safari and Chrome */
}