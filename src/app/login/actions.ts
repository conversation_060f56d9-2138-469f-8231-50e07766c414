'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'

import { createClient } from '@/utils/supabase/server'

export async function login(formData: FormData): Promise<{ error?: string }> {
    const supabase = await createClient()

    // Extract and validate inputs
    const email = formData.get('email') as string
    const password = formData.get('password') as string

    // Basic input validation
    if (!email || !email.trim()) {
        return { error: 'Email is required' }
    }

    if (!password || !password.trim()) {
        return { error: 'Password is required' }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email.trim())) {
        return { error: 'Please enter a valid email address' }
    }

    const data = {
        email: email.trim(),
        password: password,
    }

    const { error } = await supabase.auth.signInWithPassword(data)

    if (error) {
        return { error: error.message }
    }

    revalidatePath('/', 'layout')
    redirect('/account')
}

export async function signup(formData: FormData): Promise<{ error?: string }> {
    const supabase = await createClient()

    // Extract and validate inputs
    const email = formData.get('email') as string
    const password = formData.get('password') as string

    // Basic input validation
    if (!email || !email.trim()) {
        return { error: 'Email is required' }
    }

    if (!password || !password.trim()) {
        return { error: 'Password is required' }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email.trim())) {
        return { error: 'Please enter a valid email address' }
    }

    // Validate password strength
    if (password.length < 8) {
        return { error: 'Password must be at least 8 characters long' }
    }

    const data = {
        email: email.trim(),
        password: password,
    }

    // Attempt signup
    const { error } = await supabase.auth.signUp(data)

    if (error) {
        // Only log actual errors, not sensitive data
        console.error('Signup error occurred')
        return { error: error.message }
    }

    revalidatePath('/', 'layout')
    redirect('/account')
}