import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ListingFormModalProvider } from '@/contexts/ListingFormModalContext';
import { UserProvider } from '@/contexts/UserContext';
import ListingFormModal from '@/components/modals/ListingFormModal';
import Script from 'next/script';
import { Toaster } from 'sonner';

const geistSans = Geist({
    variable: "--font-geist-sans",
    subsets: ["latin"],
});

const geistMono = Geist_Mono({
    variable: "--font-geist-mono",
    subsets: ["latin"],
});

export const metadata: Metadata = {
    title: {
        default: "BuySell - Your Trusted Marketplace",
        template: "%s | BuySell"
    },
    description: "BuySell is your go-to marketplace for buying and selling products. Connect with trusted sellers and find great deals in your area.",
    keywords: ["marketplace", "buy", "sell", "ecommerce", "local marketplace", "online shopping"],
    authors: [
        {
            name: "BuySell Team"
        }
    ],
    openGraph: {
        title: "BuySell - Your Trusted Marketplace",
        description: "Buy and sell products easily on BuySell. Find great deals or reach thousands of potential buyers.",
        url: "https://buysell.com",
        siteName: "BuySell",
        locale: "en_US",
        type: "website",
    },
    twitter: {
        card: "summary_large_image",
        title: "BuySell - Your Trusted Marketplace",
        description: "Buy and sell products easily on BuySell. Find great deals or reach thousands of potential buyers.",
    },
    robots: {
        index: true,
        follow: true,
    },
    metadataBase: new URL('https://buysell.com'),
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en" suppressHydrationWarning>
            <head>
                {/* Google Analytics */}
                <Script
                    strategy="afterInteractive"
                    src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
                />
                <Script
                    id="google-analytics"
                    strategy="afterInteractive"
                    dangerouslySetInnerHTML={{
                        __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
            `,
                    }}
                />
            </head>
            <body
                className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
            >
                <UserProvider>
                    <ListingFormModalProvider>
                        {children}
                        <ListingFormModal />
                        <Toaster richColors position="top-right" />
                    </ListingFormModalProvider>
                </UserProvider>
            </body>
        </html>
    );
}