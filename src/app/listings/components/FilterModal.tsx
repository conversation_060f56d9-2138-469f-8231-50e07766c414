'use client';

import { Industry, INDUSTRY_CONFIG } from '@/config/industries';
import { X } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';

interface FilterModalProps {
    isOpen: boolean;
    onClose: () => void;
    currentIndustries?: Industry[];
}

export function FilterModal({ isOpen, onClose, currentIndustries = [] }: FilterModalProps) {
    const router = useRouter();
    const searchParams = useSearchParams();
    const [selectedIndustries, setSelectedIndustries] = useState<Industry[]>(currentIndustries);

    if (!isOpen) return null;

    const handleIndustryToggle = (value: Industry) => {
        setSelectedIndustries(prev =>
            prev.includes(value)
                ? prev.filter(i => i !== value)
                : [...prev, value]
        );
    };

    const handleApply = () => {
        const params = new URLSearchParams(searchParams.toString());
        if (selectedIndustries.length > 0) {
            params.set('industries', selectedIndustries.join(','));
        } else {
            params.delete('industries');
        }
        router.push(`/listings?${params.toString()}`);
        onClose();
    };

    const handleRemoveFilters = () => {
        const params = new URLSearchParams(searchParams.toString());
        params.delete('industries');
        router.push(`/listings?${params.toString()}`);
        setSelectedIndustries([]);
        onClose();
    };

    const hasActiveFilters = selectedIndustries.length > 0;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
            <div className="bg-white rounded-lg w-full max-w-md p-6">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold">Filter Listings</h2>
                    <button
                        onClick={onClose}
                        className="p-2 hover:bg-gray-100 rounded-full"
                    >
                        <X size={20} />
                    </button>
                </div>

                <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Industries ({selectedIndustries.length} selected)
                    </label>
                    <div className="grid grid-cols-2 gap-2 max-h-[400px] overflow-y-auto">
                        {INDUSTRY_CONFIG.map(({ value, label, icon: Icon }) => (
                            <button
                                key={value}
                                onClick={() => handleIndustryToggle(value)}
                                className={`p-2 rounded-lg text-sm flex items-center gap-2 ${selectedIndustries.includes(value)
                                    ? 'bg-emerald-100 text-emerald-700 border-2 border-emerald-500'
                                    : 'bg-gray-50 hover:bg-gray-100 text-gray-700'
                                    }`}
                            >
                                <Icon size={16} />
                                <span className="text-left flex-1">{label}</span>
                            </button>
                        ))}
                    </div>
                </div>

                <div className="flex justify-between">
                    <button
                        onClick={handleRemoveFilters}
                        disabled={!hasActiveFilters}
                        className={`px-4 py-2 rounded-lg text-sm ${hasActiveFilters
                            ? 'text-red-600 hover:bg-red-50 focus:ring-2 focus:ring-red-500'
                            : 'text-neutral-400 cursor-not-allowed'
                            }`}
                    >
                        Remove filters
                    </button>
                    <div className="space-x-2">
                        <button
                            onClick={onClose}
                            className="px-4 py-2 rounded-lg text-sm text-neutral-600 hover:bg-neutral-100 transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-500"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={handleApply}
                            className="px-4 py-2 rounded-lg text-sm bg-neutral-800 text-white hover:bg-neutral-700 transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-500"
                        >
                            Apply ({selectedIndustries.length})
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
} 