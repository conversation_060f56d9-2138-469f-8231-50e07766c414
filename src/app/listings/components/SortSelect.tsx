'use client';

import { ChevronDown, ArrowUpDown } from 'lucide-react';

type SortOption = {
    label: string;
    value: string;
    orderBy: string;
    ascending: boolean;
};

const sortOptions: SortOption[] = [
    { label: 'Post Date (Newest First)', value: 'newest', orderBy: 'created_at', ascending: false },
    { label: 'Post Date (Oldest First)', value: 'oldest', orderBy: 'created_at', ascending: true },
    { label: 'Price (Low to High)', value: 'price_asc', orderBy: 'price', ascending: true },
    { label: 'Price (High to Low)', value: 'price_desc', orderBy: 'price', ascending: false },
    { label: 'Title (A to Z)', value: 'title_asc', orderBy: 'title', ascending: true },
    { label: 'Title (Z to A)', value: 'title_desc', orderBy: 'title', ascending: false },
];

export function SortSelect({ currentSort }: { currentSort: string }) {
    return (
        <div className="relative">
            <div className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-900 bg-white border rounded-lg">
                <ArrowUpDown size={20} />
                <span>Sort:</span>
                <select
                    className="bg-transparent border-none outline-none appearance-none cursor-pointer pr-6"
                    defaultValue={currentSort}
                    onChange={(e) => {
                        const url = new URL(window.location.href);
                        url.searchParams.set('sort', e.target.value);
                        window.location.href = url.toString();
                    }}
                >
                    {sortOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                            {option.label}
                        </option>
                    ))}
                </select>
                <ChevronDown className="absolute right-2 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-500 pointer-events-none" />
            </div>
        </div>
    );
} 