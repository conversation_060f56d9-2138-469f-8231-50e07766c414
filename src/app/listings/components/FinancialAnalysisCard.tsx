'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Database } from '@/types/supabase';
import FinancialSummary from './FinancialSummary';
import { FileText, Lock, TrendingUp, BarChart3, Table } from 'lucide-react';
import Link from 'next/link';

type DataRoomFile = Database['public']['Tables']['data_room_files']['Row'];

interface FinancialAnalysisCardProps {
    listingId: string;
    className?: string;
}

export default function FinancialAnalysisCard({ listingId, className = '' }: FinancialAnalysisCardProps) {
    const [files, setFiles] = useState<DataRoomFile[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [hasAccess, setHasAccess] = useState(false);
    const [viewMode, setViewMode] = useState<'chart' | 'table'>('table');
    const supabase = createClient();

    useEffect(() => {
        const fetchFiles = async () => {
            setIsLoading(true);
            setError(null);

            try {
                // Check if user is authenticated
                const { data: { user } } = await supabase.auth.getUser();

                if (!user) {
                    // Only fetch public files for non-authenticated users
                    const { data: publicFiles, error: filesError } = await supabase
                        .from('data_room_files')
                        .select('*')
                        .eq('listing_id', listingId)
                        .eq('is_public', true)
                        .order('created_at', { ascending: false });

                    if (filesError) throw filesError;
                    setFiles(publicFiles || []);
                    setHasAccess(false);
                } else {
                    // Check if user is owner
                    const { data: listing } = await supabase
                        .from('listings')
                        .select('user_id')
                        .eq('id', listingId)
                        .single();

                    const isOwner = listing && user.id === listing.user_id;

                    if (isOwner) {
                        // Owner can see all files
                        const { data: allFiles, error: filesError } = await supabase
                            .from('data_room_files')
                            .select('*')
                            .eq('listing_id', listingId)
                            .order('created_at', { ascending: false });

                        if (filesError) throw filesError;
                        setFiles(allFiles || []);
                        setHasAccess(true);
                    } else {
                        // Check if user has access to private files
                        const { data: accessData } = await supabase
                            .from('data_room_access')
                            .select('id')
                            .eq('listing_id', listingId)
                            .eq('user_id', user.id)
                            .maybeSingle();

                        if (accessData) {
                            // User has access - fetch all files
                            const { data: allFiles, error: filesError } = await supabase
                                .from('data_room_files')
                                .select('*')
                                .eq('listing_id', listingId)
                                .order('created_at', { ascending: false });

                            if (filesError) throw filesError;
                            setFiles(allFiles || []);
                            setHasAccess(true);
                        } else {
                            // User has no access - only public files
                            const { data: publicFiles, error: filesError } = await supabase
                                .from('data_room_files')
                                .select('*')
                                .eq('listing_id', listingId)
                                .eq('is_public', true)
                                .order('created_at', { ascending: false });

                            if (filesError) throw filesError;
                            setFiles(publicFiles || []);
                            setHasAccess(false);
                        }
                    }
                }
            } catch (err) {
                console.error('Error fetching financial data:', err);
                setError(err instanceof Error ? err.message : 'Failed to load financial data');
                setFiles([]);
            } finally {
                setIsLoading(false);
            }
        };

        fetchFiles();
    }, [listingId, supabase]);

    // Check if there are any profit & loss files for financial analysis
    const profitLossFiles = files.filter(file => file.category === 'profit_loss');
    const hasFinancialData = profitLossFiles.length > 0;

    if (isLoading) {
        return (
            <div className={`bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 ${className}`}>
                <div className="flex items-center space-x-3 mb-6">
                    <div className="p-2 bg-blue-50 rounded-lg">
                        <TrendingUp className="w-5 h-5 text-blue-600" />
                    </div>
                    <h2 className="text-xl font-semibold text-gray-900">Financial Analysis</h2>
                </div>
                <div className="flex justify-center items-center h-40">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span className="ml-2 text-gray-600">Loading financial data...</span>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={`bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 ${className}`}>
                <div className="flex items-center space-x-3 mb-6">
                    <div className="p-2 bg-red-50 rounded-lg">
                        <TrendingUp className="w-5 h-5 text-red-600" />
                    </div>
                    <h2 className="text-xl font-semibold text-gray-900">Financial Analysis</h2>
                </div>
                <div className="text-center p-6 bg-red-50 rounded-lg border border-red-200">
                    <p className="text-red-700">{error}</p>
                </div>
            </div>
        );
    }

    if (!hasFinancialData) {
        return (
            <div className={`bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 ${className}`}>
                <div className="flex items-center space-x-3 mb-6">
                    <div className="p-2 bg-gray-50 rounded-lg">
                        <TrendingUp className="w-5 h-5 text-gray-600" />
                    </div>
                    <h2 className="text-xl font-semibold text-gray-900">Financial Analysis</h2>
                </div>
                <div className="text-center p-8 bg-gray-50 rounded-lg border border-gray-200">
                    <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Financial Data Available</h3>
                    <p className="text-gray-600 mb-4">
                        Financial analysis requires profit & loss statements to be uploaded to the data room.
                    </p>
                    {!hasAccess && (
                        <div className="flex items-center justify-center space-x-2 text-sm text-gray-500 mb-4">
                            <Lock className="w-4 h-4" />
                            <span>Some financial documents may be private</span>
                        </div>
                    )}
                    <Link
                        href={`/listings/${listingId}/data-room`}
                        className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        <FileText className="w-4 h-4" />
                        <span>View Data Room</span>
                    </Link>
                </div>
            </div>
        );
    }

    return (
        <div className={`bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 ${className}`}>
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-50 rounded-lg">
                        <TrendingUp className="w-5 h-5 text-blue-600" />
                    </div>
                    <div className="flex-1">
                        <h2 className="text-xl font-semibold text-gray-900">Financial Analysis</h2>
                        <p className="text-sm text-gray-600">
                            Based on {profitLossFiles.length} profit & loss statement{profitLossFiles.length !== 1 ? 's' : ''}
                        </p>
                    </div>
                </div>

                <div className="flex items-center space-x-4">
                    {/* View Mode Switcher */}
                    <div className="flex items-center space-x-1 p-1 bg-gray-100/80 rounded-lg border border-gray-200/50">
                        <button
                            onClick={() => setViewMode('chart')}
                            className={`flex items-center space-x-1.5 px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200 ${viewMode === 'chart'
                                ? 'bg-white text-gray-900 shadow-sm border border-gray-200/50'
                                : 'text-gray-600 hover:text-gray-900 hover:bg-white/60'
                                }`}
                        >
                            <BarChart3 className="w-4 h-4" />
                            <span>Chart</span>
                        </button>
                        <button
                            onClick={() => setViewMode('table')}
                            className={`flex items-center space-x-1.5 px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200 ${viewMode === 'table'
                                ? 'bg-white text-gray-900 shadow-sm border border-gray-200/50'
                                : 'text-gray-600 hover:text-gray-900 hover:bg-white/60'
                                }`}
                        >
                            <Table className="w-4 h-4" />
                            <span>Table</span>
                        </button>
                    </div>
                </div>
            </div>

            <FinancialSummary files={files} viewMode={viewMode} listingId={listingId} />
        </div>
    );
} 