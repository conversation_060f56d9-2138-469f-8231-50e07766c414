'use client';

import { useViewMode } from '@/contexts/ViewModeContext';

interface ListingTitleProps {
    realTitle: string;
    anonymousTitle: string | null;
}

export default function ListingTitle({ realTitle, anonymousTitle }: ListingTitleProps) {
    const { isPublicView } = useViewMode();

    const displayTitle = isPublicView && anonymousTitle
        ? anonymousTitle
        : realTitle;

    return (
        <h1 className="text-3xl font-bold mb-2 text-white">
            {displayTitle}
        </h1>
    );
} 