'use client';

import { Industry } from '@/config/industries';
import { Filter } from 'lucide-react';
import { useState } from 'react';
import { FilterModal } from './FilterModal';

interface FilterButtonProps {
    currentIndustries?: Industry[];
}

export function FilterButton({ currentIndustries }: FilterButtonProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);

    return (
        <>
            <button
                onClick={() => setIsModalOpen(true)}
                className="flex items-center gap-2 px-4 py-2 bg-white border rounded-lg text-sm hover:bg-gray-50"
            >
                <Filter size={20} />
                <span>Filter</span>
                {currentIndustries?.length ? (
                    <span className="px-2 py-0.5 bg-neutral-100 rounded-full text-xs">
                        {currentIndustries.length}
                    </span>
                ) : null}
            </button>

            <FilterModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                currentIndustries={currentIndustries}
            />
        </>
    );
} 