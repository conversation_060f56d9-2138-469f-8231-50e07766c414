'use client';

import { Share2, MessageSquare, Archive } from 'lucide-react';
import { SaveListingButton } from './SaveListingButton';
import { useEffect, useState } from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import { NewMessageModal } from '@/components';
import { Interest } from '@/types/listing';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';

interface InterestCardProps {
    interest: Interest;
}

export default function InterestCard({ interest }: InterestCardProps) {
    const [isOwnListing, setIsOwnListing] = useState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
    const supabase = useSupabase();
    const router = useRouter();

    useEffect(() => {
        const checkOwnership = async () => {
            try {
                const { data: { session } } = await supabase.auth.getSession();
                if (session?.user && interest.userId) {
                    // Check if user owns the listing
                    setIsOwnListing(session.user.id === interest.userId);
                }
            } catch (error) {
                console.error('Error in checkOwnership:', error);
            }
        };

        checkOwnership();
    }, [supabase, interest.userId]);

    const handleNewMessage = async () => {
        const supabase = createClient();
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.user?.id) return;

        // Create conversation if it doesn't exist
        const participants = [session.user.id, interest.userId].sort();
        const { data: existingConv } = await supabase
            .from('conversations')
            .select('id')
            .eq('listing_id', interest.listingId)
            .eq('participant1_id', participants[0])
            .eq('participant2_id', participants[1])
            .single();

        let conversationId;

        if (!existingConv) {
            const { data: newConv } = await supabase
                .from('conversations')
                .insert({
                    listing_id: interest.listingId,
                    participant1_id: participants[0],
                    participant2_id: participants[1]
                })
                .select('id')
                .single();

            conversationId = newConv?.id;
        } else {
            conversationId = existingConv.id;
        }

        setIsModalOpen(true);
        // Pass the conversation ID to the modal
        setCurrentConversationId(conversationId);
    };

    // In the owner's view, add the Data Room button
    if (isOwnListing) {
        return (
            <div className="bg-white rounded-lg shadow-lg overflow-hidden sticky top-6 border border-blue-100 ring-1 ring-blue-100/50">
                <div className="p-6 space-y-4">
                    <h2 className="text-xl font-semibold text-gray-900">Your Business Listing</h2>
                    <p className="text-sm text-gray-600">
                        This is your listing, posted on {new Date(interest.createdAt).toLocaleDateString()}.
                    </p>
                    <button
                        onClick={() => router.push(`/listings/${interest.listingId}/data-room`)}
                        className="flex items-center justify-center gap-2 w-full px-4 py-2 bg-neutral-800 text-white rounded-lg hover:bg-neutral-700 transition-colors"
                    >
                        <Archive className="w-5 h-5" />
                        Data Room
                    </button>
                    <button className="flex items-center justify-center gap-2 w-full px-4 py-2 bg-neutral-100 text-neutral-700 rounded-lg hover:bg-neutral-200">
                        <Share2 className="w-5 h-5" />
                        Share Listing
                    </button>
                </div>
            </div>
        );
    }

    return (
        <>
            <div className="bg-white rounded-lg shadow-lg overflow-hidden sticky top-6 border border-blue-100 ring-1 ring-blue-100/50">
                <div className="p-6 space-y-4">
                    <h2 className="text-xl font-semibold text-gray-900">Interested in this business?</h2>

                    <button
                        onClick={handleNewMessage}
                        className="flex items-center justify-center gap-2 w-full px-4 py-2 bg-neutral-800 text-white rounded-lg hover:bg-neutral-700 transition-colors"
                    >
                        <MessageSquare className="w-5 h-5" />
                        Send Message
                    </button>

                    <button
                        onClick={() => router.push(`/listings/${interest.listingId}/data-room`)}
                        className={`flex items-center justify-center gap-2 w-full px-4 py-2 rounded-lg transition-colors bg-neutral-800 text-white hover:bg-neutral-700`}
                    >
                        <Archive className="w-5 h-5" />
                        Data Room
                    </button>

                    <div className="space-y-3">
                        <SaveListingButton
                            listingId={interest.listingId}
                            userId={interest.userId}
                            key={interest.listingId}
                            className="flex items-center justify-center gap-2 w-full px-4 py-2 bg-neutral-100 text-neutral-700 rounded-lg hover:bg-neutral-200"
                        />

                        <button className="flex items-center justify-center gap-2 w-full px-4 py-2 bg-neutral-100 text-neutral-700 rounded-lg hover:bg-neutral-200">
                            <Share2 className="w-5 h-5" />
                            Share Listing
                        </button>
                    </div>
                </div>
            </div>

            <NewMessageModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                recipientId={interest.userId}
                listingId={interest.listingId}
                ownerName={{
                    firstName: interest.listing.user.profile.first_name,
                    lastName: interest.listing.user.profile.last_name
                }}
                ownerAvatar={interest.listing.user.profile.profile_photo}
                listingName={interest.listing.title}
                conversationId={currentConversationId || undefined}
            />
        </>
    );
} 