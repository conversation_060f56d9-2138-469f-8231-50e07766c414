import { Globe, ExternalLink } from 'lucide-react';

interface WebsiteCardProps {
    website: string | null | undefined;
}

const WebsiteCard: React.FC<WebsiteCardProps> = ({ website }) => {
    if (!website) return null;

    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
            <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-purple-50 rounded-lg">
                    <Globe className="w-5 h-5 text-purple-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">Online Presence</h2>
            </div>
            <a
                href={website}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center space-x-2 px-4 py-2 bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 text-blue-600 hover:text-blue-700 transition-colors"
            >
                <ExternalLink className="w-4 h-4" />
                <span className="text-sm font-medium truncate max-w-xs">{website}</span>
            </a>
        </div>
    );
};

export default WebsiteCard; 