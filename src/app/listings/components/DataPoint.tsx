import * as TooltipPrimitive from '@radix-ui/react-tooltip';
import { Info, Link2 } from 'lucide-react';

// Helper function to format numbers
export const formatNumber = (num: number | null | undefined) => {
    if (num === null || num === undefined) return 'N/A';
    return num.toLocaleString();
};

interface DataPointProps {
    icon: string;
    label: string;
    value: string | React.ReactNode;
    growthRate?: number | null;
    growthRatePeriod?: string;
    previousValue?: number | null;
    previousValueLabel?: string;
    growthDataSource?: string;
    infoLink?: string;
}

const DataPoint: React.FC<DataPointProps> = ({
    icon: emoji,
    label,
    value,
    growthRate,
    growthRatePeriod,
    previousValue,
    previousValueLabel,
    growthDataSource,
    infoLink
}) => (
    <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm flex flex-col items-center text-center h-full relative">
        {/* Info link icon */}
        {infoLink && (
            <a
                href={infoLink}
                target="_blank"
                rel="noopener noreferrer"
                className="absolute top-2 right-2 text-gray-400 hover:text-blue-600 transition-colors"
                aria-label="More information"
            >
                <Info size={16} />
            </a>
        )}
        {/* Main content area - ensure vertical stacking */}
        <div className="flex flex-col items-center flex-grow justify-center">
            <span className="text-2xl mb-2">
                {emoji}
            </span>
            <span className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">
                {label}
            </span>
            {/* Conditionally wrap value with TooltipTrigger if it's for description-only tooltip (e.g., walkability) */}
            {(growthRate === null || growthRate === undefined) && growthRatePeriod ? (
                <TooltipPrimitive.Provider>
                    <TooltipPrimitive.Root delayDuration={100}>
                        <TooltipPrimitive.Trigger asChild>
                            <p className="text-2xl font-bold text-gray-900 cursor-default">
                                {value}
                            </p>
                        </TooltipPrimitive.Trigger>
                        <TooltipPrimitive.Portal>
                            <TooltipPrimitive.Content
                                sideOffset={5}
                                className="z-50 overflow-hidden rounded-md bg-gray-900 px-3 py-1.5 text-xs text-gray-50 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:bg-gray-50 dark:text-gray-900 max-w-xs text-center sm:text-left"
                            >
                                {growthRatePeriod && <div className='font-semibold'>{growthRatePeriod}</div>}
                                {growthDataSource && <div className="text-gray-400 dark:text-gray-500 mt-0.5">(Source: {growthDataSource})</div>}
                                {/* Added infoLink to tooltip content below */}
                                {infoLink && (
                                    <a
                                        href={infoLink}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-400 hover:text-blue-300 underline mt-1 block"
                                    >
                                        View full details <Link2 size={12} className="inline ml-1" />
                                    </a>
                                )}
                                <TooltipPrimitive.Arrow className="fill-gray-900 dark:fill-gray-50" />
                            </TooltipPrimitive.Content>
                        </TooltipPrimitive.Portal>
                    </TooltipPrimitive.Root>
                </TooltipPrimitive.Provider>
            ) : (
                <p className="text-2xl font-bold text-gray-900">
                    {value}
                </p>
            )}
        </div>
        {/* Growth rate section - only shown if growthRate is a number */}
        {growthRate !== null && growthRate !== undefined ? (
            <div className={`mt-2 pt-2 w-full self-end border-t border-gray-200`}>
                <TooltipPrimitive.Provider>
                    <TooltipPrimitive.Root delayDuration={100}>
                        <TooltipPrimitive.Trigger asChild>
                            <div className={`text-xs font-medium inline-flex items-center justify-center cursor-default px-1.5 py-0.5 rounded-full ${growthRate >= 0 ? 'bg-green-100' : 'bg-red-100'}`}>
                                <span className={`mr-1 ${growthRate >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                                    {growthRate >= 0 ? '📈' : '📉'}
                                </span>
                                <span className={`${growthRate >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                                    {`${growthRate >= 0 ? '+' : ''}${growthRate.toFixed(1)}%`}
                                </span>
                            </div>
                        </TooltipPrimitive.Trigger>
                        <TooltipPrimitive.Portal>
                            <TooltipPrimitive.Content
                                sideOffset={5}
                                className="z-50 overflow-hidden rounded-md bg-gray-900 px-3 py-1.5 text-xs text-gray-50 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:bg-gray-50 dark:text-gray-900 max-w-xs text-center sm:text-left"
                            >
                                {growthRatePeriod && <div>{growthRatePeriod}</div>}
                                {previousValue !== null && previousValue !== undefined && previousValueLabel && (
                                    <div className="text-gray-400 dark:text-gray-500 mt-0.5">
                                        From {formatNumber(previousValue)} {previousValueLabel}.
                                    </div>
                                )}
                                {growthDataSource && <div className="text-gray-400 dark:text-gray-500 mt-0.5">(Source: {growthDataSource})</div>}
                                <TooltipPrimitive.Arrow className="fill-gray-900 dark:fill-gray-50" />
                            </TooltipPrimitive.Content>
                        </TooltipPrimitive.Portal>
                    </TooltipPrimitive.Root>
                </TooltipPrimitive.Provider>
            </div>
        ) : null}
    </div>
);

export default DataPoint; 