'use client';

import { BookmarkPlus, BookmarkCheck } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useSavedListing } from '@/contexts/SavedListingContext';
import { useSupabase } from '@/hooks/useSupabase';

interface SaveListingButtonProps {
    listingId: string;
    userId?: string;
    variant?: 'compact' | 'full';
    className?: string;
}

export function SaveListingButton({
    listingId,
    userId,
    variant = 'full',
    className = "flex items-center justify-center gap-2 w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
}: SaveListingButtonProps) {
    const { isSaved, setIsSaved } = useSavedListing();
    const [isLoading, setIsLoading] = useState(false);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [isOwnListing, setIsOwnListing] = useState(false);
    const supabase = useSupabase();

    useEffect(() => {
        const checkOwnership = async () => {
            const { data: { session } } = await supabase.auth.getSession();
            console.log('Checking ownership:', {
                sessionUserId: session?.user?.id,
                listingUserId: userId,
                isMatch: session?.user?.id === userId
            });

            if (session?.user && userId) {
                const isOwner = session.user.id === userId;
                console.log('Setting isOwnListing to:', isOwner);
                setIsOwnListing(isOwner);
            }
        };

        checkOwnership();
    }, [supabase, userId]);

    useEffect(() => {
        const checkIfSaved = async () => {
            const { data: { session } } = await supabase.auth.getSession();

            // If no authenticated user, we know it's not saved
            if (!session?.user) {
                setIsSaved(false);
                return;
            }

            const { data, error } = await supabase
                .from('saved_listings')
                .select('listing_id')
                .eq('listing_id', listingId)
                .eq('user_id', session.user.id);

            if (error) {
                console.error('Error checking saved status:', error);
                return;
            }

            setIsSaved(data.length > 0);
        };

        checkIfSaved();
    }, [listingId, supabase, setIsSaved]);

    const handleRemove = async () => {
        setIsLoading(true);
        try {
            const { data: { session } } = await supabase.auth.getSession();

            const { error } = await supabase
                .from('saved_listings')
                .delete()
                .match({
                    listing_id: listingId,
                    user_id: session!.user.id
                });

            if (error) throw error;
            setIsSaved(false);
        } catch (error) {
            console.error('Error removing listing:', error);
        } finally {
            setIsLoading(false);
            setShowConfirmModal(false);
        }
    };

    const handleSave = async () => {
        if (!listingId) {
            console.error('No listingId provided');
            return;
        }

        if (isSaved) {
            setShowConfirmModal(true);
            return;
        }

        setIsLoading(true);

        try {
            const { data: { session } } = await supabase.auth.getSession();

            if (!session?.user) {
                // Redirect to login or show login modal
                window.location.href = '/login';
                return;
            }

            const { error } = await supabase
                .from('saved_listings')
                .insert({
                    listing_id: listingId,
                    user_id: session.user.id
                });

            if (error) throw error;
            setIsSaved(true);
        } catch (error) {
            console.error('Error saving listing:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // If it's the user's own listing, don't render anything
    if (isOwnListing) {
        return null;
    }

    return (
        <>
            <button
                onClick={handleSave}
                disabled={isLoading}
                className={className}
            >
                {isSaved ? (
                    <BookmarkCheck className="w-5 h-5" />
                ) : (
                    <BookmarkPlus className="w-5 h-5" />
                )}
                {isSaved ? 'Saved' : variant === 'compact' ? 'Save' : 'Save Listing'}
            </button>

            {/* Confirmation Modal */}
            {showConfirmModal && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-sm mx-4 w-full">
                        <h3 className="text-lg font-semibold mb-4">Remove from Saved</h3>
                        <p className="text-gray-600 mb-6">
                            Are you sure you want to remove this listing from your saved items?
                        </p>
                        <div className="flex gap-3">
                            <button
                                onClick={() => setShowConfirmModal(false)}
                                className="flex-1 px-4 py-2 border border-neutral-300 rounded-lg hover:bg-neutral-50 transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-500"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleRemove}
                                disabled={isLoading}
                                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
                            >
                                Remove
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}