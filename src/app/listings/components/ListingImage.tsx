'use client';

import Image from 'next/image';
import { useViewMode } from '@/contexts/ViewModeContext';

interface ListingImageProps {
    realImageUrl: string | null;
    anonymousImageUrl: string | null;
    title: string;
}

export default function ListingImage({ realImageUrl, anonymousImageUrl, title }: ListingImageProps) {
    const { isPublicView } = useViewMode();

    const displayImageUrl = isPublicView && anonymousImageUrl
        ? anonymousImageUrl
        : realImageUrl;

    if (!displayImageUrl) {
        return (
            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                <span className="text-gray-400">No image</span>
            </div>
        );
    }

    return (
        <>
            <Image
                src={displayImageUrl}
                alt={title}
                className="w-full h-full object-cover"
                width={1200}
                height={300}
            />
            {/* Noise overlay */}
            <div
                className="absolute inset-0 opacity-10 mix-blend-multiply"
                style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%' height='100%' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
                    backgroundRepeat: 'repeat',
                }}
            />
            {/* Dark overlay */}
            <div className="absolute inset-0 bg-black/30" />
        </>
    );
} 