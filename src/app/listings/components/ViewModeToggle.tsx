'use client';

import { useViewMode } from '@/contexts/ViewModeContext';
import { <PERSON>, EyeOff, Settings } from 'lucide-react';

export default function ViewModeToggle() {
    const { isPublicView, toggleViewMode } = useViewMode();

    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
            <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-indigo-50 rounded-lg">
                    <Settings className="w-5 h-5 text-indigo-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">Preview Mode</h2>
            </div>

            <div className="space-y-4">
                <div className="flex items-center gap-2 bg-gray-50 p-2 rounded-lg border border-gray-200">
                    <button
                        onClick={() => toggleViewMode(false)}
                        className={`flex-1 px-4 py-2 rounded-md transition-all duration-200 flex items-center justify-center gap-2 text-sm font-medium ${!isPublicView
                            ? 'bg-white shadow-sm border border-gray-200 text-gray-900'
                            : 'text-gray-600 hover:text-gray-900 hover:bg-white/60'
                            }`}
                    >
                        <EyeOff size={16} />
                        Owner View
                    </button>
                    <button
                        onClick={() => toggleViewMode(true)}
                        className={`flex-1 px-4 py-2 rounded-md transition-all duration-200 flex items-center justify-center gap-2 text-sm font-medium ${isPublicView
                            ? 'bg-white shadow-sm border border-gray-200 text-gray-900'
                            : 'text-gray-600 hover:text-gray-900 hover:bg-white/60'
                            }`}
                    >
                        <Eye size={16} />
                        Buyer View
                    </button>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <p className="text-sm text-blue-800">
                        {isPublicView
                            ? "🔍 You're previewing your listing as potential buyers see it. Private information and owner-only features are hidden."
                            : "👤 You're viewing your listing with all owner details and private information visible."
                        }
                    </p>
                </div>
            </div>
        </div>
    );
} 