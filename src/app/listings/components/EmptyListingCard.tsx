'use client';

import { Store } from 'lucide-react';
import { useRouter } from 'next/navigation';

export function EmptyListingCard() {
    const router = useRouter();

    const handleAddListing = () => {
        router.push('/listings/add-listing');
    };

    return (
        <div className="w-full bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="flex flex-col items-center max-w-md mx-auto">
                <Store className="w-16 h-16 text-gray-400 mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">List Your First Business!</h3>
                <p className="text-gray-600 mb-6">
                    Ready to sell your business? Create your first listing and reach thousands of potential buyers.
                    It only takes a few minutes to get started.
                </p>
                <button
                    onClick={handleAddListing}
                    className="bg-neutral-800 text-white px-6 py-2 rounded-lg hover:bg-neutral-700 transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-500"
                >
                    List Your Business
                </button>
            </div>
        </div>
    );
}