import { HelpCircle, User2 } from 'lucide-react';
import Image from 'next/image';

interface ReasonForSellingCardProps {
    reasonForSelling: string | null;
    sellerProfile: {
        first_name: string | null;
        last_name: string | null;
        profile_photo?: string | null;
    } | null;
    businessTitle: string;
}

const ReasonForSellingCard: React.FC<ReasonForSellingCardProps> = ({
    reasonForSelling,
    sellerProfile,
    businessTitle
}) => {
    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
            <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-amber-50 rounded-lg">
                    <HelpCircle className="w-5 h-5 text-amber-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">Reason for Selling</h2>
            </div>

            <div className="space-y-6">
                {/* Message bubble */}
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-xl relative shadow-sm">
                    <p className="text-white text-sm leading-relaxed">
                        {reasonForSelling || 'The seller has not shared their reason for selling at this time.'}
                    </p>
                    {/* Triangle pointer */}
                    <div className="absolute bottom-0 left-6 transform translate-y-full">
                        <div className="w-4 h-4 bg-blue-500 transform rotate-45 -translate-y-2" />
                    </div>
                </div>

                {/* Profile section */}
                <div className="flex items-center gap-4 pl-2">
                    {sellerProfile?.profile_photo ? (
                        <Image
                            src={sellerProfile.profile_photo}
                            alt={`${sellerProfile?.first_name || 'Anonymous'}'s profile`}
                            width={48}
                            height={48}
                            className="rounded-full object-cover ring-2 ring-gray-100"
                        />
                    ) : (
                        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center ring-2 ring-gray-100">
                            <User2 className="w-6 h-6 text-gray-500" />
                        </div>
                    )}
                    <div>
                        <div className="font-medium text-gray-900 text-sm">
                            {sellerProfile?.first_name || 'Anonymous'} {sellerProfile?.last_name || ''}
                        </div>
                        <div className="text-xs text-gray-500">
                            Business Owner of <span className="font-medium text-gray-700">{businessTitle}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ReasonForSellingCard; 