import * as TooltipPrimitive from '@radix-ui/react-tooltip';
import { Info, MapPin } from 'lucide-react';
import { PriceFormatter } from '@/components';
import DataPoint, { formatNumber } from './DataPoint';

// Types for the component props
export interface AreaInsightsProps {
    areaInsights: {
        city: string;
        state: string;
        population: number | null;
        isPopulationMock?: boolean;
        medianIncome: number | null;
        isMedianIncomeMock?: boolean;
        unemploymentRate: number | null;
        isUnemploymentRateMock?: boolean;
        numBusinesses: number | null;
        isNumBusinessesMock?: boolean;
        crimeRate: string;
        isCrimeStatsMock?: boolean;
        schools: number | null;
        isSchoolCountMock?: boolean;
        populationCAGR: number | null | undefined;
        isPopulationCAGRMock?: boolean;
        medianAge: number | null | undefined;
        isMedianAgeMock?: boolean;
        educationBachelorPlusPercent: number | null | undefined;
        isEducationBachelorPlusPercentMock?: boolean;
    };
    similarCompaniesData: {
        numEstablishments?: number | null;
        totalAnnualPayroll?: number | null;
        totalEmployment?: number | null;
        averageAnnualWage?: number | null;
        countyName?: string | null;
        naicsDescription?: string | null;
        numEstablishmentsPreviousYear?: number | null;
        establishmentGrowthRate?: number | null;
        totalSalesReceipts?: number | null;
        avgSalesPerEstablishment?: number | null;
        isSalesDataMock?: boolean;
    };
    walkScoreData: {
        walkScore?: number | null;
        isWalkScoreMock?: boolean;
        walkDescription?: string | null;
        transitScore?: number | null;
        isTransitScoreMock?: boolean;
        transitDescription?: string | null;
        bikeScore?: number | null;
        isBikeScoreMock?: boolean;
        bikeDescription?: string | null;
        wsLink?: string | null;
    };
}

const AreaInsights: React.FC<AreaInsightsProps> = ({
    areaInsights,
    similarCompaniesData,
    walkScoreData
}) => {
    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
            {/* Main Title for the combined section */}
            <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-teal-50 rounded-lg">
                    <MapPin className="w-5 h-5 text-teal-600" />
                </div>
                <div className="flex-1 flex justify-between items-center">
                    <h2 className="text-xl font-semibold text-gray-900">About this area</h2>
                    {similarCompaniesData.countyName && (
                        <span className="ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                            {similarCompaniesData.countyName}
                        </span>
                    )}
                </div>
            </div>

            {/* Neighbourhood Sub-section */}
            <h3 className="text-lg font-semibold text-gray-700 mb-4">Neighbourhood</h3>
            <TooltipPrimitive.Provider> {/* SINGLE Provider for the whole grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    {/* Population Tile */}
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm flex flex-col items-center text-center">
                        <span className="text-2xl mb-2">👥</span>
                        <span className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Local Population</span>
                        <TooltipPrimitive.Root delayDuration={100}>
                            <TooltipPrimitive.Trigger asChild>
                                <p className="text-2xl font-bold text-gray-900 cursor-default">
                                    {areaInsights.population?.toLocaleString() || 'N/A'}
                                </p>
                            </TooltipPrimitive.Trigger>
                            <TooltipPrimitive.Portal>
                                <TooltipPrimitive.Content sideOffset={5} className="z-50 overflow-hidden rounded-md bg-gray-900 px-3 py-1.5 text-xs text-gray-50 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:bg-gray-50 dark:text-gray-900 max-w-xs text-center sm:text-left">
                                    <div>Medium-sized town{areaInsights.isPopulationMock ? ' (mock data)' : ''}</div>
                                    <TooltipPrimitive.Arrow className="fill-gray-900 dark:fill-gray-50" />
                                </TooltipPrimitive.Content>
                            </TooltipPrimitive.Portal>
                        </TooltipPrimitive.Root>
                        {areaInsights.populationCAGR !== null && areaInsights.populationCAGR !== undefined && (
                            <div className="mt-2 pt-2 border-t border-gray-200 w-full self-end">
                                <TooltipPrimitive.Root delayDuration={100}>
                                    <TooltipPrimitive.Trigger asChild>
                                        <div className={`text-xs font-medium inline-flex items-center justify-center cursor-default px-1.5 py-0.5 rounded-full ${areaInsights.populationCAGR >= 0 ? 'bg-green-100' : 'bg-red-100'}`}>
                                            <span className={`mr-1 ${areaInsights.populationCAGR >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                                                {areaInsights.populationCAGR >= 0 ? '📈' : '📉'}
                                            </span>
                                            <span className={`${areaInsights.populationCAGR >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                                                {`${areaInsights.populationCAGR >= 0 ? '+' : ''}${areaInsights.populationCAGR.toFixed(2)}%`}
                                            </span>
                                        </div>
                                    </TooltipPrimitive.Trigger>
                                    <TooltipPrimitive.Portal>
                                        <TooltipPrimitive.Content sideOffset={5} className="z-50 overflow-hidden rounded-md bg-gray-900 px-3 py-1.5 text-xs text-gray-50 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:bg-gray-50 dark:text-gray-900 max-w-xs text-center sm:text-left">
                                            <div>The displayed percentage is the 5-year Compound Annual Growth Rate (CAGR) for the county&apos;s population.</div>
                                            {areaInsights.isPopulationCAGRMock ? <div className="text-gray-400 dark:text-gray-500 mt-0.5">(Source: Mock Data)</div> : <div className="text-gray-400 dark:text-gray-500 mt-0.5">(Source: U.S. Census ACS, 5-yr estimate)</div>}
                                            <TooltipPrimitive.Arrow className="fill-gray-900 dark:fill-gray-50" />
                                        </TooltipPrimitive.Content>
                                    </TooltipPrimitive.Portal>
                                </TooltipPrimitive.Root>
                            </div>
                        )}
                    </div>

                    {/* Median Income Tile */}
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm flex flex-col items-center text-center">
                        <span className="text-2xl mb-2">💰</span>
                        <span className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Median Household Income</span>
                        <TooltipPrimitive.Root delayDuration={100}>
                            <TooltipPrimitive.Trigger asChild>
                                <p className="text-2xl font-bold text-gray-900 cursor-default">
                                    ${areaInsights.medianIncome?.toLocaleString() || 'N/A'}
                                </p>
                            </TooltipPrimitive.Trigger>
                            <TooltipPrimitive.Portal>
                                <TooltipPrimitive.Content sideOffset={5} className="z-50 overflow-hidden rounded-md bg-gray-900 px-3 py-1.5 text-xs text-gray-50 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:bg-gray-50 dark:text-gray-900 max-w-xs text-center sm:text-left">
                                    32% above U.S. average{areaInsights.isMedianIncomeMock ? ' (mock data)' : ''}
                                    <TooltipPrimitive.Arrow className="fill-gray-900 dark:fill-gray-50" />
                                </TooltipPrimitive.Content>
                            </TooltipPrimitive.Portal>
                        </TooltipPrimitive.Root>
                    </div>

                    {/* Unemployment Tile */}
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm flex flex-col items-center text-center">
                        <span className="text-2xl mb-2">📉</span>
                        <span className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Job Market (Unemployment)</span>
                        <TooltipPrimitive.Root delayDuration={100}>
                            <TooltipPrimitive.Trigger asChild>
                                <p className="text-2xl font-bold text-gray-900 cursor-default">
                                    {areaInsights.unemploymentRate?.toString() || 'N/A'}%
                                </p>
                            </TooltipPrimitive.Trigger>
                            <TooltipPrimitive.Portal>
                                <TooltipPrimitive.Content sideOffset={5} className="z-50 overflow-hidden rounded-md bg-gray-900 px-3 py-1.5 text-xs text-gray-50 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:bg-gray-50 dark:text-gray-900 max-w-xs text-center sm:text-left">
                                    Lower than national rate{areaInsights.isUnemploymentRateMock ? ' (mock data)' : ''}
                                    <TooltipPrimitive.Arrow className="fill-gray-900 dark:fill-gray-50" />
                                </TooltipPrimitive.Content>
                            </TooltipPrimitive.Portal>
                        </TooltipPrimitive.Root>
                    </div>

                    {/* Businesses Tile */}
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm flex flex-col items-center text-center">
                        <span className="text-2xl mb-2">🏢</span>
                        <span className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Active Local Businesses</span>
                        <TooltipPrimitive.Root delayDuration={100}>
                            <TooltipPrimitive.Trigger asChild>
                                <p className="text-2xl font-bold text-gray-900 cursor-default">
                                    {areaInsights.numBusinesses?.toLocaleString() || 'N/A'}
                                </p>
                            </TooltipPrimitive.Trigger>
                            <TooltipPrimitive.Portal>
                                <TooltipPrimitive.Content sideOffset={5} className="z-50 overflow-hidden rounded-md bg-gray-900 px-3 py-1.5 text-xs text-gray-50 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:bg-gray-50 dark:text-gray-900 max-w-xs text-center sm:text-left">
                                    Active local economy{areaInsights.isNumBusinessesMock ? ' (mock data)' : ''}
                                    <TooltipPrimitive.Arrow className="fill-gray-900 dark:fill-gray-50" />
                                </TooltipPrimitive.Content>
                            </TooltipPrimitive.Portal>
                        </TooltipPrimitive.Root>
                    </div>

                    {/* Crime Tile */}
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm flex flex-col items-center text-center">
                        <span className="text-2xl mb-2">🚨</span>
                        <span className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Crime Risk Level</span>
                        <TooltipPrimitive.Root delayDuration={100}>
                            <TooltipPrimitive.Trigger asChild>
                                <p className="text-2xl font-bold text-gray-900 cursor-default">
                                    {areaInsights.crimeRate}
                                </p>
                            </TooltipPrimitive.Trigger>
                            <TooltipPrimitive.Portal>
                                <TooltipPrimitive.Content sideOffset={5} className="z-50 overflow-hidden rounded-md bg-gray-900 px-3 py-1.5 text-xs text-gray-50 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:bg-gray-50 dark:text-gray-900 max-w-xs text-center sm:text-left">
                                    Insurance may be costlier{areaInsights.isCrimeStatsMock ? ' (mock data)' : ''}
                                    <TooltipPrimitive.Arrow className="fill-gray-900 dark:fill-gray-50" />
                                </TooltipPrimitive.Content>
                            </TooltipPrimitive.Portal>
                        </TooltipPrimitive.Root>
                    </div>

                    {/* Schools Tile */}
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm flex flex-col items-center text-center">
                        <span className="text-2xl mb-2">🎓</span>
                        <span className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Schools Nearby</span>
                        <TooltipPrimitive.Root delayDuration={100}>
                            <TooltipPrimitive.Trigger asChild>
                                <p className="text-2xl font-bold text-gray-900 cursor-default">
                                    {areaInsights.schools?.toString() || 'N/A'}
                                </p>
                            </TooltipPrimitive.Trigger>
                            <TooltipPrimitive.Portal>
                                <TooltipPrimitive.Content sideOffset={5} className="z-50 overflow-hidden rounded-md bg-gray-900 px-3 py-1.5 text-xs text-gray-50 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:bg-gray-50 dark:text-gray-900 max-w-xs text-center sm:text-left">
                                    Talent pipeline & family traffic{areaInsights.isSchoolCountMock ? ' (mock data)' : ''}
                                    <TooltipPrimitive.Arrow className="fill-gray-900 dark:fill-gray-50" />
                                </TooltipPrimitive.Content>
                            </TooltipPrimitive.Portal>
                        </TooltipPrimitive.Root>
                    </div>

                    {/* Median Age Tile */}
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm flex flex-col items-center text-center">
                        <span className="text-2xl mb-2">🎂</span>
                        <span className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Median Resident Age</span>
                        <TooltipPrimitive.Root delayDuration={100}>
                            <TooltipPrimitive.Trigger asChild>
                                <p className="text-2xl font-bold text-gray-900 cursor-default">
                                    {areaInsights.medianAge ? areaInsights.medianAge.toFixed(1) : 'N/A'}
                                </p>
                            </TooltipPrimitive.Trigger>
                            <TooltipPrimitive.Portal>
                                <TooltipPrimitive.Content sideOffset={5} className="z-50 overflow-hidden rounded-md bg-gray-900 px-3 py-1.5 text-xs text-gray-50 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:bg-gray-50 dark:text-gray-900 max-w-xs text-center sm:text-left">
                                    Median age of the population in the county.
                                    {areaInsights.isMedianAgeMock ? <span className="text-gray-400 dark:text-gray-500"> (mock data)</span> : <span className="text-gray-400 dark:text-gray-500"> (Source: U.S. Census ACS)</span>}
                                    <TooltipPrimitive.Arrow className="fill-gray-900 dark:fill-gray-50" />
                                </TooltipPrimitive.Content>
                            </TooltipPrimitive.Portal>
                        </TooltipPrimitive.Root>
                    </div>

                    {/* Education Bachelor's+ Tile */}
                    {areaInsights.educationBachelorPlusPercent !== null && areaInsights.educationBachelorPlusPercent !== undefined && (
                        <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm flex flex-col items-center text-center">
                            <span className="text-2xl mb-2">🧑‍🎓</span>
                            <span className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Education (Bachelor&apos;s+)</span>
                            <TooltipPrimitive.Root delayDuration={100}>
                                <TooltipPrimitive.Trigger asChild>
                                    <p className="text-2xl font-bold text-gray-900 cursor-default">
                                        {areaInsights.educationBachelorPlusPercent.toFixed(1)}%
                                    </p>
                                </TooltipPrimitive.Trigger>
                                <TooltipPrimitive.Portal>
                                    <TooltipPrimitive.Content sideOffset={5} className="z-50 overflow-hidden rounded-md bg-gray-900 px-3 py-1.5 text-xs text-gray-50 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:bg-gray-50 dark:text-gray-900 max-w-xs text-center sm:text-left">
                                        Percentage of population (25 years and over) with a Bachelor&apos;s degree or higher.
                                        {areaInsights.isEducationBachelorPlusPercentMock ? <span className="text-gray-400 dark:text-gray-500"> (mock data)</span> : <span className="text-gray-400 dark:text-gray-500"> (Source: U.S. Census S1501, 2022 ACS 1-Year Est.)</span>}
                                        <TooltipPrimitive.Arrow className="fill-gray-900 dark:fill-gray-50" />
                                    </TooltipPrimitive.Content>
                                </TooltipPrimitive.Portal>
                            </TooltipPrimitive.Root>
                        </div>
                    )}

                    {/* Walk Score Tile using DataPoint component */}
                    <DataPoint
                        icon="🚶‍♂️"
                        label="Walkability Score"
                        value={walkScoreData.walkScore?.toString() || 'N/A'}
                        growthRate={null}
                        growthRatePeriod={walkScoreData.walkDescription || undefined}
                        growthDataSource={walkScoreData.isWalkScoreMock ? "Mock Data (Walk Score®)" : "Walk Score® API"}
                        infoLink={walkScoreData.wsLink || undefined}
                    />

                    {/* Transit Score Tile */}
                    {walkScoreData.transitScore !== null && walkScoreData.transitScore !== undefined && (
                        <DataPoint
                            icon="🚌"
                            label="Transit Score®"
                            value={walkScoreData.transitScore.toString()}
                            growthRate={null}
                            growthRatePeriod={walkScoreData.transitDescription || undefined}
                            growthDataSource={walkScoreData.isTransitScoreMock ? "Mock Data (Transit Score®)" : "Walk Score® API"}
                            infoLink={walkScoreData.wsLink || undefined}
                        />
                    )}

                    {/* Bike Score Tile */}
                    {walkScoreData.bikeScore !== null && walkScoreData.bikeScore !== undefined && (
                        <DataPoint
                            icon="🚴"
                            label="Bike Access Score"
                            value={walkScoreData.bikeScore.toString()}
                            growthRate={null}
                            growthRatePeriod={walkScoreData.bikeDescription || undefined}
                            growthDataSource={walkScoreData.isBikeScoreMock ? "Mock Data (Bike Score®)" : "Walk Score® API"}
                            infoLink={walkScoreData.wsLink || undefined}
                        />
                    )}
                </div>
            </TooltipPrimitive.Provider>

            {/* Divider */}
            <hr className="my-6 border-gray-200" />

            {/* Similar Businesses Sub-section */}
            <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-700">Similar Businesses</h3>
                {similarCompaniesData.naicsDescription && (
                    <span className={`ml-3 inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-cyan-100 text-cyan-700`}>
                        {similarCompaniesData.naicsDescription}
                    </span>
                )}
            </div>

            {(() => {
                const {
                    numEstablishments,
                    totalAnnualPayroll,
                    totalEmployment,
                    averageAnnualWage,
                    establishmentGrowthRate,
                    numEstablishmentsPreviousYear,
                    avgSalesPerEstablishment,
                    isSalesDataMock
                } = similarCompaniesData;

                const hasCBPData = numEstablishments !== null || totalAnnualPayroll !== null || totalEmployment !== null || averageAnnualWage !== null;
                const hasEconCensusData = avgSalesPerEstablishment !== null;

                return (
                    <>
                        {!hasCBPData && !hasEconCensusData && (
                            <div className="text-center py-8">
                                <Info className="w-10 h-10 text-gray-400 mx-auto mb-3" />
                                <p className="text-gray-600 font-medium">Industry Data Not Available</p>
                                <p className="text-sm text-gray-500 mt-1">Detailed data for this specific area and category is currently unavailable.</p>
                            </div>
                        )}
                        {(hasCBPData || hasEconCensusData) && (
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                                {/* Establishments DataPoint with Growth Rate */}
                                <div className="flex flex-col items-center">
                                    <DataPoint
                                        icon="🏢"
                                        label="# of Similar Businesses Nearby"
                                        value={formatNumber(numEstablishments)}
                                        growthRate={establishmentGrowthRate}
                                        growthRatePeriod="5-Yr Growth (2016-2021)"
                                        previousValue={numEstablishmentsPreviousYear}
                                        previousValueLabel="establishments in 2016"
                                        growthDataSource="U.S. Census CBP"
                                    />
                                </div>

                                {/* Total Employment DataPoint */}
                                <DataPoint
                                    icon="👥"
                                    label="Total Staff in Similar Businesses"
                                    value={formatNumber(totalEmployment)}
                                />

                                <DataPoint
                                    icon="💸"
                                    label={similarCompaniesData.naicsDescription ? `Avg. Wage for ${similarCompaniesData.naicsDescription} Workers` : "Avg. Worker Wage"}
                                    value={averageAnnualWage !== null && averageAnnualWage !== undefined ?
                                        <PriceFormatter price={averageAnnualWage} className="!text-xl sm:!text-2xl !font-semibold !text-gray-800 !tracking-tight" /> :
                                        'N/A'}
                                />

                                {/* Avg Sales Per Establishment DataPoint */}
                                {avgSalesPerEstablishment !== null && avgSalesPerEstablishment !== undefined && (
                                    <DataPoint
                                        icon="🧾"
                                        label="Avg. Annual Sales / Est."
                                        value={<PriceFormatter price={avgSalesPerEstablishment} className="!text-xl sm:!text-2xl !font-semibold !text-gray-800 !tracking-tight" />}
                                        growthRate={null}
                                        growthRatePeriod={isSalesDataMock ? "Mock Data (Source: 2017 Econ Census)" : "Annual average (Source: U.S. Economic Census 2017)"}
                                        growthDataSource="U.S. Economic Census 2017"
                                    />
                                )}
                            </div>
                        )}
                    </>
                );
            })()}

            {/* Data sources information */}
            <div className="mt-6 text-xs text-gray-500">
                <p>
                    Data sources:
                    <a href="https://www.data.gov" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">data.gov</a> (Area Insights, Similar Businesses),
                    <a href="https://www.walkscore.com/services/api.php" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Walk Score API</a> (Walkability).
                </p>
            </div>
        </div>
    );
};

export default AreaInsights; 