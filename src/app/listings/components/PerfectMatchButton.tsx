'use client';

import { Target } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useSupabase } from '@/hooks/useSupabase';

interface PerfectMatchButtonProps {
    listingId: string;
    listingUserId: string;
}

export function PerfectMatchButton({ listingId, listingUserId }: PerfectMatchButtonProps) {
    const [isPerfectMatch, setIsPerfectMatch] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [isOwnListing, setIsOwnListing] = useState(false);
    const supabase = useSupabase();

    useEffect(() => {
        const checkPerfectMatch = async () => {
            try {
                const { data: { session } } = await supabase.auth.getSession();

                if (!session?.user) {
                    setIsLoading(false);
                    return;
                }

                // Check if this is the user's own listing
                setIsOwnListing(listingUserId === session.user.id);

                // Only check for perfect match if it's not their own listing
                if (listingUserId !== session.user.id) {
                    // Check if this listing is a perfect match for any of the user's saved match criteria
                    const { data, error } = await supabase
                        .from('match_notifications')
                        .select('id')
                        .eq('listing_id', listingId)
                        .eq('user_id', session.user.id)
                        .maybeSingle();

                    if (error) {
                        console.error('Error checking perfect match:', error);
                        setIsPerfectMatch(false);
                    } else {
                        setIsPerfectMatch(!!data);
                    }
                }
            } catch (error) {
                console.error('Error checking perfect match status:', error);
                setIsPerfectMatch(false);
            } finally {
                setIsLoading(false);
            }
        };

        if (listingId && listingUserId) {
            checkPerfectMatch();
        }
    }, [listingId, listingUserId, supabase]);

    // Don't show anything if it's the user's own listing, still loading, or not a perfect match
    if (isOwnListing || isLoading || !isPerfectMatch) {
        return null;
    }

    return (
        <div
            className="absolute top-4 right-16 p-2 rounded-full backdrop-blur-sm transition-colors shadow-sm z-10 group cursor-pointer"
            style={{
                background: 'conic-gradient(from 0deg, #fbbf24, #f97316, #eab308, #fbbf24)'
            }}
            title="This is a perfect match!"
        >
            <Target className="w-5 h-5 text-white" style={{
                animation: 'pulse-scale 2s ease-in-out infinite'
            }} />
            {/* Tooltip - positioned below the button */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                This is a perfect match!
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-900"></div>
            </div>

            {/* Global CSS for pulse scale animation */}
            <style jsx global>{`
                @keyframes pulse-scale {
                    0%, 100% {
                        transform: scale(1);
                    }
                    50% {
                        transform: scale(1.1);
                    }
                }
            `}</style>
        </div>
    );
} 