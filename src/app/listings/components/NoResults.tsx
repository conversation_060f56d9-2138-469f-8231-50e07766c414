'use client';

import { SearchX, Search } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

export function NoResults() {
    const router = useRouter();
    const searchParams = useSearchParams();

    const handleShowAll = () => {
        router.push('/listings');
    };

    const searchQuery = searchParams.get('search');
    const industry = searchParams.get('industry');

    return (
        <div className="w-full bg-white rounded-xl shadow-sm border border-gray-200/60 p-12">
            <div className="flex flex-col items-center max-w-md mx-auto text-center">
                <div className="p-4 bg-gray-50 rounded-full mb-6">
                    <SearchX className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">No businesses found</h3>
                <p className="text-gray-600 mb-8 leading-relaxed">
                    {searchQuery || industry ? (
                        <>
                            We couldn&apos;t find any businesses matching your search criteria.
                            Try broadening your search or exploring different industries.
                        </>
                    ) : (
                        "There are currently no businesses available. Please check back later."
                    )}
                </p>
                {(searchQuery || industry) && (
                    <button
                        onClick={handleShowAll}
                        className="inline-flex items-center px-6 py-3 bg-neutral-800 text-white rounded-lg hover:bg-neutral-700 transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-500 font-medium group"
                    >
                        <Search className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                        Show all businesses
                    </button>
                )}
            </div>
        </div>
    );
} 