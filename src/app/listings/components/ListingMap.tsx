'use client'

/// <reference types="@types/google.maps" />
import { useEffect, useRef } from 'react'
import { Loader } from '@googlemaps/js-api-loader'

interface ListingMapProps {
    streetAddress?: string | null
    city?: string | null
    stateCode?: string | null
    postalCode?: string | null
    latitude?: number | null
    longitude?: number | null
    className?: string
}

export function ListingMap({
    streetAddress,
    city,
    stateCode,
    postalCode,
    className = '',
    latitude,
    longitude
}: ListingMapProps) {
    const mapRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        const initMap = async () => {
            // Check for API key first
            if (!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
                console.error('Google Maps API key is missing');
                return;
            }

            const hasLatLng = typeof latitude === 'number' && typeof longitude === 'number';
            const hasAddress = streetAddress && city && stateCode;

            if (!hasAddress && !hasLatLng) {
                console.warn('ListingMap: Missing required props (full address or latitude/longitude)');
                return;
            }

            const loader = new Loader({
                apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
                version: 'weekly',
            });

            try {
                const { Map } = await loader.importLibrary('maps') as { Map: typeof google.maps.Map };
                let geocodingAttempted = false;
                let location: google.maps.LatLngLiteral | null = null;

                // Priority 1: Geocode the address if available
                if (hasAddress) {
                    geocodingAttempted = true;
                    const { Geocoder } = await loader.importLibrary('geocoding') as { Geocoder: typeof google.maps.Geocoder };
                    const geocoder = new Geocoder();
                    const fullAddress = `${streetAddress}, ${city}, ${stateCode} ${postalCode || ''}`.trim();
                    console.log(`ListingMap: Attempting to geocode address: ${fullAddress}`);

                    try {
                        const response = await geocoder.geocode({ address: fullAddress });
                        if (response.results?.[0]) {
                            location = response.results[0].geometry.location.toJSON();
                            console.log('ListingMap: Geocoded address successfully:', location);
                        } else {
                            // Geocoding might return an empty results array if address not found
                            console.error(`ListingMap: Geocoding failed for address (no results): ${fullAddress}`);
                        }
                    } catch (geoError) {
                        console.error('ListingMap: Geocoding error:', geoError);
                    }
                }

                // Priority 2: Fallback to provided lat/lon if address geocoding wasn't possible or failed
                if (!location && hasLatLng) {
                    if (geocodingAttempted) {
                        console.warn('ListingMap: Address geocoding failed or returned no results, falling back to provided lat/lon.');
                    } else {
                        console.log('ListingMap: No address provided for geocoding, using provided lat/lon.');
                    }
                    location = { lat: latitude!, lng: longitude! }; // Use ! as hasLatLng confirmed they are numbers
                }

                // Ensure we have a location and the mapRef is available
                if (location && mapRef.current) {
                    const map = new Map(mapRef.current, {
                        center: location,
                        zoom: 15,
                        mapTypeControl: false,
                        streetViewControl: false,
                        fullscreenControl: false,
                    })

                    new google.maps.Marker({
                        position: location,
                        map: map,
                    })
                } else if (!location) {
                    console.error('Could not determine map location.');
                }
            } catch (error) {
                console.error('Error initializing map:', error)
            }
        }

        initMap()
    }, [streetAddress, city, stateCode, postalCode, latitude, longitude])

    return (
        <div
            ref={mapRef}
            className={`w-full h-[200px] rounded-md ${className}`}
            style={{ minHeight: '200px' }}
        />
    )
} 