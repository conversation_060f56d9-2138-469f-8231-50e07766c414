import { FileText } from 'lucide-react';
import ListingDescription from './ListingDescription';

interface DescriptionCardProps {
    realDescription: string;
    anonymousDescription: string | null;
}

const DescriptionCard: React.FC<DescriptionCardProps> = ({
    realDescription,
    anonymousDescription
}) => {
    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
            <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-blue-50 rounded-lg">
                    <FileText className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">Business Description</h2>
            </div>
            <ListingDescription
                realDescription={realDescription}
                anonymousDescription={anonymousDescription}
            />
        </div>
    );
};

export default DescriptionCard; 