import { DollarSign } from 'lucide-react';
import { PriceFormatter } from '@/components';

interface PriceCardProps {
    price: number;
}

const PriceCard: React.FC<PriceCardProps> = ({ price }) => {
    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
            <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-green-50 rounded-lg">
                    <DollarSign className="w-5 h-5 text-green-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">Asking Price</h2>
            </div>
            <PriceFormatter
                price={price}
                className="text-3xl font-bold text-gray-900"
            />
        </div>
    );
};

export default PriceCard; 