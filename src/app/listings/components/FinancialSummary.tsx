'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import {
    ResponsiveContainer,
    Composed<PERSON>hart,
    Bar,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
} from 'recharts';
import { useFinancialData } from '@/hooks/useFinancialData';
import { Tabs, TabList, Tab, TabPanel } from 'react-tabs';
import 'react-tabs/style/react-tabs.css';
import { Database } from '@/types/supabase';
import { PriceFormatter } from '@/components';

// Types
type DataRoomFile = Database['public']['Tables']['data_room_files']['Row'];

interface FinancialData {
    month: string;
    totalRevenue: number;
    netProfitAfterTax: number;
    netProfitMargin: number;
}

interface AnnualTotals {
    totalRevenue: number;
    totalNetProfitAfterTax: number;
    averageNetProfitMargin: number;
}

interface TooltipPayload {
    dataKey: string;
    value: number;
    color: string;
}

interface TooltipProps {
    active?: boolean;
    payload?: TooltipPayload[];
    label?: string;
}

interface FinancialSummaryProps {
    files: DataRoomFile[];
    viewMode?: 'chart' | 'table';
    listingId: string;
}

const FinancialSummary: React.FC<FinancialSummaryProps> = ({ files, viewMode = 'table', listingId }) => {
    const [expandedYears, setExpandedYears] = useState<Set<string>>(new Set());

    // Use the new hook instead of useEffect logic
    const { data: financialData, loading: isLoading, error } = useFinancialData(files);

    const toggleTableExpansion = (year: string) => {
        const newExpanded = new Set(expandedYears);
        if (newExpanded.has(year)) {
            newExpanded.delete(year);
        } else {
            newExpanded.add(year);
        }
        setExpandedYears(newExpanded);
    };

    const getCondensedData = (data: FinancialData[], year: string) => {
        const isExpanded = expandedYears.has(year);

        if (isExpanded || data.length <= 3) {
            return { rows: data, showExpander: false };
        }

        const hasQ4 = data.some(item =>
            ['December', 'Q4', 'Quarter 4', '12'].some(term =>
                item.month.toLowerCase().includes(term.toLowerCase())
            )
        );

        if (hasQ4) {
            const q4Data = data.filter(item =>
                ['December', 'Q4', 'Quarter 4', '12'].some(term =>
                    item.month.toLowerCase().includes(term.toLowerCase())
                )
            );
            return { rows: q4Data, showExpander: data.length > q4Data.length };
        }

        const condensedRows = data.slice(-3);
        return { rows: condensedRows, showExpander: data.length > 3 };
    };

    const calculateAnnualTotals = (data: FinancialData[]): AnnualTotals => {
        const totalRevenue = data.reduce((sum, item) => sum + item.totalRevenue, 0);
        const totalNetProfitAfterTax = data.reduce((sum, item) => sum + item.netProfitAfterTax, 0);
        const averageNetProfitMargin = data.length > 0
            ? data.reduce((sum, item) => sum + item.netProfitMargin, 0) / data.length
            : 0;

        return {
            totalRevenue,
            totalNetProfitAfterTax,
            averageNetProfitMargin
        };
    };

    const prepareChartData = () => {
        return financialData.map(yearData => {
            const totals = calculateAnnualTotals(yearData.data);
            return {
                year: yearData.year,
                totalRevenue: totals.totalRevenue,
                totalNetProfitAfterTax: totals.totalNetProfitAfterTax,
                netProfitMargin: totals.averageNetProfitMargin
            };
        });
    };

    const CustomTooltip = ({ active, payload, label }: TooltipProps) => {
        if (active && payload && payload.length > 0) {
            return (
                <div className="bg-white/95 backdrop-blur-sm p-4 rounded-lg shadow-lg border border-gray-200/50">
                    <p className="font-semibold text-gray-900 mb-2">{`Year: ${label}`}</p>
                    {payload.map((entry, index) => {
                        let displayValue = '';
                        let label = '';

                        if (entry.dataKey === 'totalRevenue') {
                            displayValue = `$${(entry.value / 1000000).toFixed(2)}M`;
                            label = 'Total Revenue';
                        } else if (entry.dataKey === 'totalNetProfitAfterTax') {
                            displayValue = `$${(entry.value / 1000000).toFixed(2)}M`;
                            label = 'Net Profit';
                        } else if (entry.dataKey === 'netProfitMargin') {
                            displayValue = `${entry.value.toFixed(1)}%`;
                            label = 'Profit Margin';
                        }

                        return (
                            <p key={index} style={{ color: entry.color }} className="text-sm">
                                {`${label}: ${displayValue}`}
                            </p>
                        );
                    })}
                </div>
            );
        }
        return null;
    };

    // Loading state
    if (isLoading) {
        return (
            <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-4">Financial Summary</h2>
                <div className="flex justify-center items-center h-40">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-800"></div>
                    <span className="ml-2">Loading financial data...</span>
                </div>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-4">Financial Summary</h2>
                <div className="text-center p-6 bg-red-50 rounded-lg border border-red-200 text-red-700">
                    <p>{error}</p>
                    <p className="text-sm mt-2">Please upload P&L statements in CSV format with year in the filename.</p>
                </div>
            </div>
        );
    }

    // No data state
    if (financialData.length === 0) {
        return (
            <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-4">Financial Summary</h2>
                <div className="text-center p-6 bg-gray-50 rounded-lg border border-gray-200">
                    <p className="text-gray-700">No financial data available</p>
                    <p className="text-sm text-gray-500 mt-2">Upload P&L statements in CSV format with year in the filename.</p>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-gradient-to-br from-white to-gray-50/30 rounded-xl shadow-sm border border-gray-200/60 overflow-hidden backdrop-blur-sm">
            {viewMode === 'chart' ? (
                // Chart View - with padding
                <div className="p-8 pb-0">
                    <div className="flex items-center justify-between mb-8">
                        <div>
                            <h2 className="text-2xl font-semibold text-gray-900 tracking-tight">Annual Trends</h2>
                            <p className="text-sm text-gray-500 mt-1">Performance overview across all years</p>
                        </div>
                    </div>

                    {/* Annual Trends Chart */}
                    {financialData.length > 1 ? (
                        <div>
                            <div className="flex items-center justify-between mb-6">
                                <h3 className="text-lg font-medium text-gray-900">Financial Performance</h3>
                                <div className="flex items-center space-x-4 text-xs text-gray-500">
                                    <div className="flex items-center space-x-1.5">
                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                        <span>Revenue</span>
                                    </div>
                                    <div className="flex items-center space-x-1.5">
                                        <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                        <span>Profit</span>
                                    </div>
                                    <div className="flex items-center space-x-1.5">
                                        <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                                        <span>Margin</span>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-xl p-6 shadow-sm">
                                <ResponsiveContainer width="100%" height={350}>
                                    <ComposedChart data={prepareChartData()} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                                        <defs>
                                            <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                                                <stop offset="0%" stopColor="#3B82F6" stopOpacity={0.8} />
                                                <stop offset="100%" stopColor="#3B82F6" stopOpacity={0.3} />
                                            </linearGradient>
                                            <linearGradient id="profitGradient" x1="0" y1="0" x2="0" y2="1">
                                                <stop offset="0%" stopColor="#10B981" stopOpacity={0.8} />
                                                <stop offset="100%" stopColor="#10B981" stopOpacity={0.3} />
                                            </linearGradient>
                                        </defs>
                                        <CartesianGrid strokeDasharray="2 2" stroke="#E5E7EB" strokeOpacity={0.5} />
                                        <XAxis
                                            dataKey="year"
                                            tick={{ fontSize: 12, fill: '#6B7280' }}
                                            tickLine={false}
                                            axisLine={false}
                                        />
                                        <YAxis
                                            yAxisId="money"
                                            orientation="left"
                                            tick={{ fontSize: 11, fill: '#6B7280' }}
                                            tickLine={false}
                                            axisLine={false}
                                            tickFormatter={(value) => `$${(value / 1000000).toFixed(1)}M`}
                                        />
                                        <YAxis
                                            yAxisId="percentage"
                                            orientation="right"
                                            tick={{ fontSize: 11, fill: '#6B7280' }}
                                            tickLine={false}
                                            axisLine={false}
                                            tickFormatter={(value) => `${value.toFixed(1)}%`}
                                        />
                                        <Tooltip content={<CustomTooltip />} />
                                        <Bar
                                            yAxisId="money"
                                            dataKey="totalRevenue"
                                            fill="url(#revenueGradient)"
                                            name="Total Revenue"
                                            radius={[4, 4, 0, 0]}
                                            strokeWidth={0}
                                        />
                                        <Bar
                                            yAxisId="money"
                                            dataKey="totalNetProfitAfterTax"
                                            fill="url(#profitGradient)"
                                            name="Net Profit After Tax"
                                            radius={[4, 4, 0, 0]}
                                            strokeWidth={0}
                                        />
                                        <Line
                                            yAxisId="percentage"
                                            type="monotone"
                                            dataKey="netProfitMargin"
                                            stroke="#F59E0B"
                                            strokeWidth={2.5}
                                            dot={{ fill: '#F59E0B', strokeWidth: 0, r: 3 }}
                                            activeDot={{ r: 5, fill: '#F59E0B', strokeWidth: 2, stroke: '#FEF3C7' }}
                                            name="Net Profit Margin (%)"
                                        />
                                    </ComposedChart>
                                </ResponsiveContainer>
                            </div>
                        </div>
                    ) : (
                        <div className="text-center p-8 bg-gray-50/50 rounded-xl border border-gray-200/50">
                            <p className="text-gray-600">Chart view requires data from multiple years to show trends</p>
                        </div>
                    )}
                </div>
            ) : (
                // Table View - remove padding to use full width
                <div>
                    <div className="p-8 pb-0">
                        <div className="flex items-center justify-between mb-8">
                            <div>
                                <h2 className="text-2xl font-semibold text-gray-900 tracking-tight">Financial Summary</h2>
                                <p className="text-sm text-gray-500 mt-1">Annual performance and monthly breakdowns</p>
                            </div>
                        </div>
                    </div>

                    {/* Tabs section - remove horizontal padding for full width */}
                    <div className="pb-4">
                        <Tabs selectedIndex={0} onSelect={() => { }}>
                            <div className="px-8">
                                <TabList className="flex space-x-1 p-1 bg-gray-100/80 rounded-lg backdrop-blur-sm border border-gray-200/50">
                                    {financialData.map((yearData) => (
                                        <Tab
                                            key={yearData.year}
                                            className="flex-1 px-4 py-2.5 text-sm font-medium text-gray-600 rounded-md cursor-pointer focus:outline-none transition-all duration-200 hover:text-gray-900 hover:bg-white/60"
                                            selectedClassName="bg-white text-gray-900 shadow-sm border border-gray-200/50 font-semibold"
                                        >
                                            {yearData.year}
                                        </Tab>
                                    ))}
                                </TabList>
                            </div>

                            {/* Full-width tables - no horizontal padding */}
                            {financialData.map((yearData) => (
                                <TabPanel key={yearData.year}>
                                    <div className="bg-white/60 backdrop-blur-sm border-y border-gray-200/50 shadow-sm mt-4">
                                        <div className="overflow-x-auto">
                                            <table className="min-w-full">
                                                <thead>
                                                    <tr className="bg-gradient-to-r from-gray-50 to-gray-100/50 border-b border-gray-200/50">
                                                        <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                            Period
                                                        </th>
                                                        <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                            Total Revenue
                                                        </th>
                                                        <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                            Net Profit After Tax
                                                        </th>
                                                        <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                            Net Profit Margin
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody className="divide-y divide-gray-200/50">
                                                    {(() => {
                                                        const { rows, showExpander } = getCondensedData(yearData.data, yearData.year);
                                                        const isExpanded = expandedYears.has(yearData.year);

                                                        return (
                                                            <>
                                                                {/* Show first 2 months or all months if expanded */}
                                                                {(isExpanded ? yearData.data : rows.slice(0, 2)).map((row, rowIndex) => (
                                                                    <tr key={rowIndex} className="hover:bg-blue-50/30 transition-colors duration-150">
                                                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                                            {row.month}
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                                                                            <PriceFormatter price={row.totalRevenue} />
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                                                                            <PriceFormatter price={row.netProfitAfterTax} />
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                                                                            <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${row.netProfitMargin >= 0
                                                                                ? 'bg-emerald-50 text-emerald-700 border border-emerald-200/50'
                                                                                : 'bg-red-50 text-red-700 border border-red-200/50'
                                                                                }`}>
                                                                                {row.netProfitMargin.toFixed(2)}%
                                                                            </span>
                                                                        </td>
                                                                    </tr>
                                                                ))}

                                                                {/* Expand/Collapse Row */}
                                                                {showExpander && !isExpanded && (
                                                                    <tr
                                                                        className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 hover:from-blue-100/60 hover:to-indigo-100/60 cursor-pointer transition-all duration-200 border-y border-blue-200/40"
                                                                        onClick={() => toggleTableExpansion(yearData.year)}
                                                                    >
                                                                        <td colSpan={4} className="px-6 py-3">
                                                                            <div className="flex items-center justify-center space-x-2 text-blue-600">
                                                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                                                </svg>
                                                                                <span className="text-sm font-medium">
                                                                                    Expand to see all
                                                                                </span>
                                                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                                                </svg>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                )}

                                                                {/* Collapse Row */}
                                                                {isExpanded && yearData.data.length > 3 && (
                                                                    <tr
                                                                        className="bg-gradient-to-r from-gray-50/50 to-slate-50/50 hover:from-gray-100/60 hover:to-slate-100/60 cursor-pointer transition-all duration-200 border-y border-gray-200/40"
                                                                        onClick={() => toggleTableExpansion(yearData.year)}
                                                                    >
                                                                        <td colSpan={4} className="px-6 py-3">
                                                                            <div className="flex items-center justify-center space-x-2 text-gray-600">
                                                                                <svg className="w-4 h-4 rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                                                </svg>
                                                                                <span className="text-sm font-medium">
                                                                                    Collapse to summary view
                                                                                </span>
                                                                                <svg className="w-4 h-4 rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                                                </svg>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                )}

                                                                {/* Show last month if not expanded and it exists */}
                                                                {!isExpanded && yearData.data.length > 2 && (
                                                                    <tr className="hover:bg-blue-50/30 transition-colors duration-150">
                                                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                                            {yearData.data[yearData.data.length - 1].month}
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                                                                            <PriceFormatter price={yearData.data[yearData.data.length - 1].totalRevenue} />
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                                                                            <PriceFormatter price={yearData.data[yearData.data.length - 1].netProfitAfterTax} />
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                                                                            <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${yearData.data[yearData.data.length - 1].netProfitMargin >= 0
                                                                                ? 'bg-emerald-50 text-emerald-700 border border-emerald-200/50'
                                                                                : 'bg-red-50 text-red-700 border border-red-200/50'
                                                                                }`}>
                                                                                {yearData.data[yearData.data.length - 1].netProfitMargin.toFixed(2)}%
                                                                            </span>
                                                                        </td>
                                                                    </tr>
                                                                )}
                                                            </>
                                                        );
                                                    })()}

                                                    {/* Annual Totals Row */}
                                                    {(() => {
                                                        const annualTotals = calculateAnnualTotals(yearData.data);
                                                        return (
                                                            <tr className="bg-gradient-to-r from-slate-50 to-blue-50/30 border-t-2 border-blue-200/60">
                                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-slate-900">
                                                                    <div className="flex items-center space-x-2">
                                                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                                        <span>Annual Total</span>
                                                                    </div>
                                                                </td>
                                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-slate-900">
                                                                    <PriceFormatter price={annualTotals.totalRevenue} />
                                                                </td>
                                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-slate-900">
                                                                    <PriceFormatter price={annualTotals.totalNetProfitAfterTax} />
                                                                </td>
                                                                <td className="px-6 py-4 whitespace-nowrap text-sm">
                                                                    <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-bold border-2 ${annualTotals.averageNetProfitMargin >= 0
                                                                        ? 'bg-emerald-50 text-emerald-800 border-emerald-300/50'
                                                                        : 'bg-red-50 text-red-800 border-red-300/50'
                                                                        }`}>
                                                                        {annualTotals.averageNetProfitMargin.toFixed(2)}%
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        );
                                                    })()}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </TabPanel>
                            ))}
                        </Tabs>
                    </div>
                </div>
            )}

            {/* Data Room Button - different padding based on view mode */}
            <div className={`pt-6 pb-6 ${viewMode === 'chart' ? 'px-8' : 'px-4'}`}>
                <Link
                    href={`/listings/${listingId}/data-room`}
                    className="w-full flex items-center justify-center space-x-2 py-3 px-4 bg-gray-50 hover:bg-gray-100 border border-gray-200/60 rounded-lg text-gray-700 hover:text-gray-900 text-sm font-medium transition-all duration-200 group"
                >
                    <svg className="w-4 h-4 group-hover:translate-x-0.5 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span>View Complete Data Room</span>
                </Link>
            </div>
        </div>
    );
};

export default FinancialSummary; 