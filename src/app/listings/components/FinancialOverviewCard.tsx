import { BarChart2, Receipt, PiggyBank, DollarSign, Wallet, Repeat, Users, TrendingUp } from 'lucide-react';
import { PriceFormatter } from '@/components';

interface FinancialOverviewCardProps {
    listingDetails: {
        annual_revenue_ttm_min?: number | null;
        annual_revenue_ttm_max?: number | null;
        annual_net_profit_ttm_min?: number | null;
        annual_net_profit_ttm_max?: number | null;
        last_month_revenue_min?: number | null;
        last_month_revenue_max?: number | null;
        last_month_profit_min?: number | null;
        last_month_profit_max?: number | null;
        recurring_revenue_min?: number | null;
        recurring_revenue_max?: number | null;
        active_customers?: number | null;
        growth_rate?: string | null;
    };
}

const FinancialOverviewCard: React.FC<FinancialOverviewCardProps> = ({ listingDetails }) => {
    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
            <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-emerald-50 rounded-lg">
                    <BarChart2 className="w-5 h-5 text-emerald-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">Financial Overview</h2>
            </div>

            <dl className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center gap-2 mb-2">
                        <Receipt className="w-4 h-4 text-gray-400" />
                        Annual Revenue (TTM)
                    </dt>
                    <dd className="text-sm text-gray-900">
                        {listingDetails?.annual_revenue_ttm_min && listingDetails?.annual_revenue_ttm_max ? (
                            <>
                                <PriceFormatter price={listingDetails.annual_revenue_ttm_min} />
                                {' - '}
                                <PriceFormatter price={listingDetails.annual_revenue_ttm_max} />
                            </>
                        ) : 'Not shared'}
                    </dd>
                </div>

                <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center gap-2 mb-2">
                        <PiggyBank className="w-4 h-4 text-gray-400" />
                        Annual Net Profit (TTM)
                    </dt>
                    <dd className="text-sm text-gray-900">
                        {listingDetails?.annual_net_profit_ttm_min && listingDetails?.annual_net_profit_ttm_max ? (
                            <>
                                <PriceFormatter price={listingDetails.annual_net_profit_ttm_min} />
                                {' - '}
                                <PriceFormatter price={listingDetails.annual_net_profit_ttm_max} />
                            </>
                        ) : 'Not shared'}
                    </dd>
                </div>

                <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center gap-2 mb-2">
                        <DollarSign className="w-4 h-4 text-gray-400" />
                        Last Month Revenue
                    </dt>
                    <dd className="text-sm text-gray-900">
                        {listingDetails?.last_month_revenue_min && listingDetails?.last_month_revenue_max ? (
                            <>
                                <PriceFormatter price={listingDetails.last_month_revenue_min} />
                                {' - '}
                                <PriceFormatter price={listingDetails.last_month_revenue_max} />
                            </>
                        ) : 'Not shared'}
                    </dd>
                </div>

                <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center gap-2 mb-2">
                        <Wallet className="w-4 h-4 text-gray-400" />
                        Last Month Profit
                    </dt>
                    <dd className="text-sm text-gray-900">
                        {listingDetails?.last_month_profit_min && listingDetails?.last_month_profit_max ? (
                            <>
                                <PriceFormatter price={listingDetails.last_month_profit_min} />
                                {' - '}
                                <PriceFormatter price={listingDetails.last_month_profit_max} />
                            </>
                        ) : 'Not shared'}
                    </dd>
                </div>

                <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center gap-2 mb-2">
                        <Repeat className="w-4 h-4 text-gray-400" />
                        Recurring Revenue
                    </dt>
                    <dd className="text-sm text-gray-900">
                        {listingDetails?.recurring_revenue_min && listingDetails?.recurring_revenue_max ? (
                            <>
                                <PriceFormatter price={listingDetails.recurring_revenue_min} />
                                {' - '}
                                <PriceFormatter price={listingDetails.recurring_revenue_max} />
                            </>
                        ) : 'Not shared'}
                    </dd>
                </div>

                <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center gap-2 mb-2">
                        <Users className="w-4 h-4 text-gray-400" />
                        Active Customers
                    </dt>
                    <dd className="text-sm text-gray-900">
                        {listingDetails?.active_customers?.toLocaleString() || 'Not shared'}
                    </dd>
                </div>

                {listingDetails?.growth_rate && (
                    <div className="md:col-span-2">
                        <dt className="text-sm font-medium text-gray-500 flex items-center gap-2 mb-2">
                            <TrendingUp className="w-4 h-4 text-gray-400" />
                            Growth Rate
                        </dt>
                        <dd className="text-sm text-gray-900">
                            {listingDetails.growth_rate}
                        </dd>
                    </div>
                )}
            </dl>
        </div>
    );
};

export default FinancialOverviewCard; 