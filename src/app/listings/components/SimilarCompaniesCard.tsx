'use client';

import React from 'react';
import { Briefcase, Users, DollarSign, Info, MapPin, TrendingUp } from 'lucide-react';
import { PriceFormatter } from '@/components';

interface SimilarCompaniesData {
    numEstablishments?: number | null;
    totalAnnualPayroll?: number | null; // in thousands of dollars
    totalEmployment?: number | null;
    averageAnnualWage?: number | null;
    countyName?: string | null;
    naicsDescription?: string | null;
}

interface SimilarCompaniesCardProps {
    data: SimilarCompaniesData;
    className?: string;
}

const DataPoint: React.FC<{
    icon: React.ElementType;
    label: string;
    value: string | React.ReactNode;
    accentColor?: string;
}> = ({ icon: Icon, label, value, accentColor = 'text-cyan-600' }) => (
    <div className="bg-slate-50 p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
        <dt className="text-sm font-medium text-gray-500 flex items-center">
            <Icon className={`w-5 h-5 mr-2 ${accentColor}`} aria-hidden="true" />
            {label}
        </dt>
        <dd className="mt-1 text-xl font-semibold text-gray-800 sm:text-2xl tracking-tight">
            {value}
        </dd>
    </div>
);

const SimilarCompaniesCard: React.FC<SimilarCompaniesCardProps> = ({ data, className }) => {
    const {
        numEstablishments,
        totalAnnualPayroll,
        totalEmployment,
        averageAnnualWage,
        countyName,
        naicsDescription
    } = data;

    const formatPayroll = (payrollInThousands: number | null | undefined) => {
        if (payrollInThousands === null || payrollInThousands === undefined) return 'N/A';
        return <PriceFormatter price={payrollInThousands * 1000} className="!text-xl sm:!text-2xl !font-semibold !text-gray-800 !tracking-tight" />;
    };

    const formatNumber = (num: number | null | undefined) => {
        if (num === null || num === undefined) return 'N/A';
        return num.toLocaleString();
    };

    const hasData = numEstablishments !== null || totalAnnualPayroll !== null || totalEmployment !== null || averageAnnualWage !== null;
    const accentColor = 'text-cyan-600';

    return (
        <div className={`bg-white rounded-xl shadow-xl overflow-hidden ${className || ''}`}>
            <div className="p-6">
                <div className="mb-6 text-center sm:text-left">
                    <h3 className="text-xl font-semibold text-gray-800 leading-tight mb-2">
                        Similar Businesses
                    </h3>
                    {naicsDescription && (
                        <span className={`inline-block bg-cyan-100 text-cyan-700 text-xs font-semibold mr-2 px-2.5 py-1 rounded-full mb-2 sm:mb-1`}>
                            {naicsDescription}
                        </span>
                    )}
                    {countyName && (
                        <div className="flex items-center justify-center sm:justify-start text-sm text-gray-500">
                            <MapPin className={`w-4 h-4 ${accentColor} mr-1.5 flex-shrink-0`} />
                            <span>in {countyName}</span>
                        </div>
                    )}
                </div>

                {!hasData && (
                    <div className="text-center py-8">
                        <Info className="w-10 h-10 text-gray-400 mx-auto mb-3" />
                        <p className="text-gray-600 font-medium">Industry Data Not Available</p>
                        <p className="text-sm text-gray-500 mt-1">Detailed data for this specific area and category is currently unavailable.</p>
                    </div>
                )}

                {hasData && (
                    <dl className="flex flex-col space-y-4 mt-2">
                        <DataPoint
                            icon={Briefcase}
                            label="Establishments"
                            value={formatNumber(numEstablishments)}
                            accentColor={accentColor}
                        />
                        <DataPoint
                            icon={Users}
                            label="Total Employment"
                            value={formatNumber(totalEmployment)}
                            accentColor={accentColor}
                        />
                        <DataPoint
                            icon={DollarSign}
                            label="Total Annual Payroll"
                            value={formatPayroll(totalAnnualPayroll)}
                            accentColor={accentColor}
                        />
                        <DataPoint
                            icon={TrendingUp}
                            label="Avg. Annual Wage"
                            value={averageAnnualWage !== null && averageAnnualWage !== undefined ? <PriceFormatter price={averageAnnualWage} className="!text-xl sm:!text-2xl !font-semibold !text-gray-800 !tracking-tight" /> : 'N/A'}
                            accentColor={accentColor}
                        />
                    </dl>
                )}
                <p className="text-xs text-gray-400 mt-6 pt-4 border-t border-gray-100">
                    Data sourced from U.S. Census Bureau County Business Patterns (CBP).
                </p>
            </div>
        </div>
    );
};

export default SimilarCompaniesCard; 