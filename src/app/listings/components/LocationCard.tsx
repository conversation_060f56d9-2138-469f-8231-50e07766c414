import { MapPin } from 'lucide-react';
import { ListingMap } from './ListingMap';
import { Home } from 'lucide-react';

interface LocationCardProps {
    address?: string | null;
    city?: string | null;
    state?: { name: string; code: string } | null;
    postalCode?: string | null;
    latitude?: number | null;
    longitude?: number | null;
}

const LocationCard: React.FC<LocationCardProps> = ({
    address,
    city,
    state,
    postalCode,
    latitude,
    longitude
}) => {
    const hasAddress = address && city && state && postalCode;
    if (!hasAddress) return null;

    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
            <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-red-50 rounded-lg">
                    <MapPin className="w-5 h-5 text-red-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">Location</h2>
            </div>

            <div className="space-y-6">
                <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center gap-2 mb-2">
                        <Home className="w-4 h-4 text-gray-400" />
                        Address
                    </dt>
                    <dd className="text-sm text-gray-900">
                        {address ? (
                            <>
                                {address}, {city}, {state?.name} {postalCode}
                            </>
                        ) : 'Not shared'}
                    </dd>
                </div>

                {/* Add Map */}
                {address && (
                    <div>
                        {(latitude && longitude) ? (
                            <ListingMap
                                latitude={latitude}
                                longitude={longitude}
                                className="rounded-lg"
                            />
                        ) : (
                            <ListingMap
                                streetAddress={address || undefined}
                                city={city || undefined}
                                stateCode={state?.code || undefined}
                                postalCode={postalCode || undefined}
                                className="rounded-lg"
                            />
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

export default LocationCard; 