'use client';

import { SaveListingButton } from './SaveListingButton';
import { Share2 } from 'lucide-react';

interface ListingClientActionsProps {
    listingId: string;
    userId: string;
}

export function ListingClientActions({ listingId, userId }: ListingClientActionsProps) {
    return (
        <div className="flex gap-3">
            <button className="flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-colors">
                <Share2 size={20} />
                <span>Share</span>
            </button>
            <SaveListingButton
                listingId={listingId}
                userId={userId}
                variant="compact"
                className="flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-colors"
            />
        </div>
    );
} 