import { Building2, Calendar, FileText, Users } from 'lucide-react';

interface BusinessDetailsCardProps {
    yearEstablished: number | null | undefined;
    legalStructure: string | null | undefined;
    teamSize: number | null | undefined;
}

const BusinessDetailsCard: React.FC<BusinessDetailsCardProps> = ({
    yearEstablished,
    legalStructure,
    teamSize
}) => {
    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
            <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-orange-50 rounded-lg">
                    <Building2 className="w-5 h-5 text-orange-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">Business Details</h2>
            </div>

            <dl className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center gap-2 mb-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        Year Established
                    </dt>
                    <dd className="text-sm text-gray-900">
                        {yearEstablished || 'Not shared'}
                    </dd>
                </div>

                <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center gap-2 mb-2">
                        <FileText className="w-4 h-4 text-gray-400" />
                        Legal Structure
                    </dt>
                    <dd className="text-sm text-gray-900">
                        {legalStructure || 'Not shared'}
                    </dd>
                </div>

                <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center gap-2 mb-2">
                        <Users className="w-4 h-4 text-gray-400" />
                        Team Size
                    </dt>
                    <dd className="text-sm text-gray-900">
                        {teamSize ? `${teamSize} employees` : 'Not shared'}
                    </dd>
                </div>
            </dl>
        </div>
    );
};

export default BusinessDetailsCard; 