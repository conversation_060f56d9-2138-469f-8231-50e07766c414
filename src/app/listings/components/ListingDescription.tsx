'use client';

import { useViewMode } from '@/contexts/ViewModeContext';

interface ListingDescriptionProps {
    realDescription: string | null;
    anonymousDescription: string | null;
}

export default function ListingDescription({ realDescription, anonymousDescription }: ListingDescriptionProps) {
    const { isPublicView } = useViewMode();

    console.log('ListingDescription props:', {
        realDescription,
        anonymousDescription,
        isPublicView
    });

    const displayDescription = isPublicView && anonymousDescription
        ? anonymousDescription
        : (realDescription || 'No description available');

    console.log('Final display description:', displayDescription);

    return (
        <p className="text-gray-600 whitespace-pre-wrap">
            {displayDescription}
        </p>
    );
} 