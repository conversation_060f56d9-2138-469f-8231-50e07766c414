'use client';

import { useState, useEffect } from 'react';
import { Message } from '@/types/message';
import { User } from '@supabase/supabase-js';
import { createClient } from '@/utils/supabase/client';

interface ListingMessagesProps {
    listingId: string;
    currentUser: User | null;
    initialMessages?: Message[];
}

export function ListingMessages({ listingId, currentUser, initialMessages = [] }: ListingMessagesProps) {
    const [messages, setMessages] = useState<Message[]>(initialMessages);
    const [newMessage, setNewMessage] = useState('');
    const supabase = createClient();

    // Set up real-time subscription
    useEffect(() => {
        const channel = supabase
            .channel(`listing-messages-${listingId}`)
            .on(
                'postgres_changes',
                {
                    event: 'INSERT',
                    schema: 'public',
                    table: 'messages',
                    filter: `listing_id=eq.${listingId}`
                },
                (payload) => {
                    setMessages(prev => [payload.new as Message, ...prev]);
                }
            )
            .subscribe();

        return () => {
            supabase.removeChannel(channel);
        };
    }, [supabase, listingId]);

    const handleSendMessage = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!newMessage.trim() || !currentUser) return;

        try {
            // We need to get the listing owner to set as recipient
            const { data: listing } = await supabase
                .from('listings')
                .select('user_id')
                .eq('id', listingId)
                .single();

            if (!listing) {
                console.error('Could not find listing');
                return;
            }

            const { data: messageResult, error } = await supabase
                .from('messages')
                .insert({
                    listing_id: listingId,
                    content: newMessage,
                    sender_id: currentUser.id,
                    recipient_id: listing.user_id
                })
                .select()
                .single();

            if (error) throw error;

            // Trigger email notification (non-blocking)
            if (messageResult) {
                try {
                    await fetch('/api/send-message-email', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            record: messageResult
                        })
                    });
                } catch (emailError) {
                    console.log('Email notification failed (non-critical):', emailError);
                }
            }

            setNewMessage('');
        } catch (error) {
            console.error('Error sending message:', error);
        }
    };

    return (
        <div className="bg-white rounded-lg shadow p-4">
            <div className="space-y-4 max-h-96 overflow-y-auto">
                {messages.map((message) => (
                    <div
                        key={message.id}
                        className={`flex gap-4 ${message.sender_id === currentUser?.id ? 'justify-end' : 'justify-start'
                            }`}
                    >
                        <div className="bg-gray-100 rounded-lg p-3 max-w-[80%]">
                            <p className="text-sm text-gray-600">
                                {message.sender?.profiles.first_name || 'Anonymous'}
                            </p>
                            <p className="text-gray-900">{message.content}</p>
                            <p className="text-xs text-gray-500">
                                {new Date(message.created_at).toLocaleString()}
                            </p>
                        </div>
                    </div>
                ))}
            </div>

            <form onSubmit={handleSendMessage} className="mt-4">
                <div className="flex gap-2">
                    <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        placeholder="Type your message..."
                        className="flex-1 rounded-lg border p-2"
                    />
                    <button
                        type="submit"
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg"
                    >
                        Send
                    </button>
                </div>
            </form>
        </div>
    );
} 