export default function LoadingPage() {
    return (
        <main className="min-h-screen bg-gray-50">
            {/* Hero Section Skeleton */}
            <div className="relative h-[600px] w-full overflow-hidden">
                <div className="absolute inset-0 bg-neutral-800 animate-pulse">
                    {/* Notification Bar */}
                    <div className="relative bg-white/10 backdrop-blur-sm border-b border-white/20">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 flex items-center justify-center">
                            <div className="h-8 w-64 bg-white/20 rounded-md animate-pulse" />
                        </div>
                    </div>

                    {/* Hero Content */}
                    <div className="relative h-[calc(100%-48px)] flex flex-col items-center justify-center px-4 gap-8">
                        <div className="h-16 w-96 bg-white/20 rounded-lg animate-pulse" />
                        <div className="w-full max-w-4xl flex flex-col md:flex-row gap-2">
                            <div className="flex-1 h-12 bg-white/20 rounded-lg animate-pulse" />
                            <div className="flex-1 h-12 bg-white/20 rounded-lg animate-pulse" />
                            <div className="w-32 h-12 bg-white/20 rounded-lg animate-pulse" />
                        </div>
                    </div>
                </div>
            </div>

            {/* Listings Section */}
            <div className="max-w-7xl mx-auto p-6">
                {/* Header */}
                <div className="flex justify-between items-center mb-8">
                    <div className="flex items-center gap-2">
                        <div className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
                        <div className="h-8 w-24 bg-gray-200 rounded animate-pulse" />
                    </div>
                    <div className="flex items-center gap-4">
                        <div className="h-10 w-40 bg-gray-200 rounded-lg animate-pulse" />
                        <div className="h-8 w-px bg-gray-200" />
                        <div className="h-10 w-32 bg-gray-200 rounded-lg animate-pulse" />
                        <div className="h-8 w-px bg-gray-200" />
                        <div className="h-10 w-24 bg-gray-200 rounded-lg animate-pulse" />
                    </div>
                </div>

                {/* Listings Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {[...Array(6)].map((_, i) => (
                        <div key={i} className="bg-white rounded-lg shadow-sm overflow-hidden">
                            <div className="aspect-video bg-gray-200 animate-pulse" />
                            <div className="p-4 space-y-4">
                                <div className="space-y-2">
                                    <div className="h-6 w-3/4 bg-gray-200 rounded animate-pulse" />
                                    <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse" />
                                </div>
                                <div className="space-y-2">
                                    <div className="h-4 w-full bg-gray-200 rounded animate-pulse" />
                                    <div className="h-4 w-5/6 bg-gray-200 rounded animate-pulse" />
                                </div>
                                <div className="flex justify-between items-center pt-4">
                                    <div className="h-10 w-24 bg-gray-200 rounded-lg animate-pulse" />
                                    <div className="h-10 w-10 bg-gray-200 rounded-full animate-pulse" />
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </main>
    );
}
