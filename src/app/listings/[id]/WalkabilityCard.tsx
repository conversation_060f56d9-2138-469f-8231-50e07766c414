import React from 'react';
import { Footprints, Train, Bike } from 'lucide-react';
import * as TooltipPrimitive from '@radix-ui/react-tooltip';

export interface WalkabilityData {
    walkScore: number | null;
    isWalkScoreMock?: boolean;
    walkDescription: string | null;
    transitScore: number | null;
    isTransitScoreMock?: boolean;
    transitDescription: string | null;
    bikeScore: number | null;
    isBikeScoreMock?: boolean;
    bikeDescription: string | null;
    wsLink: string | null;
}

interface WalkabilityCardProps {
    data: WalkabilityData;
}

// Helper component for individual score cards
const ScoreCard: React.FC<{
    icon: React.ElementType;
    iconColorClass: string;
    label: string;
    score: number | null;
    description: string | null;
    isMock?: boolean;
}> = ({ icon: Icon, iconColorClass, label, score, description, isMock }) => {
    const tooltipDescription = description ?
        (isMock ? `${description} (mock data)` : description) :
        (isMock ? '(mock data)' : null);

    return (
        <div className="flex-1 bg-gray-50 rounded-lg p-4 border border-gray-200 flex flex-col items-center text-center shadow-sm">
            <Icon className={`w-6 h-6 mb-2 ${iconColorClass}`} />
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">{label}</span>
            {tooltipDescription ? (
                <TooltipPrimitive.Root delayDuration={100}>
                    <TooltipPrimitive.Trigger asChild>
                        <b className="text-xl font-bold text-gray-800 cursor-default">
                            {score !== null ? score : 'N/A'}
                        </b>
                    </TooltipPrimitive.Trigger>
                    <TooltipPrimitive.Portal>
                        <TooltipPrimitive.Content
                            sideOffset={5}
                            className="z-50 overflow-hidden rounded-md bg-gray-900 px-3 py-1.5 text-xs text-gray-50 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:bg-gray-50 dark:text-gray-900"
                        >
                            {tooltipDescription}
                            <TooltipPrimitive.Arrow className="fill-gray-900 dark:fill-gray-50" />
                        </TooltipPrimitive.Content>
                    </TooltipPrimitive.Portal>
                </TooltipPrimitive.Root>
            ) : (
                <b className="text-xl font-bold text-gray-800">
                    {score !== null ? score : 'N/A'}
                </b>
            )}
        </div>
    );
};


const WalkabilityCard: React.FC<WalkabilityCardProps> = ({ data }) => {
    // Only render the card if at least one score is available
    const hasAnyScore = data.walkScore !== null || data.transitScore !== null || data.bikeScore !== null;

    if (!hasAnyScore && !data.wsLink) {
        return null; // Don't render anything if there's no data at all
    }

    return (
        <TooltipPrimitive.Provider>
            {/* Removed the outer card div, added margin top */}
            <div className="mt-6">
                {/* <span className=\"text-sm font-bold text-gray-400 mb-4 flex items-center gap-2 uppercase tracking-wider\"> Removed Walkability Header
                    <Footprints className=\"w-4 h-4\" />
                    Walkability
                </span> */}
                {/* Flex container for horizontal cards */}
                <div className="flex flex-col sm:flex-row gap-4 mt-2">
                    {data.walkScore !== null && (
                        <ScoreCard
                            icon={Footprints}
                            iconColorClass="text-green-600"
                            label="Walk Score"
                            score={data.walkScore}
                            description={data.walkDescription}
                            isMock={data.isWalkScoreMock}
                        />
                    )}
                    {data.transitScore !== null && (
                        <ScoreCard
                            icon={Train}
                            iconColorClass="text-blue-600"
                            label="Transit Score"
                            score={data.transitScore}
                            description={data.transitDescription}
                            isMock={data.isTransitScoreMock}
                        />
                    )}
                    {data.bikeScore !== null && (
                        <ScoreCard
                            icon={Bike}
                            iconColorClass="text-yellow-600"
                            label="Bike Score"
                            score={data.bikeScore}
                            description={data.bikeDescription}
                            isMock={data.isBikeScoreMock}
                        />
                    )}
                </div>
                {/* Links container with flex space-between */}
                {/* REMOVED data source link section from here */}
                {/* <div className=\"mt-4 flex justify-between items-center text-xs text-gray-400\">
                    {data.wsLink && (
                        <div> 
                            <a href={data.wsLink} target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline hover:text-gray-600 flex items-center gap-1\">
                                <Link2 className=\"w-3 h-3\" /> View full Walk Score details
                            </a>
                        </div>
                    )}
                    {!data.wsLink && <div />} 

                    <div> 
                        Data source: <a href=\"https://www.walkscore.com/professional/api.php\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline hover:text-gray-600\">Walk Score API</a>
                    </div>
                </div> */}
            </div>
        </TooltipPrimitive.Provider>
    );
}

export default WalkabilityCard; 