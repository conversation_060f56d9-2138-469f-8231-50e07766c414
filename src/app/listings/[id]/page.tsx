import { notFound } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import { Listing } from '@/types/listing';
import { Calendar } from 'lucide-react';
import InterestCard from '../components/InterestCard';
import { Industry, IndustryIcon } from '@/config/industries';
import { ListingClientActions } from '../components/ListingClientActions';
import { SavedListingProvider } from '@/contexts/SavedListingContext';
import ViewModeToggle from '../components/ViewModeToggle';
import { ViewModeProvider } from '@/contexts/ViewModeContext';
import ListingTitle from '../components/ListingTitle';
import ListingImage from '../components/ListingImage';
import PriceCard from '../components/PriceCard';
import DescriptionCard from '../components/DescriptionCard';
import WebsiteCard from '../components/WebsiteCard';
import FinancialOverviewCard from '../components/FinancialOverviewCard';
import FinancialAnalysisCard from '../components/FinancialAnalysisCard';
import BusinessDetailsCard from '../components/BusinessDetailsCard';
import LocationCard from '../components/LocationCard';
import ReasonForSellingCard from '../components/ReasonForSellingCard';
import LazyAreaInsights from '../components/LazyAreaInsights';
import Link from 'next/link';

// Focused interface definitions following single responsibility principle
interface ListingProfile {
    user_id: string;
    first_name: string | null;
    last_name: string | null;
    profile_photo?: string;
    email: string;
}

interface ListingIndustry {
    id: string;
    name: string;
    naics_code?: string | null;
}

interface ListingState {
    id: string;
    name: string;
    code: string;
}

interface ListingLocation {
    street_address: string | null;
    city: string | null;
    state: ListingState | null;
    postal_code: string | null;
    latitude?: number | null;
    longitude?: number | null;
    fips_code?: string | null;
}

interface ListingFinancialRanges {
    annual_revenue_ttm_min: number | null;
    annual_revenue_ttm_max: number | null;
    annual_net_profit_ttm_min: number | null;
    annual_net_profit_ttm_max: number | null;
    last_month_revenue_min: number | null;
    last_month_revenue_max: number | null;
    last_month_profit_min: number | null;
    last_month_profit_max: number | null;
    recurring_revenue_min: number | null;
    recurring_revenue_max: number | null;
}

interface ListingBusinessDetails {
    year_established?: number | null;
    legal_structure?: string | null;
    team_size?: number | null;
    employees?: number;
    reason_for_selling?: string | null;
}

interface ListingDetails extends ListingFinancialRanges, ListingLocation, ListingBusinessDetails {
    active_customers: number | null;
    growth_rate: string | null;
    states: ListingState | null;
}

interface ListingAnonymizedDetails {
    anonymous_title: string;
    anonymous_description: string | null;
    anonymous_image_url: string | null;
}

// Main interface that composes the focused interfaces
interface ListingWithProfile extends Listing {
    profiles: ListingProfile | null;
    industries?: ListingIndustry;
    industry: Industry;
    listing_details?: ListingDetails[];
    listing_anonymized_details?: ListingAnonymizedDetails;

    // Flattened financial data for backward compatibility
    annual_revenue_ttm?: number | null;
    annual_net_profit_ttm?: number | null;
    last_month_revenue?: number | null;
    last_month_profit?: number | null;
    recurring_revenue?: number | null;
    active_customers?: number | null;
    growth_rate?: string | null;
    revenue?: number;
    profit?: number;

    // Flattened business details for backward compatibility
    employees?: number;
    year_established?: number | null;
    legal_structure?: string | null;
    team_size?: number | null;
    reason_for_selling?: string | null;

    // Flattened location data for backward compatibility
    street_address?: string | null;
    city?: string | null;
    state?: ListingState | null;
    postal_code?: string | null;
}

// Change this interface to match Next.js 14's expected types
type PageProps = {
    params: Promise<{ id: string }>;
    searchParams: Promise<{ [key: string]: string | undefined }>;
}

async function checkListingStatus(id: string): Promise<'live' | 'draft' | 'not_found'> {
    const supabase = await createClient();

    try {
        const { data } = await supabase
            .from('listings')
            .select('status')
            .eq('id', id)
            .single();

        if (!data) return 'not_found';
        return data.status as 'live' | 'draft';
    } catch {
        return 'not_found';
    }
}

async function getListing(id: string): Promise<ListingWithProfile | null> {
    const supabase = await createClient();

    try {
        // Use the database function to get all data in one call
        const { data } = await supabase
            .rpc('get_listing_with_profile', { target_listing_id: id });

        if (!data || data.length === 0) {
            return null;
        }

        const row = data[0];

        // Transform the flattened data back to the expected structure
        const listing = {
            id: row.id,
            user_id: row.user_id,
            title: row.title,
            description: row.description,
            price: row.price,
            website: row.website,
            created_at: row.created_at,
            image_url: row.image_url,

            // Profile data
            profiles: row.profile_user_id ? {
                user_id: row.profile_user_id,
                first_name: row.profile_first_name,
                last_name: row.profile_last_name,
                profile_photo: row.profile_photo,
                email: row.profile_email
            } : null,

            // Industry data
            industries: row.industry_name ? {
                id: row.industry_id,
                name: row.industry_name,
                naics_code: row.industry_naics_code
            } : undefined,

            // Listing details
            listing_details: row.listing_details ? [row.listing_details] : [],

            // Anonymized details
            listing_anonymized_details: row.anonymized_details || undefined,

            // Extract specific fields for backward compatibility
            year_established: row.listing_details?.year_established || null,
            legal_structure: row.listing_details?.legal_structure || null,
            team_size: row.listing_details?.team_size || null,
            street_address: row.listing_details?.street_address || null,
            city: row.listing_details?.city || null,
            postal_code: row.listing_details?.postal_code || null,
            reason_for_selling: row.listing_details?.reason_for_selling || null,
            state: row.listing_details?.state_id ? {
                id: row.listing_details.state_id,
                name: '', // Will need to be populated separately if needed
                code: ''
            } : null,

            // Required properties for interface compatibility
            industry: row.industry_name?.toLowerCase().replace(/ & /g, '_').replace(/ /g, '_') as Industry || 'other'
        } as ListingWithProfile;

        return listing;
    } catch (error) {
        console.error('Error fetching listing:', error);
        return null;
    }
}

export default async function ListingDetailPage({ params }: PageProps) {
    try {
        const resolvedParams = await params;
        if (!resolvedParams?.id) notFound();

        // First check if the listing exists and get its status
        const status = await checkListingStatus(resolvedParams.id);

        if (status === 'not_found') {
            notFound();
        }

        if (status === 'draft') {
            // Show draft/under construction page
            return (
                <main className="min-h-screen bg-gray-50 flex items-center justify-center">
                    <div className="max-w-md mx-auto text-center p-8">
                        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                            <div className="flex justify-center mb-6">
                                <div className="p-4 bg-orange-50 rounded-full">
                                    <svg className="w-12 h-12 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 9.172V5L8 4z" />
                                    </svg>
                                </div>
                            </div>
                            <h1 className="text-2xl font-semibold text-gray-900 mb-4">
                                Listing Under Construction
                            </h1>
                            <p className="text-gray-600 mb-6 leading-relaxed">
                                This listing went back into draft mode so the owner could work on it.
                                The owner is making some updates and improvements.
                            </p>
                            <p className="text-sm text-gray-500 mb-8">
                                Please check back later when the listing is live again.
                            </p>
                            <Link
                                href="/listings"
                                className="inline-flex items-center px-6 py-3 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors font-medium"
                            >
                                ← Browse Other Listings
                            </Link>
                        </div>
                    </div>
                </main>
            );
        }

        // **OPTIMIZATION 9: Get essential listing data first for immediate render**
        const listing = await getListing(resolvedParams.id);
        if (!listing) notFound();

        // Extract title and description for immediate display
        const anonymousTitle = listing.listing_anonymized_details?.anonymous_title || null;
        const anonymousDescription = listing.listing_anonymized_details?.anonymous_description || null;

        return (
            <main className="min-h-screen bg-gray-50">
                <SavedListingProvider>
                    <ViewModeProvider>
                        <div className="relative w-full min-h-screen overflow-auto">
                            <div className="relative w-full h-64">
                                <ListingImage
                                    realImageUrl={listing.image_url}
                                    anonymousImageUrl={listing.listing_anonymized_details?.anonymous_image_url || null}
                                    title={listing.title}
                                />
                                {/* Overlay content */}
                                <div className="absolute inset-0 flex items-center">
                                    <div className="max-w-4xl mx-auto w-full p-12">
                                        <div className="flex justify-between items-center">
                                            {/* Left side content */}
                                            <div>
                                                <ListingTitle
                                                    realTitle={listing.title}
                                                    anonymousTitle={anonymousTitle}
                                                />
                                                <div className="flex items-center gap-2">
                                                    <div className="inline-flex items-center gap-2 bg-white/90 rounded-full px-4 py-2 text-sm">
                                                        <IndustryIcon
                                                            industry={listing.industries?.name.toLowerCase().replace(/ & /g, '_').replace(/ /g, '_') as Industry}
                                                            className="text-gray-600"
                                                            size={16}
                                                        />
                                                        <span>{listing.industries?.name}</span>
                                                    </div>
                                                    <div className="inline-flex items-center gap-2 bg-white/90 rounded-full px-4 py-2 text-sm">
                                                        <Calendar className="w-4 h-4 text-gray-600" />
                                                        <span>Listed {new Date(listing.created_at).toLocaleDateString()}</span>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Right side buttons */}
                                            <ListingClientActions
                                                listingId={listing.id}
                                                userId={listing.user_id}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="max-w-4xl mx-auto">
                                <div className="p-6 flex flex-col-reverse md:flex-row gap-6">
                                    {/* Left column - Essential content loads immediately */}
                                    <div className="w-full md:w-2/3 space-y-6">
                                        {/* View Toggle Card */}
                                        <ViewModeToggle />

                                        {/* Price Card - Essential, loads immediately */}
                                        <PriceCard price={listing.price} />

                                        {/* Description Card - Essential, loads immediately */}
                                        <DescriptionCard
                                            realDescription={listing.description || ""}
                                            anonymousDescription={anonymousDescription}
                                        />

                                        {/* Website Card - Essential, loads immediately */}
                                        {listing.website && (
                                            <WebsiteCard website={listing.website} />
                                        )}

                                        {/* Financial Overview Card - Essential, loads immediately */}
                                        <FinancialOverviewCard
                                            listingDetails={listing.listing_details?.[0] || {}}
                                        />

                                        {/* Financial Analysis - Remove nested card structure */}
                                        <FinancialAnalysisCard
                                            listingId={listing.id}
                                            className="!p-0 !bg-transparent !border-none !shadow-none !rounded-none"
                                        />

                                        {/* Business Details Card - Essential, loads immediately */}
                                        <BusinessDetailsCard
                                            yearEstablished={listing.year_established}
                                            legalStructure={listing.legal_structure}
                                            teamSize={listing.team_size}
                                        />

                                        {/* Location Card - Essential, loads immediately */}
                                        <LocationCard
                                            address={listing.listing_details?.[0]?.street_address}
                                            city={listing.listing_details?.[0]?.city}
                                            state={listing.listing_details?.[0]?.states}
                                            postalCode={listing.listing_details?.[0]?.postal_code}
                                            latitude={listing.listing_details?.[0]?.latitude}
                                            longitude={listing.listing_details?.[0]?.longitude}
                                        />

                                        {/* **OPTIMIZATION 10: Lazy load heavy API data in a client component** */}
                                        <LazyAreaInsights
                                            postalCode={listing.postal_code}
                                            fipsCode={listing.listing_details?.[0]?.fips_code}
                                            naicsCode={listing.industries?.naics_code}
                                            address={listing.street_address}
                                            city={listing.city}
                                            state={listing.state?.name}
                                            latitude={listing.listing_details?.[0]?.latitude}
                                            longitude={listing.listing_details?.[0]?.longitude}
                                        />

                                        {/* Reason for Selling Card - Essential, loads immediately */}
                                        <ReasonForSellingCard
                                            reasonForSelling={listing.reason_for_selling ?? null}
                                            sellerProfile={listing.profiles}
                                            businessTitle={listing.title}
                                        />
                                    </div>

                                    {/* Right column - Essential, loads immediately */}
                                    <div className="w-full md:w-1/3 mb-6 md:mb-0">
                                        <InterestCard
                                            interest={{
                                                listingId: listing.id,
                                                userId: listing.user_id,
                                                createdAt: listing.created_at,
                                                listing: {
                                                    title: listing.title,
                                                    user: {
                                                        profile: {
                                                            first_name: listing.profiles?.first_name || '',
                                                            last_name: listing.profiles?.last_name || '',
                                                            profile_photo: listing.profiles?.profile_photo || null
                                                        }
                                                    }
                                                }
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ViewModeProvider>
                </SavedListingProvider>
            </main>
        );
    } catch (e) {
        console.error("Error in ListingDetailPage:", e);
        return <main className="min-h-screen flex items-center justify-center"><div className="text-red-600 font-bold text-xl">Sorry, something went wrong loading this listing.</div></main>;
    }
}
