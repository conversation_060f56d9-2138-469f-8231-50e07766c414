import { createClient } from '@/utils/supabase/server'
import { redirect, notFound } from 'next/navigation'
import DataRoomClient from './DataRoomClient'
import { Metadata } from 'next'
import { Database } from '@/types/supabase'

type ProfileData = Database['public']['Tables']['profiles']['Row'] | null; // Type alias for profile

type AccessEntry = Database['public']['Tables']['data_room_access']['Row'] & {
    profiles: ProfileData; // Use ProfileData type
}

export const metadata: Metadata = {
    title: 'Data Room | Business Marketplace',
    description: 'Manage confidential documents for your business listing',
}

type PageProps = {
    params: Promise<{ id: string }>;
}

export default async function DataRoomPage({
    params,
}: PageProps) {
    const supabase = await createClient()
    const { id: listingId } = await params
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
        redirect('/login')
    }

    // Fetch listing details
    const { data: listing, error: listingError } = await supabase
        .from('listings')
        .select(`
            id,
            user_id,
            title
        `)
        .eq('id', listingId)
        .single()

    if (listingError || !listing) {
        console.error("Listing fetch error:", listingError)
        notFound()
    }

    // Fetch owner's profile using the user_id from the listing
    const { data: ownerProfile, error: profileError } = await supabase
        .from('profiles')
        .select('id, first_name, last_name, profile_photo')
        .eq('user_id', listing.user_id)
        .single<ProfileData>() // Use single<ProfileData> for type safety

    if (profileError) {
        console.error("Owner profile fetch error:", profileError)
        // Handle profile fetch error appropriately, maybe allow continuing without profile?
        // For now, let's treat it as not found, but this might need adjustment
        notFound()
    }

    const isOwner = !!user && user.id === listing.user_id
    let hasAccess = false
    let initialAccessList: AccessEntry[] = []

    // Check access if not the owner
    if (user && !isOwner) {
        const { data: accessData, error: accessError } = await supabase
            .from('data_room_access')
            .select('id')
            .eq('listing_id', listingId)
            .eq('user_id', user.id)
            .maybeSingle() // Use maybeSingle to handle null case gracefully

        if (accessError) {
            console.error("Access check error:", accessError)
            // Decide how to handle error - maybe show an error message instead of NoAccess?
        }
        hasAccess = !!accessData
    }

    // Owners always have access
    if (isOwner) {
        hasAccess = true
    }

    // Fetch all files - RLS will filter based on is_public and access/ownership
    // If the user has neither ownership nor access, RLS should only return public files.
    // If the user has access or is owner, RLS should return all files.
    const { data: allFiles, error: filesError } = await supabase
        .from('data_room_files')
        .select('*')
        .eq('listing_id', listingId)
        .order('created_at', { ascending: false })

    if (filesError) {
        console.error("Files fetch error:", filesError)
        // Handle error appropriately, maybe show an error message
        // For now, we'll proceed with an empty array, but this might hide issues.
        return <div>Error loading data room files.</div>
    }

    // Fetch access list only if owner
    if (isOwner) {
        try {
            // Get access entries with manually joined profiles
            const { data: accessListWithProfiles, error: accessError } = await supabase
                .from('data_room_access')
                .select(`
                    id, 
                    listing_id, 
                    user_id, 
                    created_at
                `)
                .eq('listing_id', listingId);

            if (accessError) {
                console.error("Access list fetch error:", accessError);
                initialAccessList = [];
            } else if (accessListWithProfiles && accessListWithProfiles.length > 0) {
                // Get user IDs from access entries
                const userIds = accessListWithProfiles.map(entry => entry.user_id);

                // Fetch profile information separately
                const { data: profilesData, error: profilesError } = await supabase
                    .from('profiles')
                    .select('user_id, first_name, last_name, email')
                    .in('user_id', userIds);

                if (profilesError) {
                    console.error("Profiles fetch error:", profilesError);
                    initialAccessList = accessListWithProfiles.map(entry => ({
                        ...entry,
                        profiles: null
                    })) as AccessEntry[];
                } else {
                    // Create a map for quick lookup
                    const profileMap = new Map();
                    profilesData?.forEach(profile => {
                        profileMap.set(profile.user_id, profile);
                    });

                    // Manually join the profiles to access entries
                    initialAccessList = accessListWithProfiles.map(entry => ({
                        ...entry,
                        profiles: profileMap.get(entry.user_id) || null
                    })) as AccessEntry[];
                }
            } else {
                initialAccessList = [];
            }
        } catch (error) {
            console.error("Access list fetch error:", error);
            initialAccessList = [];
        }
    }

    // Determine if the user should see the public-only view or the full view
    // The component needs to know if the user *could* see private files if they toggled
    const canSeePrivate = isOwner || hasAccess

    // If user is not owner and has no access, they *only* see public files returned by RLS.
    // If they have access, they *can* see private files.
    // The initial fetch respects RLS, so `allFiles` contains what the user is allowed to see initially.
    // We pass `canSeePrivate` to let the client component know if the "Private" toggle should be enabled.

    return (
        <div className="container mx-auto px-4 py-8 max-w-5xl">
            <DataRoomClient
                listingId={listingId}
                allFiles={allFiles || []} // Ensure it's an array
                listingTitle={listing.title || 'this listing'}
                isOwner={isOwner}
                canSeePrivate={canSeePrivate} // Let client know if private view is possible
                initialAccessList={initialAccessList}
                // Pass owner details needed for messaging
                ownerId={listing.user_id}
                ownerProfile={{
                    firstName: ownerProfile?.first_name ?? null,
                    lastName: ownerProfile?.last_name ?? null,
                    avatar: ownerProfile?.profile_photo ?? null,
                }}
            />
        </div>
    )
} 
