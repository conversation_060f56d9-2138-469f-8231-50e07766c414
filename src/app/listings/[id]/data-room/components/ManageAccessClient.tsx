'use client'

import { createClient } from '@/utils/supabase/client'
import { useState } from 'react'
import { toast } from 'sonner'
import { But<PERSON> } from "@/components/ui/button"
import Input from "@/components/ui/Input"
import { Trash2, UserPlus, Users } from 'lucide-react'
import Image from 'next/image'
import { AccessEntry } from '@/types/access'
import { DeleteModal } from '@/components/ui/DeleteModal'

interface Props {
    listingId: string;
    initialAccessList: AccessEntry[];
}

export default function ManageAccessClient({ listingId, initialAccessList }: Props) {
    const [accessList, setAccessList] = useState<AccessEntry[]>(initialAccessList)
    const [email, setEmail] = useState('')
    const [loading, setLoading] = useState(false)
    const [showDeleteModal, setShowDeleteModal] = useState(false)
    const [accessToRevoke, setAccessToRevoke] = useState<AccessEntry | null>(null)
    const supabase = createClient()

    const handleGrantAccess = async (e: React.FormEvent) => {
        e.preventDefault()
        setLoading(true)

        try {
            // Fetch the user profile to ensure they exist and get details
            const { data: profile, error: profileError } = await supabase
                .from('profiles')
                .select('id, user_id, email, first_name, last_name, profile_photo') // Ensure we select all needed fields
                .eq('email', email)
                .single();

            if (profileError || !profile) {
                toast.error('User not found')
                setLoading(false)
                return
            }

            // Insert into data_room_access
            const { data: accessData, error: insertError } = await supabase
                .from('data_room_access')
                .insert({
                    listing_id: listingId,
                    user_id: profile.user_id, // Use user_id from the fetched profile
                    granted_by: (await supabase.auth.getUser()).data.user?.id
                })
                .select('*') // Select all fields from the inserted access row
                .single();

            if (insertError) throw insertError;
            if (!accessData) throw new Error('Failed to retrieve access entry after insert.');

            // Construct the object based on the AccessEntry type
            // Use data returned from the insert (accessData)
            const newAccessEntry: AccessEntry = {
                id: accessData.id,
                user_id: accessData.user_id,
                created_at: accessData.created_at,
                granted_by: accessData.granted_by,
                listing_id: accessData.listing_id,
                // Use the profile data we fetched earlier
                profiles: profile ? {
                    id: profile.id,
                    email: profile.email,
                    first_name: profile.first_name,
                    last_name: profile.last_name,
                    profile_photo: profile.profile_photo
                } : null
            }

            setAccessList(prev => [newAccessEntry, ...prev])
            setEmail('')
            toast.success('Access granted successfully')
        } catch (error) {
            console.error('Error granting access:', error)
            toast.error('Error granting access')
        } finally {
            setLoading(false)
        }
    }

    const handleRevokeAccess = async (access: AccessEntry) => {
        setAccessToRevoke(access)
        setShowDeleteModal(true)
    }

    const confirmRevokeAccess = async () => {
        if (!accessToRevoke) return
        setLoading(true)

        try {
            const { error } = await supabase
                .from('data_room_access')
                .delete()
                .eq('id', accessToRevoke.id)

            if (error) throw error

            setAccessList(prev => prev.filter(access => access.id !== accessToRevoke.id))
            toast.success('Access revoked successfully')
        } catch (error) {
            console.error('Error revoking access:', error)
            toast.error('Error revoking access')
        } finally {
            setLoading(false)
            setShowDeleteModal(false)
            setAccessToRevoke(null)
        }
    }

    return (
        <div className="rounded-lg border bg-white text-card-foreground shadow">
            <div className="flex flex-col space-y-1.5 p-6">
                <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-gray-500" />
                    <h3 className="text-lg font-semibold">Manage Data Room Access</h3>
                </div>
                <p className="text-sm text-gray-500">
                    Control who can access your confidential business documents
                </p>
            </div>
            <div className="p-6 pt-0">
                <form onSubmit={handleGrantAccess} className="space-y-4">
                    <div className="flex gap-4">
                        <Input
                            type="email"
                            placeholder="Enter user's email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                        />
                        <Button type="submit" disabled={loading}>
                            <UserPlus className="h-4 w-4 mr-2" />
                            Grant Access
                        </Button>
                    </div>
                </form>
            </div>

            <hr className="mx-6 my-2" />

            <div className="p-6 pt-4">
                <div className="space-y-4">
                    <h4 className="text-sm font-medium text-gray-900">Access List</h4>
                    {accessList.length === 0 ? (
                        <p className="text-sm text-gray-500">
                            No users have been granted access yet
                        </p>
                    ) : (
                        <div className="rounded-md border divide-y">
                            {accessList.map((access) => (
                                <div
                                    key={access.id}
                                    className="flex items-center justify-between p-4 bg-white"
                                >
                                    <div className="flex items-center space-x-4">
                                        <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden">
                                            {access.profiles?.profile_photo ? (
                                                <Image
                                                    src={access.profiles.profile_photo}
                                                    alt=""
                                                    width={40}
                                                    height={40}
                                                    className="h-full w-full object-cover"
                                                />
                                            ) : (
                                                <span className="text-lg text-gray-600">
                                                    {access.profiles?.email?.charAt(0).toUpperCase() || 'U'}
                                                </span>
                                            )}
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">
                                                {(access.profiles?.first_name || access.profiles?.last_name)
                                                    ? `${access.profiles.first_name || ''} ${access.profiles.last_name || ''}`.trim()
                                                    : access.profiles?.email || 'Unnamed User'}
                                            </p>
                                            <p className="text-sm text-gray-500">
                                                {access.profiles?.email || 'No Email'}
                                            </p>
                                        </div>
                                    </div>
                                    <Button
                                        onClick={() => handleRevokeAccess(access)}
                                        variant="outline"
                                        size="sm"
                                        className="gap-2 text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 hover:bg-red-50"
                                    >
                                        <Trash2 className="h-4 w-4" />
                                        Revoke Access
                                    </Button>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>

            <DeleteModal
                isOpen={showDeleteModal}
                onClose={() => {
                    setShowDeleteModal(false)
                    setAccessToRevoke(null)
                }}
                onConfirm={confirmRevokeAccess}
                isLoading={loading}
                title="Revoke Access"
                description={`Are you sure you want to revoke access for ${accessToRevoke?.profiles?.email || 'this user'}?`}
            />
        </div>
    )
} 