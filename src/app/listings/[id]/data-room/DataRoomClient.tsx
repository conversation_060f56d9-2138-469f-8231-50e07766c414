'use client'

import { createClient } from '@/utils/supabase/client'
import { useCallback, useState, useMemo } from 'react'
import { useDropzone } from 'react-dropzone'
import { FileText, Trash2, Upload, Download, ChevronDown, Lock, Unlock, Info, MessageSquare } from 'lucide-react'
import { toast } from 'sonner'
import { Button } from "@/components/ui/button"
import * as RadixSwitch from '@radix-ui/react-switch'
import ManageAccessClient from './components/ManageAccessClient'
import { NewMessageModal } from '@/components'
import { AccessEntry } from '@/types/access'
import * as Accordion from '@radix-ui/react-accordion'
import { Database } from '@/types/supabase'

type DataRoomFile = Database['public']['Tables']['data_room_files']['Row']

interface CategorySection {
    id: 'profit_loss' | 'balance_sheet' | 'tax_returns' | 'other'
    title: string
    description: (isOwner: boolean) => string
}

const CATEGORIES: CategorySection[] = [
    {
        id: 'profit_loss',
        title: 'Profit & Loss Statements',
        description: isOwner => isOwner
            ? 'Upload your profit and loss statements to show your business performance over time.'
            : 'View profit and loss statements showing the business performance over time.'
    },
    {
        id: 'balance_sheet',
        title: 'Balance Sheet',
        description: isOwner => isOwner
            ? 'Share your balance sheets to demonstrate your business assets, liabilities, and equity.'
            : 'Review balance sheets showing the business assets, liabilities, and equity.'
    },
    {
        id: 'tax_returns',
        title: 'Business Tax Returns',
        description: isOwner => isOwner
            ? 'Provide tax returns to verify your business financial history.'
            : 'Access business tax returns verifying the financial history.'
    },
    {
        id: 'other',
        title: 'Other Documents',
        description: isOwner => isOwner
            ? 'Upload any additional relevant documents.'
            : 'View additional relevant business documents.'
    }
]

interface DeleteModalProps {
    isOpen: boolean
    onClose: () => void
    onConfirm: () => void
    isLoading: boolean
}

function DeleteModal({ isOpen, onClose, onConfirm, isLoading }: DeleteModalProps) {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-sm mx-4 w-full">
                <h3 className="text-lg font-semibold mb-4">Delete File</h3>
                <p className="text-gray-600 mb-6">
                    Are you sure you want to delete this file? This action cannot be undone.
                </p>
                <div className="flex gap-3">
                    <button
                        onClick={onClose}
                        className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={onConfirm}
                        disabled={isLoading}
                        className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                    >
                        Delete
                    </button>
                </div>
            </div>
        </div>
    );
}

interface Props {
    listingId: string
    allFiles: DataRoomFile[]
    listingTitle: string
    isOwner: boolean
    canSeePrivate: boolean
    initialAccessList: AccessEntry[]
    ownerId: string
    ownerProfile: {
        firstName: string | null
        lastName: string | null
        avatar: string | null
    }
}

export default function DataRoomClient({
    listingId,
    allFiles: initialAllFiles,
    listingTitle,
    isOwner,
    canSeePrivate,
    initialAccessList,
    ownerId,
    ownerProfile
}: Props) {
    const [allFiles, setAllFiles] = useState<DataRoomFile[]>(initialAllFiles)
    const [uploading, setUploading] = useState<string | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [showDeleteModal, setShowDeleteModal] = useState(false)
    const [fileToDelete, setFileToDelete] = useState<string | null>(null)
    const [viewMode, setViewMode] = useState<'public' | 'private'>('public')
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [currentConversationId, setCurrentConversationId] = useState<string | null>(null)
    const supabase = createClient()

    const publicFiles = useMemo(() => allFiles.filter(file => file.is_public), [allFiles])
    const privateFiles = useMemo(() => allFiles.filter(file => !file.is_public), [allFiles])

    const filesToDisplay = useMemo(() => {
        if (viewMode === 'public') {
            return publicFiles
        } else if (viewMode === 'private' && canSeePrivate) {
            return privateFiles
        }
        return publicFiles
    }, [viewMode, canSeePrivate, publicFiles, privateFiles])

    const onDrop = useCallback(async (acceptedFiles: File[], category: CategorySection['id']) => {
        setUploading(category)
        console.log(`onDrop called for category: ${category}. Uploading ${acceptedFiles.length} file(s).`);

        try {
            const { data: { session } } = await supabase.auth.getSession()
            if (!session?.user) throw new Error('No authenticated user')

            for (const file of acceptedFiles) {
                const fileName = `${Date.now()}-${file.name.replace(/[^a-zA-Z0-9.]/g, '-')}`
                const filePath = `${fileName}`

                const { error: uploadError } = await supabase.storage
                    .from('data-room-files')
                    .upload(filePath, file)
                if (uploadError) throw uploadError

                const { data: urlData } = supabase.storage
                    .from('data-room-files')
                    .getPublicUrl(filePath)

                console.log(`Inserting file ${file.name} as private.`);

                const { data, error } = await supabase
                    .from('data_room_files')
                    .insert({
                        listing_id: listingId,
                        file_name: file.name,
                        file_url: urlData.publicUrl,
                        file_size: file.size,
                        file_type: file.type,
                        uploaded_by: session.user.id,
                        category: category,
                        is_public: viewMode === 'public'
                    })
                    .select()
                    .single()

                console.log('Database insert result:', data);

                if (error) throw error

                setAllFiles(prev => [data as DataRoomFile, ...prev])
            }
            toast.success(`${acceptedFiles.length} file(s) uploaded successfully (as ${viewMode === 'public' ? 'public' : 'private'}).`)
        } catch (error) {
            console.error('Error uploading file:', error)
            const errorMessage = error instanceof Error ? error.message : 'Please try again.';
            toast.error(`Error uploading file: ${errorMessage}`)
        } finally {
            setUploading(null)
        }
    }, [listingId, supabase, viewMode])

    const DropzoneComponent = ({ category }: { category: CategorySection['id'] }) => {
        const { getRootProps, getInputProps, isDragActive } = useDropzone({
            onDrop: (acceptedFiles) => onDrop(acceptedFiles, category),
            accept: {
                'application/pdf': ['.pdf'],
                'application/msword': ['.doc'],
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
                'application/vnd.ms-excel': ['.xls'],
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
                'text/csv': ['.csv']
            }
        })

        return (
            <div>
                <div
                    {...getRootProps()}
                    className={`
                        border-2 border-dashed rounded-lg p-6 text-center cursor-pointer
                        transition-colors duration-200 ease-in-out mb-4
                        ${isDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
                    `}
                >
                    <input {...getInputProps()} />
                    <Upload className="mx-auto h-8 w-8 text-gray-400" />
                    <p className="mt-2 text-sm text-gray-600">
                        {isDragActive ? "Drop files here..." : "Drag 'n' drop files, or click to select"}
                    </p>
                    <p className="mt-1 text-xs text-gray-500">
                        Supported: PDF, DOC(X), XLS(X), CSV
                    </p>
                </div>
            </div>
        );
    }

    const handleDeleteClick = (fileId: string) => {
        setFileToDelete(fileId)
        setShowDeleteModal(true)
    }

    const handleDelete = async () => {
        if (!fileToDelete) return
        setIsLoading(true)

        const fileObj = allFiles.find(f => f.id === fileToDelete)

        try {
            const { error: dbError } = await supabase
                .from('data_room_files')
                .delete()
                .eq('id', fileToDelete)

            if (dbError) throw dbError

            if (fileObj?.file_url) {
                try {
                    const url = new URL(fileObj.file_url)
                    const pathParts = url.pathname.split('/')
                    const filePath = pathParts.slice(6).join('/')

                    if (filePath) {
                        const { error: storageError } = await supabase.storage
                            .from('data-room-files')
                            .remove([filePath])

                        if (storageError) {
                            console.warn(`Failed to delete file from storage: ${filePath}`, storageError)
                        } else {
                            console.log(`Successfully deleted from storage: ${filePath}`)
                        }
                    }
                } catch (e) {
                    console.error("Error parsing URL or constructing file path for storage deletion:", e)
                }
            }

            setAllFiles(prev => prev.filter(f => f.id !== fileToDelete))
            toast.success('File deleted successfully')
        } catch (error) {
            console.error('Error deleting file:', error)
            const errorMessage = error instanceof Error ? error.message : 'Please try again.';
            toast.error(`Error deleting file: ${errorMessage}`)
        } finally {
            setIsLoading(false)
            setShowDeleteModal(false)
            setFileToDelete(null)
        }
    }

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes'
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const handleToggleFileVisibility = async (fileId: string, currentStatus: boolean) => {
        const newStatus = !currentStatus;
        const originalFiles = [...allFiles];

        setAllFiles(prevFiles =>
            prevFiles.map(f => f.id === fileId ? { ...f, is_public: newStatus } : f)
        );

        try {
            const { error } = await supabase
                .from('data_room_files')
                .update({ is_public: newStatus })
                .eq('id', fileId)

            if (error) throw error;

            toast.success(`File marked as ${newStatus ? 'public' : 'private'}.`);

        } catch (error) {
            console.error('Error updating file visibility:', error);
            const errorMessage = error instanceof Error ? error.message : 'Please try again.';
            toast.error(`Failed to update file status: ${errorMessage}`);
            setAllFiles(originalFiles);
        }
    };

    const FileItem = ({ file, isOwner, handleDeleteClick, formatFileSize }: { file: DataRoomFile, isOwner: boolean, handleDeleteClick: (id: string) => void, formatFileSize: (bytes: number) => string }) => (
        <div
            className="flex items-center justify-between p-4 hover:bg-white transition-colors duration-150 group gap-2"
        >
            <div className="flex items-center space-x-3 flex-grow min-w-0">
                <span title={file.is_public ? "Public" : "Private"} className="flex-shrink-0">
                    {file.is_public ? <Unlock className="h-5 w-5 text-green-500" /> : <Lock className="h-5 w-5 text-red-500" />}
                </span>
                <FileText className="h-8 w-8 text-gray-400 group-hover:text-gray-600 flex-shrink-0" />
                <div className="overflow-hidden flex-grow">
                    <p className="text-sm font-medium text-gray-900 group-hover:text-gray-700 truncate" title={file.file_name}>
                        {file.file_name}
                    </p>
                    <p className="text-xs text-gray-500 group-hover:text-gray-600">
                        {formatFileSize(file.file_size)} • Uploaded {
                            new Date(file.created_at).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
                        }
                    </p>
                </div>
            </div>
            <div className="flex items-center space-x-2 flex-shrink-0">
                {isOwner && (
                    <div className="flex items-center space-x-1.5" title={`Click to make ${file.is_public ? 'private' : 'public'}`}>
                        <label htmlFor={`vis-toggle-${file.id}`} className="sr-only">
                            Toggle file visibility
                        </label>
                        <RadixSwitch.Root
                            id={`vis-toggle-${file.id}`}
                            checked={file.is_public}
                            onCheckedChange={() => handleToggleFileVisibility(file.id, file.is_public)}
                            className="group relative inline-flex h-[20px] w-[36px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-green-600 data-[state=unchecked]:bg-red-600"
                        >
                            <RadixSwitch.Thumb className="pointer-events-none block h-4 w-4 rounded-full bg-white shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0" />
                        </RadixSwitch.Root>
                    </div>
                )}
                <Button
                    onClick={() => window.open(file.file_url, '_blank')}
                    variant="outline"
                    size="sm"
                    className="gap-1.5 hover:bg-gray-100 hover:text-gray-900 hover:border-gray-400"
                >
                    <Download className="h-4 w-4" />
                    <span className="hidden sm:inline">Download</span>
                </Button>
                {isOwner && (
                    <Button
                        onClick={() => handleDeleteClick(file.id)}
                        variant="outline"
                        size="sm"
                        className="gap-1.5 text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 hover:bg-red-50"
                    >
                        <Trash2 className="h-4 w-4" />
                        <span className="hidden sm:inline">Delete</span>
                    </Button>
                )}
            </div>
        </div>
    )

    const CategorySectionComponent = ({ category }: { category: CategorySection }) => {
        const categoryFiles = filesToDisplay.filter(file => file.category === category.id)

        if (isOwner) {
            return (
                <div className="space-y-6">
                    <div>
                        <h2 className="text-xl font-semibold text-gray-900">{category.title}</h2>
                        <p className="mt-1 text-sm text-gray-500">{category.description(isOwner)}</p>
                    </div>

                    <DropzoneComponent category={category.id} />

                    {uploading === category.id && <div className="text-sm text-gray-600">Uploading...</div>}

                    {categoryFiles.length > 0 ? (
                        <div className="border rounded-lg divide-y bg-gray-50">
                            {categoryFiles.map((file) => (
                                <FileItem key={file.id} file={file} isOwner={isOwner} handleDeleteClick={handleDeleteClick} formatFileSize={formatFileSize} />
                            ))}
                        </div>
                    ) : (
                        <p className="text-sm text-gray-500 text-center py-4">
                            No {viewMode === 'private' ? 'private' : 'public'} documents in this category.
                        </p>
                    )}
                </div>
            )
        }

        const displayFilesForAccordion = filesToDisplay.filter(file => file.category === category.id)
        const publicFilesInAccordion = publicFiles.filter(file => file.category === category.id).length
        const privateFilesInAccordion = privateFiles.filter(file => file.category === category.id).length

        return (
            <Accordion.Item value={category.id} className="border rounded-lg overflow-hidden">
                <Accordion.Trigger className="flex items-center justify-between w-full p-4 bg-white hover:bg-gray-50 transition-colors group">
                    <div className="flex flex-col items-start text-left">
                        <div className="text-lg font-semibold text-gray-900">{category.title}</div>
                        {canSeePrivate ? (
                            <div className="text-sm text-gray-500">
                                {viewMode === 'public'
                                    ? `${publicFilesInAccordion} public document(s)`
                                    : `${privateFilesInAccordion} private document(s)`
                                } available
                            </div>
                        ) : (
                            <div className="text-sm text-gray-500">
                                {publicFilesInAccordion} public document(s) available
                            </div>
                        )}
                    </div>
                    <ChevronDown className="h-5 w-5 text-gray-500 transition-transform duration-200 ease-out group-data-[state=open]:rotate-180" />
                </Accordion.Trigger>

                <Accordion.Content className="data-[state=open]:animate-slideDown data-[state=closed]:animate-slideUp overflow-hidden">
                    <div className="p-4 bg-gray-50">
                        {displayFilesForAccordion.length > 0 ? (
                            <div className="border border-gray-200 rounded-lg overflow-hidden">
                                <div className="divide-y divide-gray-200 bg-white">
                                    {displayFilesForAccordion.map((file) => (
                                        <FileItem key={file.id} file={file} isOwner={false} handleDeleteClick={() => { }} formatFileSize={formatFileSize} />
                                    ))}
                                </div>
                            </div>
                        ) : (
                            <p className="text-sm text-gray-500">
                                No {viewMode === 'private' ? 'private' : 'public'} documents available in this category.
                            </p>
                        )}
                    </div>
                </Accordion.Content>
            </Accordion.Item>
        )
    }

    const handleSendMessageClick = async () => {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.user?.id || !ownerId) {
            toast.error('Could not initiate message. User or owner not found.');
            return;
        }
        if (session.user.id === ownerId) {
            toast.info('You cannot send a message to yourself.');
            return;
        }

        setIsLoading(true);
        try {
            const participants = [session.user.id, ownerId].sort();
            const participant1_id = participants[0];
            const participant2_id = participants[1];

            const { data: existingConv, error: convError } = await supabase
                .from('conversations')
                .select('id')
                .eq('listing_id', listingId)
                .eq('participant1_id', participant1_id)
                .eq('participant2_id', participant2_id)
                .maybeSingle();

            if (convError) throw convError;

            let conversationId = existingConv?.id;

            if (!conversationId) {
                const { data: newConv, error: insertConvError } = await supabase
                    .from('conversations')
                    .insert({
                        listing_id: listingId,
                        participant1_id: participant1_id,
                        participant2_id: participant2_id
                    })
                    .select('id')
                    .single();

                if (insertConvError) throw insertConvError;
                if (!newConv?.id) throw new Error("Failed to create or retrieve conversation ID.");
                conversationId = newConv.id;
            }

            setCurrentConversationId(conversationId);
            setIsModalOpen(true);

        } catch (error: unknown) {
            console.error('Error initiating message:', error);
            const errorMessage = error instanceof Error ? error.message : 'Please try again.';
            toast.error(`Failed to initiate message: ${errorMessage}`);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="space-y-8">
            {/* Header Card */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-50 rounded-lg">
                            <FileText className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                            <h1 className="text-2xl font-semibold text-gray-900">Data Room</h1>
                            <p className="text-sm text-gray-600 mt-1">
                                Documents for {listingTitle}
                            </p>
                        </div>
                    </div>
                    {canSeePrivate && (
                        <div className="flex items-center space-x-3 border p-2 rounded-lg bg-gray-50 self-end sm:self-center">
                            <label htmlFor="view-mode-toggle" className={`text-sm font-medium cursor-pointer ${viewMode === 'public' ? 'text-green-600' : 'text-gray-500'}`}>
                                <Unlock className="inline h-4 w-4 mr-1" />Public
                            </label>
                            <RadixSwitch.Root
                                id="view-mode-toggle"
                                checked={viewMode === 'private'}
                                onCheckedChange={(checked: boolean) => setViewMode(checked ? 'private' : 'public')}
                                className="group relative inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-red-600 data-[state=unchecked]:bg-gray-300"
                            >
                                <RadixSwitch.Thumb className="pointer-events-none block h-5 w-5 rounded-full bg-white shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0" />
                            </RadixSwitch.Root>
                            <label htmlFor="view-mode-toggle" className={`text-sm font-medium cursor-pointer ${viewMode === 'private' ? 'text-red-600' : 'text-gray-500'}`}>
                                <Lock className="inline h-4 w-4 mr-1" />Private
                            </label>
                        </div>
                    )}
                </div>
            </div>

            {isOwner && viewMode === 'public' && (
                <div className="bg-blue-50 border border-blue-200 text-blue-800 p-4 rounded-lg flex items-start gap-3 shadow-sm">
                    <Info className="h-5 w-5 flex-shrink-0 mt-0.5" />
                    <div>
                        <h4 className="font-semibold text-sm">Public Upload Mode</h4>
                        <p className="text-xs mt-1">
                            Files uploaded here will be publicly visible. To upload private documents for invited users only, please switch to the <span className="font-medium">Private</span> view.
                            You can always change a file&apos;s visibility later using the <Lock className="inline h-3 w-3 mx-0.5" />/<Unlock className="inline h-3 w-3 mx-0.5" /> toggle next to the file.
                        </p>
                    </div>
                </div>
            )}

            {!isOwner && !canSeePrivate && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 text-center">
                    <div className="flex flex-col items-center max-w-md mx-auto">
                        <div className="p-4 bg-gray-50 rounded-full mb-6">
                            <Lock className="w-12 h-12 text-gray-400" />
                        </div>
                        <h4 className="text-xl font-semibold text-gray-900 mb-3">Need Full Access?</h4>
                        <p className="text-sm text-gray-600 mb-8 leading-relaxed">
                            This data room contains both public and private documents. To view private files, you&apos;ll need access granted by the owner.
                        </p>
                        <Button
                            onClick={handleSendMessageClick}
                            disabled={isLoading}
                            className="inline-flex items-center gap-2 bg-gray-800 text-white hover:bg-gray-700 focus-visible:ring-gray-500 font-medium"
                        >
                            <MessageSquare className="w-4 h-4" />
                            Send Message to Owner
                        </Button>
                    </div>
                </div>
            )}

            <div className="space-y-8">
                {!isOwner ? (
                    <Accordion.Root type="single" collapsible className="space-y-4">
                        {CATEGORIES.map((category) => (
                            <CategorySectionComponent key={category.id} category={category} />
                        ))}
                    </Accordion.Root>
                ) : (
                    <div className="space-y-12">
                        {CATEGORIES.map((category, index) => (
                            <div key={category.id}>
                                {index > 0 && <hr className="my-12 border-gray-200" />}
                                <CategorySectionComponent category={category} />
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {isOwner && (
                <>
                    <hr className="my-12 border-gray-200" />
                    <ManageAccessClient
                        listingId={listingId}
                        initialAccessList={initialAccessList}
                    />
                </>
            )}

            <DeleteModal
                isOpen={showDeleteModal}
                onClose={() => setShowDeleteModal(false)}
                onConfirm={handleDelete}
                isLoading={isLoading}
            />

            <NewMessageModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                recipientId={ownerId}
                listingId={listingId}
                ownerName={{
                    firstName: ownerProfile.firstName ?? '',
                    lastName: ownerProfile.lastName ?? ''
                }}
                ownerAvatar={ownerProfile.avatar}
                listingName={listingTitle}
                conversationId={currentConversationId || undefined}
            />
        </div>
    )
} 