'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Lock } from "lucide-react"
import Link from "next/link"

interface Props {
    listingId: string
    listingTitle: string
}

export default function NoAccess({ listingId, listingTitle }: Props) {
    return (
        <div className="flex-grow bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
                <div className="text-center">
                    <Lock className="mx-auto h-12 w-12 text-gray-400" />
                    <h2 className="mt-2 text-lg font-medium text-gray-900">
                        Data Room Access Required
                    </h2>
                    <p className="mt-1 text-sm text-gray-500">
                        You don&apos;t have access to view the data room for {listingTitle}.
                        Contact the listing owner to request access.
                    </p>
                    <div className="mt-6">
                        <Link href={`/listings/${listingId}`}>
                            <Button>
                                Back to Listing
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    )
} 