'use client';

import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';
import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { Search, CheckCircle2, DollarSign } from 'lucide-react';
import { getIndustryMetricsSummary, MetricsSummary } from '@/utils/industryMetricsMapping';
import { findIndustryFromPlaceTypes } from '@/utils/placeTypeMapping';

// Import step components
import QuickStartStep from './steps/QuickStartStep';
import AutoFillStep from './steps/AutoFillStep';
import ValuationStep from './steps/ValuationStep';
import SuccessStep from './steps/SuccessStep';
import StepIndicator from './StepIndicator';

interface ListingForm {
    // Basic Information
    title: string;
    description: string;
    price: string;
    website: string;
    industry_id: string;
    sub_industry_id: string;
    image_url?: string;
    status: 'live' | 'draft';

    // Business Details
    year_established: string;
    legal_structure?: string;
    location?: string;
    team_size: string;

    // Financial Information
    annual_revenue_ttm: {
        min: number | null;
        max: number | null;
    } | null;
    annual_net_profit_ttm: {
        min: number | null;
        max: number | null;
    } | null;
    last_month_revenue: {
        min: number | null;
        max: number | null;
    } | null;
    last_month_profit: {
        min: number | null;
        max: number | null;
    } | null;
    recurring_revenue: {
        min: number | null;
        max: number | null;
    } | null;

    // Business Metrics
    active_customers?: string;
    growth_rate?: string;
    reason_for_selling?: string;

    // Additional fields
    state_id: string;
    city: string;
    street_address: string;
    postal_code: string;
    latitude?: number | null;
    longitude?: number | null;
    naics_code?: string | null;

    // Valuation result
    valuation?: string;

    // Remove the index signature and explicitly define all possible types
    [key: string]: string | number | boolean | object | undefined | null;
}

const STEPS = [
    {
        id: 1,
        title: 'Quick Start',
        icon: Search
    },
    {
        id: 2,
        title: 'Auto-Fill & Confirmation',
        icon: CheckCircle2
    },
    {
        id: 3,
        title: 'Instant Valuation',
        icon: DollarSign
    }
];

// Define the prop types based on the component requirements
interface AutoFillFormData {
    title: string;
    description: string;
    website: string;
    industry_id: string;
    sub_industry_id: string;
    state_id: string;
    city: string;
    street_address: string;
    postal_code: string;
    year_established: string;
    team_size: string;
    reason_for_selling: string | null;
    [key: string]: string | null;
}

interface AddListingFormProps {
    onSuccess?: () => void;
}

export default function AddListingForm({ onSuccess }: AddListingFormProps) {
    const router = useRouter();
    const [supabase] = useState(() => createClient());
    const [loading, setLoading] = useState(false);
    const [industries, setIndustries] = useState<Array<{ id: string, name: string, naics_code: string | null }>>([]);
    const [subIndustries, setSubIndustries] = useState<Array<{ id: string, name: string, industry_id: string, naics_code: string | null }>>([]);
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const [formData, setFormData] = useState<ListingForm>({
        title: '',
        description: '',
        price: '',
        website: '',
        industry_id: '',
        sub_industry_id: '',
        year_established: '',
        legal_structure: '',
        location: '',
        team_size: '',
        annual_revenue_ttm: null,
        annual_net_profit_ttm: null,
        last_month_revenue: null,
        last_month_profit: null,
        recurring_revenue: null,
        active_customers: '',
        growth_rate: '',
        reason_for_selling: '',
        state_id: '',
        city: '',
        street_address: '',
        postal_code: '',
        latitude: null,
        longitude: null,
        naics_code: null,
        status: 'live',
    });
    const [generatingDraft, setGeneratingDraft] = useState(false);
    const [states, setStates] = useState<Array<{ id: string, name: string, code: string }>>([]);
    const [currentStep, setCurrentStep] = useState(1);
    const [descriptionStatus, setDescriptionStatus] = useState<{
        type: 'error' | 'info' | null;
        message: string | null;
    }>({ type: null, message: null });
    const [industryMetrics, setIndustryMetrics] = useState<MetricsSummary | null>(null);
    const [selectedIndustryName, setSelectedIndustryName] = useState<string>('');
    const [selectedSubIndustryName, setSelectedSubIndustryName] = useState<string>('');
    const [isSearching, setIsSearching] = useState(false);
    const [businessImageUrl, setBusinessImageUrl] = useState<string | null>(null);
    const [valuationCalculated, setValuationCalculated] = useState(false);
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [aiGeneratedImageUrls, setAiGeneratedImageUrls] = useState<string[]>([]);
    const [selectedAiImageUrl, setSelectedAiImageUrl] = useState<string | null>(null);
    const [isGeneratingImage, setIsGeneratingImage] = useState(false);

    // Define handlePriceChange here, before the useEffect that uses it
    const handlePriceChange = useCallback((newPrice: number) => {
        console.log('AddListingForm: handlePriceChange called with:', newPrice);
        // Ensure price is not NaN and is non-negative
        const validPrice = !isNaN(newPrice) && newPrice >= 0 ? newPrice : 0;
        setFormData(prevFormData => ({
            ...prevFormData,
            price: String(Math.round(validPrice)) // Store as string, rounded
        }));
    }, []);

    useEffect(() => {
        const fetchData = async () => {
            const { data: industriesData } = await supabase
                .from('industries')
                .select('id, name, naics_code');

            if (industriesData) {
                setIndustries(industriesData as Array<{ id: string, name: string, naics_code: string | null }>);
            }

            const { data: statesData } = await supabase
                .from('states')
                .select('*');

            if (statesData) {
                setStates(statesData);
            }
        };

        fetchData();
    }, [supabase]);

    useEffect(() => {
        const fetchSubIndustries = async () => {
            if (!formData.industry_id) return;

            const { data } = await supabase
                .from('sub_industries')
                .select('id, name, industry_id, naics_code')
                .eq('industry_id', formData.industry_id);

            if (data) {
                setSubIndustries(data as Array<{ id: string, name: string, industry_id: string, naics_code: string | null }>);
            }
        };

        fetchSubIndustries();
    }, [supabase, formData.industry_id]);

    useEffect(() => {
        const fetchMetrics = async () => {
            if (formData.industry_id && currentStep === 3) {
                try {
                    const metrics = await getIndustryMetricsSummary(formData.industry_id, formData.sub_industry_id);
                    // Only update if the metrics are different
                    if (JSON.stringify(metrics) !== JSON.stringify(industryMetrics)) {
                        setIndustryMetrics(metrics);
                        setValuationCalculated(true);
                    }
                } catch (error) {
                    console.error('Error fetching metrics:', error);
                }
            }
        };

        fetchMetrics();
    }, [formData.industry_id, formData.sub_industry_id, currentStep, industryMetrics]);

    // Effect to set initial price based on metrics
    useEffect(() => {
        if (
            industryMetrics &&
            industryMetrics.medianAskingPrice &&
            (!formData.price || formData.price === '0') // Only set if price isn't already entered/set
        ) {
            console.log("AddListingForm: Setting initial price from metrics", industryMetrics.medianAskingPrice);
            const initialPrice = Math.round(industryMetrics.medianAskingPrice);
            // Use the existing handler to ensure consistent state update
            handlePriceChange(initialPrice);
        }
        // Intentionally excluding formData.price from dependencies to only run when metrics change
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [industryMetrics, handlePriceChange]);

    const formatCurrency = (amount: string | number) => {
        const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
        if (isNaN(numericAmount)) return '$--'; // Handle NaN
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(numericAmount);
    };

    // Add a formatter without the currency symbol
    const formatNumber = (amount: string | number) => {
        const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
        if (isNaN(numericAmount)) return '--'; // Handle NaN
        return new Intl.NumberFormat('en-US', {
            style: 'decimal', // Use decimal style
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(numericAmount);
    };

    const handleChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
    ) => {
        const { name, value } = e.target;

        if (name === 'industry_id') {
            const selectedIndustry = industries.find(ind => ind.id === value);
            setSelectedIndustryName(selectedIndustry?.name || '');
            setSelectedSubIndustryName('');
        }

        if (name === 'sub_industry_id') {
            const selectedSubIndustry = subIndustries.find(sub => sub.id === value);
            setSelectedSubIndustryName(selectedSubIndustry?.name || '');
        }

        // List of fields that should be formatted as currency  
        const currencyFields = [
            'annual_revenue_ttm',
            'annual_net_profit_ttm',
            'last_month_revenue',
            'last_month_profit',
            'recurring_revenue',
            'price'
        ];

        if (currencyFields.includes(name)) {
            // Store the raw number but display formatted
            const rawValue = value.replace(/[^0-9.]/g, '');
            setFormData(prev => ({
                ...prev,
                [name]: rawValue
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setImageFile(file);
            // Create preview URL
            const previewUrl = URL.createObjectURL(file);
            setImagePreview(previewUrl);
        }
    };

    const uploadImage = async (file: File): Promise<string> => {
        const fileExt = file.name.split('.').pop();
        const fileName = `${Math.random()}.${fileExt}`;
        const filePath = `${fileName}`;

        const { error: uploadError } = await supabase.storage
            .from('listing-images')
            .upload(filePath, file);

        if (uploadError) {
            throw uploadError;
        }

        const { data: { publicUrl } } = supabase.storage
            .from('listing-images')
            .getPublicUrl(filePath);

        return publicUrl;
    };

    const generateDescription = async () => {
        if (!formData.website) {
            toast.error('Please enter a website URL first');
            return;
        }

        let websiteUrl = formData.website;
        if (!websiteUrl.startsWith('http://') && !websiteUrl.startsWith('https://')) {
            websiteUrl = 'https://' + websiteUrl;
        }

        setGeneratingDraft(true);
        setDescriptionStatus({ type: 'info', message: 'Generating description...' });

        try {
            const response = await fetch('/api/generate-description', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url: websiteUrl }),
            });

            if (!response.ok) {
                setDescriptionStatus({
                    type: 'error',
                    message: 'We were unable to use our AI to come up with a description.'
                });
                return;
            }

            const data = await response.json();

            if (data.description) {
                setFormData(prev => ({
                    ...prev,
                    description: data.description
                }));
                setDescriptionStatus({ type: null, message: null });
                toast.success('Description draft generated!');
            }
        } catch {
            setDescriptionStatus({
                type: 'error',
                message: 'We were unable to use our AI to come up with a description.'
            });
        } finally {
            setGeneratingDraft(false);
        }
    };

    const generateAnonymizedDescription = async (originalDescription: string) => {
        try {
            const response = await fetch('/api/generate-anonymized-description', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ description: originalDescription }),
            });

            if (!response.ok) {
                throw new Error('Failed to generate anonymized description');
            }

            const data = await response.json();
            return data.description;
        } catch (error) {
            console.error('Error generating anonymized description:', error);
            return null;
        }
    };

    const handleSubmit = async () => {
        setLoading(true);

        try {
            const { data: { session } } = await supabase.auth.getSession();

            if (!session?.user?.id) {
                throw new Error('No session found');
            }

            // Validate required fields
            if (!formData.industry_id) {
                toast.error('Please select an industry');
                return;
            }

            if (!formData.sub_industry_id) {
                toast.error('Please select a sub-industry');
                return;
            }

            // Helper function to safely parse numbers within PostgreSQL numeric(12,2) limits
            const parseAmount = (value: string | null): number | null => {
                if (!value) return null;
                const num = parseFloat(value.replace(/[^0-9.-]/g, ''));
                // Check if number is within PostgreSQL numeric(12,2) limits
                if (num >= 1e10) {
                    throw new Error('Amount exceeds maximum allowed value (9,999,999,999.99)');
                }
                return num;
            };

            let imageUrl = '';

            // If we have an uploaded image file, use that
            if (imageFile) {
                imageUrl = await uploadImage(imageFile);
            }
            // Otherwise, if we have a Google image URL, fetch and upload that
            else if (businessImageUrl) {
                try {
                    // Fetch the image from our proxy
                    const response = await fetch(businessImageUrl);
                    const blob = await response.blob();

                    // Create a File object from the blob
                    const googleImageFile = new File([blob], 'google-business-image.jpg', { type: 'image/jpeg' });

                    // Upload the file
                    imageUrl = await uploadImage(googleImageFile);
                } catch (error) {
                    console.error('Error uploading Google image:', error);
                    // Continue without the image if there's an error
                }
            }

            // Generate anonymized description
            const anonymizedDescription = await generateAnonymizedDescription(formData.description);

            // Create the main listing
            const listing = {
                user_id: session.user.id,
                title: formData.title.trim(),
                description: formData.description.trim(),
                price: parseAmount(formData.price) || 0,
                website: formData.website.trim(),
                industry_id: formData.industry_id,
                sub_industry_id: formData.sub_industry_id,
                image_url: imageUrl,
                status: formData.status,
            };

            const { data: listingData, error: listingError } = await supabase
                .from('listings')
                .insert([listing])
                .select()
                .single();

            if (listingError) {
                console.error('Listing Error:', listingError);
                throw new Error(listingError.message);
            }

            if (!listingData?.id) {
                throw new Error('No listing ID returned');
            }

            // Generate anonymized title
            const stateName = states.find(s => s.id === formData.state_id)?.name || 'Undisclosed Location';
            const anonymousTitle = `${selectedIndustryName} Business in ${stateName}`;

            // Create the anonymized version with title, description, and image
            const { error: anonymizedError } = await supabase
                .from('listing_anonymized_details')
                .insert({
                    listing_id: listingData.id,
                    anonymous_title: anonymousTitle,
                    anonymous_description: anonymizedDescription,
                    anonymous_image_url: selectedAiImageUrl
                });

            if (anonymizedError) {
                console.error('Error creating anonymized details:', anonymizedError);
                // Continue with the flow even if this fails
            }

            // Insert the listing details
            let naicsCodeToSave: string | null = null;
            if (formData.sub_industry_id) {
                const selectedSubIndustry = subIndustries.find(sub => sub.id === formData.sub_industry_id);
                if (selectedSubIndustry) {
                    naicsCodeToSave = selectedSubIndustry.naics_code || null;
                }
            } else if (formData.industry_id) {
                const selectedIndustry = industries.find(ind => ind.id === formData.industry_id);
                if (selectedIndustry) {
                    naicsCodeToSave = selectedIndustry.naics_code || null;
                }
            }

            const listingDetails = {
                listing_id: listingData.id,
                year_established: parseInt(formData.year_established) || null,
                legal_structure: formData.legal_structure || null,
                team_size: parseInt(formData.team_size) || null,
                active_customers: parseInt(formData.active_customers || '') || null,
                growth_rate: formData.growth_rate || null,
                reason_for_selling: formData.reason_for_selling || null,
                state_id: formData.state_id || null,
                city: formData.city || null,
                street_address: formData.street_address || null,
                postal_code: formData.postal_code || null,
                latitude: formData.latitude || null,
                longitude: formData.longitude || null,
                naics_code: naicsCodeToSave,
                annual_revenue_ttm_min: formData.annual_revenue_ttm?.min,
                annual_revenue_ttm_max: formData.annual_revenue_ttm?.max,
                annual_net_profit_ttm_min: formData.annual_net_profit_ttm?.min,
                annual_net_profit_ttm_max: formData.annual_net_profit_ttm?.max,
                last_month_revenue_min: formData.last_month_revenue?.min,
                last_month_revenue_max: formData.last_month_revenue?.max,
                last_month_profit_min: formData.last_month_profit?.min,
                last_month_profit_max: formData.last_month_profit?.max,
                recurring_revenue_min: formData.recurring_revenue?.min,
                recurring_revenue_max: formData.recurring_revenue?.max,
            };

            // Log coordinates with type just before sending to DB
            console.log(
                'Submitting Lat/Lon:',
                formData.latitude,
                '(Type:', typeof formData.latitude, ')',
                formData.longitude,
                '(Type:', typeof formData.longitude, ')'
            );
            console.log('Full listingDetails object:', listingDetails);

            const { error: detailsError } = await supabase
                .from('listing_details')
                .insert([listingDetails]);

            if (detailsError) {
                console.error('Error inserting listing details:', detailsError);
                // Attempt to delete the original listing if details fail, to avoid orphaned listings
                await supabase.from('listings').delete().eq('id', listingData.id);
                toast.error(`Failed to save listing details: ${detailsError.message}`);
                setLoading(false);
                return;
            }

            // *** NEW: Call API to update FIPS code if lat/lon exist ***
            if (listingData.id && formData.latitude && formData.longitude) {
                try {
                    const fipsResponse = await fetch('/api/fcc/get-fips', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            listing_id: listingData.id,
                            latitude: formData.latitude,
                            longitude: formData.longitude,
                        }),
                    });

                    if (!fipsResponse.ok) {
                        const fipsErrorData = await fipsResponse.json();
                        console.warn('Failed to update FIPS code:', fipsErrorData.error);
                        toast.warning(`Listing created, but could not fetch FIPS code: ${fipsErrorData.error || 'Unknown error'}`);
                    } else {
                        const fipsSuccessData = await fipsResponse.json();
                        console.log('FIPS code update response:', fipsSuccessData.message);
                        // Optionally show a success toast for FIPS update if needed
                    }
                } catch (fipsApiError) {
                    console.warn('Error calling FIPS update API:', fipsApiError);
                    toast.warning('Listing created, but encountered an error calling the FIPS update service.');
                }
            }
            // *** END NEW SECTION ***

            toast.success('Listing created successfully!');

            // Trigger email notifications for matches (5 minutes delayed)
            setTimeout(async () => {
                try {
                    const notificationResponse = await fetch('/api/trigger-match-notifications', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    });

                    if (notificationResponse.ok) {
                        console.log('✅ Email notifications triggered successfully');
                    } else {
                        console.warn('⚠️ Failed to trigger email notifications');
                    }
                } catch (error) {
                    console.warn('⚠️ Error triggering email notifications:', error);
                }
            }, 5 * 60 * 1000); // 5 minutes delay

            router.push('/listings');

            // After successful submission
            setIsSubmitted(true);
            setCurrentStep(4); // Assuming you have 3 steps before this

            // If modal onSuccess callback is provided, don't call it yet
            // We'll call it when the user navigates away from the success screen
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            toast.error(`Error creating listing: ${errorMessage}`);
            console.error('Full error details:', error);
        } finally {
            setLoading(false);
        }
    };

    // Function to handle when user leaves the success screen
    const handleLeaveSuccess = () => {
        if (onSuccess) {
            onSuccess();
        }
    };

    const handleRevenueChange = useCallback((range: { min: number; max: number }) => {
        setFormData(prev => ({
            ...prev,
            annual_revenue_ttm: range
        }));
    }, []);

    const handleProfitChange = useCallback((range: { min: number; max: number }) => {
        setFormData(prev => ({
            ...prev,
            annual_net_profit_ttm: range
        }));
    }, []);

    const handleLastMonthRevenueChange = useCallback((range: { min: number; max: number }) => {
        setFormData(prev => ({
            ...prev,
            last_month_revenue: range
        }));
    }, []);

    const handleLastMonthProfitChange = useCallback((range: { min: number; max: number }) => {
        setFormData(prev => ({
            ...prev,
            last_month_profit: range
        }));
    }, []);

    const handleRecurringRevenueChange = useCallback((range: { min: number; max: number }) => {
        setFormData(prev => ({
            ...prev,
            recurring_revenue: range
        }));
    }, []);

    // Create a function to search for business and move to step 2
    const searchBusiness = async () => {
        if (!formData.title || !formData.postal_code) {
            toast.error('Please enter a business name and zip code');
            return;
        }

        setIsSearching(true);

        try {
            // Log the search parameters
            console.log('Searching for business:', {
                name: formData.title,
                postalCode: formData.postal_code
            });

            const response = await fetch(`/api/places/search`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: formData.title,
                    postalCode: formData.postal_code
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to search for business');
            }

            const data = await response.json();
            console.log('Search results:', data);

            if (data.results && data.results.length > 0) {
                // Take the first result and fetch details
                const business = data.results[0];
                console.log('Selected business:', business);

                // Fetch additional details
                if (business.placeId) {
                    await fetchBusinessDetails(business.placeId);
                    toast.success('Business details loaded successfully!');
                } else {
                    toast.warning('Business found but no place ID available. Some details may be missing.');

                    // Update basic information from the search result
                    setFormData(prev => ({
                        ...prev,
                        title: business.name || prev.title,
                        // Add any other available fields from the search result
                    }));
                }
            } else {
                // No businesses found, but still proceed to next step with manual entry
                toast.info('No exact match found. You can manually enter your business details.');
            }

            // Always move to the next step, even if no business is found
            setCurrentStep(2);
        } catch (error) {
            console.error('Error searching for business:', error);
            toast.error('Failed to search for business. Please try again or proceed with manual entry.');

            // Still move to the next step even if there's an error
            setCurrentStep(2);
        } finally {
            setIsSearching(false);
        }
    };

    // Function to fetch business details
    const fetchBusinessDetails = async (placeId: string) => {
        try {
            const response = await fetch('/api/places/details', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ placeId }),
            });

            if (!response.ok) {
                throw new Error('Failed to fetch business details');
            }

            const data = await response.json();
            console.log('Business details:', data);

            // Find matching industry and sub-industry if types are available
            let industryId = formData.industry_id;
            let subIndustryId = formData.sub_industry_id;

            if (data.types && data.types.length > 0) {
                // First, find the restaurant industry
                const restaurantIndustry = industries.find(i =>
                    i.name.toLowerCase().includes('restaurant') ||
                    i.name.toLowerCase().includes('food'));

                if (restaurantIndustry) {
                    // Pre-fetch sub-industries for the restaurant industry
                    const { data: subIndustriesData } = await supabase
                        .from('sub_industries')
                        .select('*')
                        .eq('industry_id', restaurantIndustry.id);

                    if (subIndustriesData && subIndustriesData.length > 0) {
                        // Update the sub-industries state
                        setSubIndustries(subIndustriesData);

                        // Set the industry ID to restaurant industry
                        industryId = restaurantIndustry.id;

                        // DIRECT NAME-BASED MATCHING: Check business name first for specific types
                        // This takes precedence over the general type matching
                        let nameBasedMatch = false;

                        // Check for pizza in the business name
                        if (data.name && data.name.toLowerCase().includes('pizza')) {
                            const pizzaSubIndustry = subIndustriesData.find(si =>
                                si.name.toLowerCase().includes('pizza'));

                            if (pizzaSubIndustry) {
                                subIndustryId = pizzaSubIndustry.id;
                                nameBasedMatch = true;
                            } else {
                                // Fallback to Italian if no pizza sub-industry
                                const italianSubIndustry = subIndustriesData.find(si =>
                                    si.name.toLowerCase().includes('italian'));

                                if (italianSubIndustry) {
                                    subIndustryId = italianSubIndustry.id;
                                    nameBasedMatch = true;
                                }
                            }
                        }

                        // If we didn't find a match based on the name, use the type-based matching
                        if (!nameBasedMatch) {
                            // Now call findIndustryFromPlaceTypes with the loaded sub-industries
                            const { industryId: matchedIndustryId, subIndustryId: matchedSubIndustryId } =
                                findIndustryFromPlaceTypes(
                                    data.types,
                                    industries as Array<{ id: string, name: string }>,
                                    subIndustriesData as Array<{ id: string, name: string, industry_id: string }>,
                                    data.name
                                );

                            if (matchedIndustryId) {
                                industryId = matchedIndustryId;

                                // Use the matched sub-industry or the first one
                                if (matchedSubIndustryId) {
                                    subIndustryId = matchedSubIndustryId;
                                } else if (subIndustriesData.length > 0) {
                                    subIndustryId = subIndustriesData[0].id;
                                }
                            }
                        }

                        // Update the selected industry and sub-industry names for display
                        if (industryId) {
                            const selectedIndustry = industries.find(ind => ind.id === industryId);
                            if (selectedIndustry) {
                                setSelectedIndustryName(selectedIndustry.name);
                            }
                        }

                        if (subIndustryId) {
                            const selectedSubIndustry = subIndustriesData.find(sub => sub.id === subIndustryId);
                            if (selectedSubIndustry) {
                                setSelectedSubIndustryName(selectedSubIndustry.name);
                            }
                        }
                    }
                }
            }

            // Update form with business details
            setFormData(prev => {
                const updatedData = {
                    ...prev,
                    title: data.name || prev.title,
                    website: data.website || prev.website,
                    // Parse address components if available
                    city: data.addressComponents?.locality || prev.city,
                    postal_code: data.addressComponents?.postalCode || prev.postal_code,
                    street_address: data.addressComponents?.streetAddress || prev.street_address,
                    // If we have state data, find the matching state_id
                    state_id: data.addressComponents?.state ?
                        states.find(s => s.code === data.addressComponents.state)?.id || prev.state_id :
                        prev.state_id,
                    // Set the industry and sub-industry
                    industry_id: industryId || prev.industry_id,
                    sub_industry_id: subIndustryId || prev.sub_industry_id,
                    // Add latitude and longitude from geometry if available
                    latitude: data.geometry?.location?.lat || null,
                    longitude: data.geometry?.location?.lng || null,
                };

                console.log('Extracted Lat/Lon from API:', data.geometry?.location?.lat, data.geometry?.location?.lng);

                console.log('Updated form data:', updatedData);
                return updatedData;
            });

            // Set the business image URL if available
            if (data.photoUrl) {
                setBusinessImageUrl(data.photoUrl);
                setImagePreview(data.photoUrl);
            }

            // Generate description if we have a website
            if (data.website) {
                generateDescription();
            }
        } catch (error) {
            console.error('Error fetching business details:', error);
            toast.error('Failed to fetch additional business details');
        }
    };

    const handleGenerateImage = async () => {
        if (!formData.description) return;

        setIsGeneratingImage(true);
        try {
            const response = await fetch('/api/generate-image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    description: formData.description,
                    count: 5 // Request 5 images
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                console.error('Image generation error:', data);
                throw new Error(data.error || 'Failed to generate image');
            }

            // Handle both the single imageUrl format and the new imageUrls array format
            if (data.imageUrls && Array.isArray(data.imageUrls)) {
                setAiGeneratedImageUrls(data.imageUrls);
                // Set the first image as selected by default
                setSelectedAiImageUrl(data.imageUrls[0] || null);
                toast.success('Multiple preview images generated successfully!');
            } else if (data.imageUrl) {
                // Fallback for older API version that returns a single image
                setAiGeneratedImageUrls([data.imageUrl]);
                setSelectedAiImageUrl(data.imageUrl);
                toast.success('Preview image generated successfully!');
            } else {
                throw new Error('No image URLs returned from the API');
            }

        } catch (error) {
            console.error('Error generating image:', error);
            toast.error(error instanceof Error ? error.message : 'Failed to generate preview image');
        } finally {
            setIsGeneratingImage(false);
        }
    };

    const handleSelectAiImage = (imageUrl: string) => {
        setSelectedAiImageUrl(imageUrl);
    };

    return (
        <div className="relative">
            {/* Only show StepIndicator when not in success state */}
            {!isSubmitted && <StepIndicator currentStep={currentStep} steps={STEPS} />}

            <div className="bg-gray-50 rounded-lg overflow-hidden">
                {isSubmitted ? (
                    <SuccessStep onNavigateAway={handleLeaveSuccess} />
                ) : (
                    <>
                        {currentStep === 1 && (
                            <QuickStartStep
                                formData={formData}
                                onFormChange={(name: string, value: string) => handleChange({ target: { name, value } } as React.ChangeEvent<HTMLInputElement>)}
                                onSearchBusiness={searchBusiness}
                                isSearching={isSearching}
                            />
                        )}

                        {currentStep === 2 && (
                            <AutoFillStep
                                formData={formData as unknown as AutoFillFormData}
                                onFormChange={(name: string, value: string) => handleChange({ target: { name, value } } as React.ChangeEvent<HTMLInputElement>)}
                                onImageChange={handleImageChange}
                                imagePreview={imagePreview}
                                businessImageUrl={businessImageUrl}
                                setBusinessImageUrl={setBusinessImageUrl}
                                setImagePreview={setImagePreview}
                                setImageFile={setImageFile}
                                industries={industries}
                                subIndustries={subIndustries}
                                states={states}
                                generateDescription={generateDescription}
                                generatingDraft={generatingDraft}
                                descriptionStatus={descriptionStatus}
                                onNextStep={() => setCurrentStep(3)}
                            />
                        )}

                        {currentStep === 3 && (
                            <ValuationStep
                                formData={{
                                    title: formData.title,
                                    website: formData.website,
                                    annual_revenue_ttm: formData.annual_revenue_ttm || { min: null, max: null },
                                    annual_net_profit_ttm: formData.annual_net_profit_ttm || { min: null, max: null },
                                    last_month_revenue: formData.last_month_revenue || { min: null, max: null },
                                    last_month_profit: formData.last_month_profit || { min: null, max: null },
                                    recurring_revenue: formData.recurring_revenue || { min: null, max: null },
                                    price: formData.price,
                                    active_customers: formData.active_customers,
                                    growth_rate: formData.growth_rate,
                                    reason_for_selling: formData.reason_for_selling,
                                    description: formData.description,
                                    postalCode: formData.postal_code,
                                    status: formData.status
                                }}
                                industryMetrics={industryMetrics}
                                selectedIndustryName={selectedIndustryName}
                                selectedSubIndustryName={selectedSubIndustryName}
                                valuationCalculated={valuationCalculated}
                                formatCurrency={formatCurrency}
                                formatNumber={formatNumber}
                                handleRevenueChange={handleRevenueChange}
                                handleProfitChange={handleProfitChange}
                                handleLastMonthRevenueChange={handleLastMonthRevenueChange}
                                handleLastMonthProfitChange={handleLastMonthProfitChange}
                                handleRecurringRevenueChange={handleRecurringRevenueChange}
                                handlePriceChange={handlePriceChange}
                                handleStatusChange={(status: 'live' | 'draft') => setFormData(prev => ({ ...prev, status }))}
                                handleSubmit={handleSubmit}
                                loading={loading}
                                aiImageUrl={selectedAiImageUrl}
                                aiImageUrls={aiGeneratedImageUrls}
                                onSelectAiImage={handleSelectAiImage}
                                isGeneratingImage={isGeneratingImage}
                                onGenerateImage={handleGenerateImage}
                            />
                        )}
                    </>
                )}
            </div>
        </div>
    );
} 