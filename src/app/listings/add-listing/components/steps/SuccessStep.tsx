'use client';

import { useState, useEffect } from 'react';
import { CheckCircle } from 'lucide-react';
import Link from 'next/link';
import Confetti from 'react-confetti';

interface SuccessStepProps {
    onNavigateAway?: () => void;
}

export default function SuccessStep({ onNavigateAway }: SuccessStepProps) {
    const [windowDimensions, setWindowDimensions] = useState({
        width: typeof window !== 'undefined' ? window.innerWidth : 0,
        height: typeof window !== 'undefined' ? window.innerHeight : 0
    });
    const [showConfetti, setShowConfetti] = useState(true);

    useEffect(() => {
        // Handle window resize
        const handleResize = () => {
            setWindowDimensions({
                width: window.innerWidth,
                height: window.innerHeight
            });
        };

        // Add event listener
        window.addEventListener('resize', handleResize);

        // Set a timeout to stop the confetti after a few seconds
        const timer = setTimeout(() => {
            setShow<PERSON>onfetti(false);
        }, 5000);

        // Cleanup
        return () => {
            window.removeEventListener('resize', handleResize);
            clearTimeout(timer);
        };
    }, []);

    // Handle navigation away
    const handleNavigation = () => {
        if (onNavigateAway) {
            onNavigateAway();
        }
    };

    return (
        <div className="relative flex flex-col items-center justify-center py-12 px-4 text-center min-h-[500px]">
            {/* Confetti component */}
            {showConfetti && (
                <Confetti
                    width={windowDimensions.width}
                    height={windowDimensions.height}
                    recycle={false}
                    numberOfPieces={200}
                    gravity={0.2}
                    colors={['#26ccff', '#a25afd', '#ff5e7e', '#88ff5a', '#fcff42', '#ffa62d', '#ff36ff']}
                />
            )}

            <div className="relative z-10 flex flex-col items-center justify-center w-full">
                {/* Centered checkmark icon with gradient background - using inline styles to ensure they apply */}
                <div className="flex justify-center items-center mb-8">
                    <div
                        style={{
                            background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
                            padding: '1.5rem',
                            borderRadius: '9999px',
                            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
                        }}
                    >
                        <CheckCircle size={100} strokeWidth={1.5} style={{ color: 'white' }} />
                    </div>
                </div>

                <h2 className="text-2xl font-black mb-4">Your Business Has Been Listed!</h2>

                <p className="text-gray-600 mb-8 max-w-md mx-auto font-semibold">
                    Congratulations! Your business listing is now live. To attract serious buyers,
                    we recommend enhancing your listing with additional details and financial data.
                </p>

                <div className="bg-white p-6 rounded-lg border border-gray-100 mb-8 max-w-md w-full mx-auto">
                    <h3 className="font-extrabold text-lg mb-3">Next Steps:</h3>
                    <ul className="text-left space-y-3">
                        <li className="flex items-start">
                            <span className="text-blue-500 mr-2 font-bold">•</span>
                            <span>Visit <Link href="/account/my-listings" className="text-blue-600 hover:underline font-extrabold">My Listings</Link> to add more information about your business</span>
                        </li>
                        <li className="flex items-start">
                            <span className="text-blue-500 mr-2 font-bold">•</span>
                            <span>Upload financial documents to the Data Room to provide serious buyers with detailed insights</span>
                        </li>
                        <li className="flex items-start">
                            <span className="text-blue-500 mr-2 font-bold">•</span>
                            <span>Keep an eye on your inbox for inquiries from potential buyers</span>
                        </li>
                    </ul>
                </div>

                <div className="flex space-x-4">
                    <Link
                        href="/account/my-listings"
                        style={{
                            background: 'linear-gradient(to right, #2563eb, #7c3aed)',
                            padding: '0.75rem 1.5rem',
                            borderRadius: '0.375rem',
                            color: 'white',
                            fontWeight: 'bold',
                            transition: 'all 0.2s'
                        }}
                        onClick={handleNavigation}
                    >
                        Go to My Listings
                    </Link>
                    <Link
                        href="/listings"
                        className="px-6 py-3 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors font-semibold"
                        onClick={handleNavigation}
                    >
                        Browse Listings
                    </Link>
                </div>
            </div>
        </div>
    );
} 