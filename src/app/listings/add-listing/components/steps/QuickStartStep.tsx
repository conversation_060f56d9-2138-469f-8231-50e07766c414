'use client';

import { Loader2, <PERSON><PERSON><PERSON>, Shield } from 'lucide-react';

interface QuickStartStepProps {
    formData: {
        title: string;
        postal_code: string;
    };
    onFormChange: (name: string, value: string) => void;
    onSearchBusiness: () => Promise<void>;
    isSearching: boolean;
}

export default function QuickStartStep({
    formData,
    onFormChange,
    onSearchBusiness,
    isSearching
}: QuickStartStepProps) {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        onFormChange(name, value);
    };

    return (
        <div className="space-y-8 w-full bg-gray-50 p-6 rounded-lg">
            <p className="text-gray-600">
                Let&apos;s start by finding your business. Enter your business name and zip code, and we&apos;ll help you fill in the details.
            </p>

            <div className="bg-blue-50 border border-blue-100 rounded-lg p-4">
                <div className="flex items-start gap-3">
                    <Shield className="w-5 h-5 text-blue-600 flex-shrink-0 mt-1" />
                    <div className="space-y-1">
                        <h3 className="font-semibold text-blue-900">Your Privacy Matters</h3>
                        <p className="text-sm text-blue-700">
                            We protect your business identity by anonymizing your listing details. Your actual business information will only be visible to verified buyers who have signed confidentiality agreements. This ensures your business operations remain unaffected while exploring sale opportunities.
                        </p>
                    </div>
                </div>
            </div>

            <div className="w-full bg-white p-6 rounded-lg shadow-md border border-gray-200">
                <div className="space-y-4">
                    <div>
                        <label htmlFor="title" className="block text-sm font-medium mb-2">
                            Business Name
                        </label>
                        <input
                            type="text"
                            id="title"
                            placeholder="Enter your business name (or part of it)"
                            name="title"
                            value={formData.title}
                            onChange={handleChange}
                            required
                            className="w-full p-3 border rounded-md"
                        />
                    </div>

                    <div>
                        <label htmlFor="postal_code" className="block text-sm font-medium mb-2">
                            Zip Code
                        </label>
                        <input
                            type="text"
                            id="postal_code"
                            name="postal_code"
                            value={formData.postal_code}
                            onChange={handleChange}
                            className="w-full p-3 border rounded-md"
                            placeholder="Enter zip code"
                            required
                        />
                    </div>
                </div>
            </div>

            <div className="flex justify-center w-full">
                <button
                    type="button"
                    onClick={onSearchBusiness}
                    disabled={isSearching || !formData.title || !formData.postal_code}
                    className="inline-flex w-full items-center justify-between gap-2 px-6 py-3 bg-neutral-800 text-white rounded-md hover:bg-neutral-700 disabled:opacity-50 transition-colors"
                >
                    {isSearching ? (
                        <>
                            <Loader2 className="w-5 h-5 animate-spin" />
                            Searching...
                        </>
                    ) : (
                        <>
                            Find My Business
                            <ArrowRight className="w-5 h-5" />
                        </>
                    )}
                </button>
            </div>
        </div>
    );
} 