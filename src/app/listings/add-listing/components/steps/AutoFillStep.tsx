'use client';

import Image from 'next/image';
import {
    ImageIcon, Store, MapPin, Globe, Building2, FileText,
    Loader2, Sparkle, ArrowRight, Calendar, Trash2, RefreshCw
} from 'lucide-react';
import React from 'react';

interface AutoFillStepProps {
    formData: {
        title: string;
        description: string;
        website: string;
        industry_id: string;
        sub_industry_id: string;
        state_id: string;
        city: string;
        street_address: string;
        postal_code: string;
        year_established: string;
        team_size: string;
        reason_for_selling: string | null;
        [key: string]: string | null;
    };
    onFormChange: (name: string, value: string) => void;
    onImageChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    imagePreview: string | null;
    businessImageUrl: string | null;
    setBusinessImageUrl: (url: string | null) => void;
    setImagePreview: (url: string | null) => void;
    setImageFile: (file: File | null) => void;
    industries: Array<{ id: string, name: string }>;
    subIndustries: Array<{ id: string, name: string, industry_id: string }>;
    states: Array<{ id: string, name: string, code: string }>;
    generateDescription: () => Promise<void>;
    generatingDraft: boolean;
    descriptionStatus: {
        type: 'error' | 'info' | null;
        message: string | null;
    };
    onNextStep: () => void;
}

export default function AutoFillStep({
    formData,
    onFormChange,
    onImageChange,
    imagePreview,
    businessImageUrl,
    setBusinessImageUrl,
    setImagePreview,
    setImageFile,
    industries,
    subIndustries,
    states,
    generateDescription,
    generatingDraft,
    descriptionStatus,
    onNextStep
}: AutoFillStepProps) {
    const handleChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
    ) => {
        const { name, value } = e.target;
        onFormChange(name, value);
    };

    const [hasAutoFetched, setHasAutoFetched] = React.useState(false);
    const [hasExtractedDetails, setHasExtractedDetails] = React.useState(false);

    // Function to extract year established and team size from description
    const extractBusinessDetails = React.useCallback(() => {
        if (!formData.description || hasExtractedDetails) return;

        // Extract year established using regex patterns
        const yearPatterns = [
            /founded in (\d{4})/i,
            /established in (\d{4})/i,
            /since (\d{4})/i,
            /started in (\d{4})/i,
            /began in (\d{4})/i,
            /founded (\d{4})/i,
            /established (\d{4})/i,
            /in (\d{4}),/i
        ];

        // Extract team size using regex patterns
        const teamSizePatterns = [
            /(\d+)\s+employees/i,
            /team of (\d+)/i,
            /staff of (\d+)/i,
            /(\d+)\s+team members/i,
            /(\d+)\s+people team/i,
            /(\d+)\s+staff members/i,
            /employs (\d+)/i,
            /employing (\d+)/i
        ];

        // Try to find year established
        if (!formData.year_established) {
            for (const pattern of yearPatterns) {
                const match = formData.description.match(pattern);
                if (match && match[1]) {
                    const year = parseInt(match[1]);
                    const currentYear = new Date().getFullYear();

                    // Validate the year is reasonable (not in the future and not too old)
                    if (year <= currentYear && year >= 1900) {
                        onFormChange('year_established', year.toString());
                        break;
                    }
                }
            }
        }

        // Try to find team size
        if (!formData.team_size) {
            for (const pattern of teamSizePatterns) {
                const match = formData.description.match(pattern);
                if (match && match[1]) {
                    const teamSize = parseInt(match[1]);

                    // Validate team size is reasonable
                    if (teamSize > 0 && teamSize < 1000000) {
                        onFormChange('team_size', teamSize.toString());
                        break;
                    }
                }
            }
        }

        setHasExtractedDetails(true);
    }, [formData.description, formData.year_established, formData.team_size, hasExtractedDetails, onFormChange]);

    // Auto-fetch description when website is available
    React.useEffect(() => {
        if (
            formData.website &&
            !hasAutoFetched &&
            !formData.description &&
            !generatingDraft
        ) {
            setHasAutoFetched(true);
            generateDescription();
        }
    }, [formData.website, formData.description, hasAutoFetched, generatingDraft, generateDescription]);

    // Extract business details when description is available
    React.useEffect(() => {
        if (formData.description && !hasExtractedDetails) {
            extractBusinessDetails();
        }
    }, [formData.description, hasExtractedDetails, extractBusinessDetails]);

    // Reset extraction flag if description changes
    React.useEffect(() => {
        if (!formData.description) {
            setHasExtractedDetails(false);
        }
    }, [formData.description]);

    return (
        <div className="space-y-8 w-full">
            {/* Notification-style header card */}
            <div className="w-full bg-blue-50 p-6 rounded-lg shadow-sm border border-blue-100">
                <div className="flex items-start">
                    <div className="flex-shrink-0 mr-3">
                        <Sparkle className="h-6 w-6 text-blue-500" />
                    </div>
                    <div>
                        <h2 className="text-xl font-semibold text-blue-800">Auto-Fill & Confirmation</h2>
                        <p className="text-blue-700 mt-2">
                            We&apos;ve found your business information. Please carefully review and make any necessary edits before proceeding.
                        </p>
                    </div>
                </div>
            </div>

            {/* SECTION 1: BUSINESS IDENTITY */}
            <div>
                <h3 className="text-lg font-semibold mb-4">Business Identity</h3>

                <div className="w-full bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    {/* Business Name */}
                    <div className="mb-8">
                        <h4 className="text-md font-medium mb-4 flex items-center">
                            <Store className="w-5 h-5 mr-2 text-neutral-600" />
                            Business Name
                        </h4>
                        <div>
                            <label htmlFor="title" className="block text-sm font-medium mb-2">
                                Business Name
                            </label>
                            <input
                                type="text"
                                id="title"
                                name="title"
                                value={formData.title}
                                onChange={handleChange}
                                required
                                className="w-full p-3 border rounded-md"
                            />
                        </div>
                    </div>

                    {/* Business Image */}
                    <div className="mb-8">
                        <h4 className="text-md font-medium mb-4 flex items-center">
                            <ImageIcon className="w-5 h-5 mr-2 text-neutral-600" />
                            Business Image
                        </h4>

                        <div className="flex flex-col items-center space-y-4">
                            {businessImageUrl && !imagePreview && (
                                <div className="w-full space-y-4">
                                    <Image
                                        src={businessImageUrl}
                                        alt="Business from Google"
                                        className="w-full h-48 object-cover rounded-lg"
                                        width={300}
                                        height={200}
                                    />
                                    <div className="flex justify-between items-center">
                                        <p className="text-sm text-gray-500">Image from Google Places</p>
                                        <button
                                            type="button"
                                            onClick={() => setBusinessImageUrl(null)}
                                            className="flex items-center px-2 py-1 text-sm text-white bg-red-500 hover:bg-red-600 rounded-md transition-colors"
                                        >
                                            <Trash2 className="w-3 h-3 mr-1" />
                                            Remove
                                        </button>
                                    </div>
                                </div>
                            )}

                            {imagePreview && (
                                <div className="w-full space-y-4">
                                    <Image
                                        src={imagePreview}
                                        alt="Preview"
                                        className="w-full h-48 object-cover rounded-lg"
                                        width={300}
                                        height={200}
                                    />
                                    <div className="flex justify-end">
                                        <button
                                            type="button"
                                            onClick={() => {
                                                setImagePreview(null);
                                                setImageFile(null);
                                            }}
                                            className="flex items-center px-2 py-1 text-sm text-white bg-red-500 hover:bg-red-600 rounded-md transition-colors"
                                        >
                                            <Trash2 className="w-3 h-3 mr-1" />
                                            Remove
                                        </button>
                                    </div>
                                </div>
                            )}

                            {!imagePreview && !businessImageUrl && (
                                <div className="w-full">
                                    <p className="mb-2 text-sm text-gray-600">
                                        Upload an image for your business:
                                    </p>
                                    <input
                                        type="file"
                                        id="image"
                                        accept="image/*"
                                        onChange={onImageChange}
                                        className="w-full"
                                    />
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Business Description */}
                    <div className="mb-8">
                        <h4 className="text-md font-medium mb-4 flex items-center">
                            <FileText className="w-5 h-5 mr-2 text-neutral-600" />
                            Business Description
                        </h4>
                        <div>
                            <div className="flex justify-between items-center mb-2">
                                <label htmlFor="description" className="block text-sm font-medium">
                                    Description
                                </label>
                                {formData.website && (
                                    <button
                                        type="button"
                                        onClick={generateDescription}
                                        disabled={generatingDraft}
                                        className="flex items-center px-2 py-1 text-xs text-blue-700 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors"
                                    >
                                        {generatingDraft ? (
                                            <>
                                                <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                                                Generating...
                                            </>
                                        ) : (
                                            <>
                                                <RefreshCw className="w-3 h-3 mr-1" />
                                                {hasAutoFetched ? 'Regenerate from website' : 'Generate from website'}
                                            </>
                                        )}
                                    </button>
                                )}
                            </div>
                            <textarea
                                id="description"
                                name="description"
                                value={formData.description}
                                onChange={handleChange}
                                rows={6}
                                className="w-full p-3 border rounded-md"
                                placeholder={generatingDraft ? "Generating description..." : "Describe your business..."}
                                required
                            />
                            {descriptionStatus.message && (
                                <p className={`text-sm mt-2 ${descriptionStatus.type === 'error' ? 'text-red-600' : 'text-blue-600'}`}>
                                    {descriptionStatus.message}
                                </p>
                            )}
                        </div>
                    </div>

                    {/* Business Category */}
                    <div>
                        <h4 className="text-md font-medium mb-4 flex items-center">
                            <Building2 className="w-5 h-5 mr-2 text-neutral-600" />
                            Business Category
                        </h4>
                        <div className="space-y-4">
                            <div>
                                <label htmlFor="industry_id" className="block text-sm font-medium mb-2">
                                    Industry
                                </label>
                                <select
                                    id="industry_id"
                                    name="industry_id"
                                    value={formData.industry_id}
                                    onChange={handleChange}
                                    required
                                    className="w-full p-3 border rounded-md"
                                >
                                    <option value="">Select an industry</option>
                                    {industries.map((industry) => (
                                        <option key={industry.id} value={industry.id}>
                                            {industry.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div>
                                <label htmlFor="sub_industry_id" className="block text-sm font-medium mb-2">
                                    Sub-Industry
                                </label>
                                <select
                                    id="sub_industry_id"
                                    name="sub_industry_id"
                                    value={formData.sub_industry_id}
                                    onChange={handleChange}
                                    required
                                    className="w-full p-3 border rounded-md"
                                    disabled={!formData.industry_id}
                                >
                                    <option value="">Select a sub-industry</option>
                                    {subIndustries.map((subIndustry) => (
                                        <option key={subIndustry.id} value={subIndustry.id}>
                                            {subIndustry.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* SECTION 2: BUSINESS DETAILS */}
            <div>
                <h3 className="text-lg font-semibold mb-4">Business Details</h3>

                <div className="w-full bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    {/* Business Location */}
                    <div className="mb-8">
                        <h4 className="text-md font-medium mb-4 flex items-center">
                            <MapPin className="w-5 h-5 mr-2 text-neutral-600" />
                            Business Location
                        </h4>
                        <div className="space-y-4">
                            <div>
                                <label htmlFor="street_address" className="block text-sm font-medium mb-2">
                                    Street Address
                                </label>
                                <input
                                    type="text"
                                    id="street_address"
                                    name="street_address"
                                    value={formData.street_address}
                                    onChange={handleChange}
                                    className="w-full p-3 border rounded-md"
                                />
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label htmlFor="city" className="block text-sm font-medium mb-2">
                                        City
                                    </label>
                                    <input
                                        type="text"
                                        id="city"
                                        name="city"
                                        value={formData.city}
                                        onChange={handleChange}
                                        className="w-full p-3 border rounded-md"
                                    />
                                </div>
                                <div>
                                    <label htmlFor="postal_code" className="block text-sm font-medium mb-2">
                                        Zip Code
                                    </label>
                                    <input
                                        type="text"
                                        id="postal_code"
                                        name="postal_code"
                                        value={formData.postal_code}
                                        onChange={handleChange}
                                        className="w-full p-3 border rounded-md"
                                    />
                                </div>
                            </div>
                            <div>
                                <label htmlFor="state_id" className="block text-sm font-medium mb-2">
                                    State
                                </label>
                                <select
                                    id="state_id"
                                    name="state_id"
                                    value={formData.state_id}
                                    onChange={handleChange}
                                    className="w-full p-3 border rounded-md"
                                >
                                    <option value="">Select a state</option>
                                    {states.map((state) => (
                                        <option key={state.id} value={state.id}>
                                            {state.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    </div>

                    {/* Business Website */}
                    <div className="mb-8">
                        <h4 className="text-md font-medium mb-4 flex items-center">
                            <Globe className="w-5 h-5 mr-2 text-neutral-600" />
                            Business Website
                        </h4>
                        <div>
                            <label htmlFor="website" className="block text-sm font-medium mb-2">
                                Website URL
                            </label>
                            <input
                                type="url"
                                id="website"
                                name="website"
                                value={formData.website}
                                onChange={handleChange}
                                className="w-full p-3 border rounded-md"
                                placeholder="https://example.com"
                            />
                        </div>
                    </div>

                    {/* Business Age & Size */}
                    <div>
                        <h4 className="text-md font-medium mb-4 flex items-center">
                            <Calendar className="w-5 h-5 mr-2 text-neutral-600" />
                            Business Age
                        </h4>
                        <div>
                            <label htmlFor="year_established" className="block text-sm font-medium mb-2">
                                Year Established
                            </label>
                            <div className="relative">
                                <input
                                    type="number"
                                    id="year_established"
                                    name="year_established"
                                    value={formData.year_established}
                                    onChange={handleChange}
                                    className="w-full p-3 border rounded-md"
                                    placeholder="e.g. 2010"
                                    min="1900"
                                    max={new Date().getFullYear()}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* SECTION 3: REASON FOR SELLING */}
            <div>
                <h3 className="text-lg font-semibold mb-4">Reason for Selling</h3>
                <div className="w-full bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    <div>
                        <label htmlFor="reason_for_selling" className="block text-sm font-medium mb-2">
                            Why are you selling your business?
                        </label>
                        <textarea
                            id="reason_for_selling"
                            name="reason_for_selling"
                            value={formData.reason_for_selling || ''}
                            onChange={handleChange}
                            rows={4}
                            className="w-full p-3 border rounded-md"
                            placeholder="Explain why you're selling your business..."
                        />
                        <p className="text-sm text-gray-500 mt-2">
                            Being transparent about your reasons for selling helps build trust with potential buyers.
                        </p>
                    </div>
                </div>
            </div>

            <div className="flex justify-center w-full mt-8">
                <button
                    type="button"
                    onClick={() => {
                        window.scrollTo(0, 0);
                        onNextStep();
                    }}
                    className="inline-flex w-full justify-between items-center gap-2 px-6 py-3 bg-neutral-800 text-white rounded-md hover:bg-neutral-700 transition-colors"
                >
                    Save and show my valuation
                    <ArrowRight className="w-5 h-5" />
                </button>
            </div>
        </div>
    );
}