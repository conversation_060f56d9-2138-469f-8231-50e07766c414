'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>ader2, ArrowRight, ChevronDown, ChevronUp, T<PERSON>dingUp, BarChart, DollarSign, Store } from 'lucide-react';
import { useState, useEffect, useCallback } from 'react';
import { MetricsSummary } from '@/utils/industryMetricsMapping';
import {
    IndustryMetricsDisplay,
    PriceRangeSlider,
    RevenueRangeSlider,
    ProfitRangeSlider,
    LastMonthRevenueSlider,
    LastMonthProfitSlider,
    RecurringRevenueSlider
} from '@/components';
import Image from 'next/image';
import { getEnigmaData } from '@/utils/services/enigma';
import { toast } from 'sonner';

interface ValuationStepProps {
    formData: {
        title: string;
        annual_revenue_ttm: {
            min: number | null;
            max: number | null;
        };
        annual_net_profit_ttm: {
            min: number | null;
            max: number | null;
        };
        last_month_revenue: {
            min: number | null;
            max: number | null;
        };
        last_month_profit: {
            min: number | null;
            max: number | null;
        };
        recurring_revenue: {
            min: number | null;
            max: number | null;
        };
        price: string;
        active_customers?: string;
        growth_rate?: string;
        reason_for_selling?: string;
        description: string;
        website: string;
        postalCode: string;
        status: 'live' | 'draft';
    };
    industryMetrics: MetricsSummary | null;
    selectedIndustryName: string;
    selectedSubIndustryName: string;
    valuationCalculated: boolean;
    formatCurrency: (value: string | number) => string;
    formatNumber: (value: string | number) => string;
    handleRevenueChange: (range: { min: number; max: number }) => void;
    handleProfitChange: (range: { min: number; max: number }) => void;
    handleLastMonthRevenueChange: (range: { min: number; max: number }) => void;
    handleLastMonthProfitChange: (range: { min: number; max: number }) => void;
    handleRecurringRevenueChange: (range: { min: number; max: number }) => void;
    handlePriceChange: (price: number) => void;
    handleStatusChange: (status: 'live' | 'draft') => void;
    handleSubmit: () => Promise<void>;
    loading: boolean;
    aiImageUrl: string | null;
    aiImageUrls?: string[];
    onSelectAiImage?: (imageUrl: string) => void;
    isGeneratingImage: boolean;
    onGenerateImage: () => Promise<void>;
}

export default function ValuationStep({
    formData,
    industryMetrics,
    selectedIndustryName,
    selectedSubIndustryName,
    valuationCalculated,
    formatCurrency,
    formatNumber,
    handleRevenueChange,
    handleProfitChange,
    handleLastMonthRevenueChange,
    handleLastMonthProfitChange,
    handleRecurringRevenueChange,
    handlePriceChange,
    handleStatusChange,
    handleSubmit,
    loading,
    aiImageUrl,
    aiImageUrls,
    onSelectAiImage,
    isGeneratingImage,
    onGenerateImage
}: ValuationStepProps) {
    const [activeAccordion, setActiveAccordion] = useState<string | null>(null);
    const [enigmaData, setEnigmaData] = useState<{
        monthly_revenue?: number;
        annual_growth_rate?: number;
        card_revenue?: Array<{
            "12m"?: {
                average_monthly_amount?: number;
            };
        }>;
        card_transactions?: Array<{
            "12m"?: {
                average_monthly_count?: number;
            };
        }>;
        growth_rate?: Array<{
            "12m"?: {
                rate?: number;
            };
        }>;
    }>({});
    const [isLoadingEnigma, setIsLoadingEnigma] = useState(false);
    const [annualRevenue, setAnnualRevenue] = useState<number | undefined>(undefined);
    const [priceBasedOnVerifiedData, setPriceBasedOnVerifiedData] = useState(false);
    const [lastSuggestedPrice, setLastSuggestedPrice] = useState<number | null>(null);
    const [localPrice, setLocalPrice] = useState<number | null>(null);
    const [hasVerified, setHasVerified] = useState(false);

    // Initialize localPrice with formData.price when component mounts
    useEffect(() => {
        if (formData.price) {
            const numericPrice = parseFloat(formData.price);
            if (!isNaN(numericPrice)) {
                setLocalPrice(numericPrice);
            }
        }
    }, [formData.price]);

    // Update localPrice when formData.price changes
    useEffect(() => {
        if (formData.price) {
            const numericPrice = parseFloat(formData.price);
            if (!isNaN(numericPrice) && numericPrice !== localPrice) {
                console.log('Updating localPrice from formData:', numericPrice);
                setLocalPrice(numericPrice);
            }
        }
    }, [formData.price, localPrice]);

    // Log when formData.price changes
    useEffect(() => {
        console.log('formData.price changed:', formData.price);
    }, [formData.price]);

    // Automatically generate AI image on mount if not already generated
    useEffect(() => {
        if ((!aiImageUrls || aiImageUrls.length === 0) && !isGeneratingImage && formData.description) {
            onGenerateImage();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formData.description]); // Only re-run if description changes, to ensure we have it

    // Update local annualRevenue based on slider input if not using verified data
    useEffect(() => {
        if (!priceBasedOnVerifiedData && formData.annual_revenue_ttm?.min !== undefined && formData.annual_revenue_ttm?.min !== null) {
            console.log('Setting annual revenue from slider data:', formData.annual_revenue_ttm.min);
            setAnnualRevenue(formData.annual_revenue_ttm.min);
        }
        // Optional: Reset if slider goes back to null/undefined and not verified
        // else if (!priceBasedOnVerifiedData && (formData.annual_revenue_ttm?.min === undefined || formData.annual_revenue_ttm?.min === null)) {
        //     setAnnualRevenue(undefined);
        // }
    }, [formData.annual_revenue_ttm, priceBasedOnVerifiedData]);

    const toggleAccordion = (section: string) => {
        setActiveAccordion(activeAccordion === section ? null : section);
    };

    // Update the verifyBusiness function
    const verifyBusiness = async () => {
        if (!formData?.title && !formData?.website) {
            toast.error('Please enter business name or website to verify');
            return;
        }

        setIsLoadingEnigma(true);

        try {
            const data = await getEnigmaData({
                name: formData.title,
                website: formData.website,
                postalCode: formData.postalCode
            });

            if (data.error) {
                throw new Error(data.error);
            }

            setEnigmaData(data);
            setHasVerified(true);

            // Extract values and update annual revenue
            const { annualRevenue: calculatedAnnualRevenue } = extractEnigmaValues(data);
            if (calculatedAnnualRevenue && calculatedAnnualRevenue > 0) {
                console.log('Setting annual revenue from verified data:', calculatedAnnualRevenue);

                // THIS IS CRITICAL - set both of these values
                setAnnualRevenue(calculatedAnnualRevenue);
                setPriceBasedOnVerifiedData(true);

                // Log to verify these values are being set
                console.log('Updated verification state:', {
                    annualRevenue: calculatedAnnualRevenue,
                    priceBasedOnVerifiedData: true
                });
            }

            toast.success('Business data verified successfully!');
        } catch (error) {
            console.error('Error verifying business:', error);
            toast.error('Unable to verify business data. Please check your information and try again.');
        } finally {
            setIsLoadingEnigma(false);
        }
    };

    // Add this function to extract values from either format - wrapped in useCallback
    const extractEnigmaValues = useCallback((data: typeof enigmaData) => {
        // Try the new simplified format first
        if (data.monthly_revenue !== undefined) {
            return {
                monthlyRevenue: data.monthly_revenue,
                annualRevenue: data.monthly_revenue * 12,
                growthRate: data.annual_growth_rate
            };
        }

        // Fall back to the original format
        const monthlyRevenue = data.card_revenue?.[0]?.["12m"]?.average_monthly_amount;
        const monthlyCount = data.card_transactions?.[0]?.["12m"]?.average_monthly_count;
        const growthRate = data.growth_rate?.[0]?.["12m"]?.rate;

        return {
            monthlyRevenue,
            annualRevenue: monthlyRevenue ? monthlyRevenue * 12 : undefined,
            transactionCount: monthlyCount,
            growthRate
        };
    }, []);

    // In the useEffect that processes enigma data
    useEffect(() => {
        const { annualRevenue: calculatedAnnualRevenue } = extractEnigmaValues(enigmaData);

        // Reset the price based on verified data flag when Enigma data changes
        setPriceBasedOnVerifiedData(false);

        if (calculatedAnnualRevenue !== annualRevenue) {
            setAnnualRevenue(calculatedAnnualRevenue);
        }
    }, [enigmaData, annualRevenue, extractEnigmaValues]);

    // Update the price when annual revenue is available
    useEffect(() => {
        if (annualRevenue && !isNaN(annualRevenue) && industryMetrics?.medianRevenueMultiple) {
            // Calculate suggested price based on annual revenue and revenue multiple
            const suggestedPrice = Math.round(annualRevenue * industryMetrics.medianRevenueMultiple);

            // Only update if the price has changed significantly (add a small tolerance)
            if (Math.abs((lastSuggestedPrice || 0) - suggestedPrice) > 1) {
                console.log('Updating price based on revenue data:', {
                    annualRevenue,
                    revenueMultiple: industryMetrics.medianRevenueMultiple,
                    suggestedPrice,
                    currentPrice: formData.price,
                    lastSuggestedPrice
                });

                // Ensure the price is a valid number
                if (!isNaN(suggestedPrice) && suggestedPrice > 0) {
                    // Force the price update
                    handlePriceChange(suggestedPrice);
                    setLocalPrice(suggestedPrice);
                    setLastSuggestedPrice(suggestedPrice);

                    // Show a toast notification to inform the user
                    toast.success(
                        `Suggested asking price updated to ${formatCurrency(suggestedPrice)} based on annual revenue of ${formatCurrency(annualRevenue)} and industry revenue multiple of ${industryMetrics.medianRevenueMultiple.toFixed(2)}x.`
                    );
                } else {
                    console.error('Invalid suggested price calculated:', suggestedPrice);
                }
            }
        }
    }, [annualRevenue, industryMetrics?.medianRevenueMultiple, handlePriceChange, formatCurrency, lastSuggestedPrice, formData.price]);

    // Inside the ValuationStep component, let's add a new useEffect that logs the Enigma data whenever it changes
    useEffect(() => {
        if (enigmaData) {
            console.log('===== ENIGMA DATA DETAILED LOGGING =====');
            console.log('Full Enigma response:', enigmaData);

            // Log card_revenue data structure
            console.log('Card Revenue Data:', JSON.stringify(enigmaData.card_revenue, null, 2));

            // Log card_transactions data structure
            console.log('Card Transactions Data:', JSON.stringify(enigmaData.card_transactions, null, 2));

            // Log growth_rate data structure
            console.log('Growth Rate Data:', JSON.stringify(enigmaData.growth_rate, null, 2));

            // Extract and log specific values we're using
            const monthlyAmount = enigmaData.card_revenue?.[0]?.["12m"]?.average_monthly_amount;
            const calculatedAnnualRevenue = monthlyAmount ? monthlyAmount * 12 : undefined;
            const monthlyCount = enigmaData.card_transactions?.[0]?.["12m"]?.average_monthly_count;
            const growthRate = enigmaData.growth_rate?.[0]?.["12m"]?.rate;

            console.log('Extracted Values:');
            console.log('- Monthly Revenue Amount:', monthlyAmount);
            console.log('- Calculated Annual Revenue:', calculatedAnnualRevenue);
            console.log('- Monthly Transaction Count:', monthlyCount);
            console.log('- Growth Rate:', growthRate);
            console.log('====================================');
        }
    }, [enigmaData, extractEnigmaValues]);

    // Update EnigmaDataSection UI
    const EnigmaDataSection = () => {
        const { growthRate } = extractEnigmaValues(enigmaData);

        return (
            <div className="w-full bg-white rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <BarChart className="w-5 h-5 mr-2 text-blue-500" />
                    Verified Business Data
                </h3>

                {!hasVerified ? (
                    <div className="flex flex-col items-center space-y-4">
                        <p className="text-sm text-gray-600">
                            Verify your business data to see transaction growth.
                        </p>
                        <button
                            onClick={verifyBusiness}
                            disabled={isLoadingEnigma || (!formData?.title && !formData?.website)}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                        >
                            {isLoadingEnigma ? (
                                <span className="flex items-center">
                                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                                    Verifying...
                                </span>
                            ) : (
                                "Verify Business Data"
                            )}
                        </button>
                    </div>
                ) : isLoadingEnigma ? (
                    <div className="flex items-center space-x-2">
                        <Loader2 className="w-4 h-4 animate-spin" />
                        <p className="text-gray-500">Fetching verified data...</p>
                    </div>
                ) : (
                    <div className="flex justify-between items-center">
                        <span className="text-gray-600 flex items-center">
                            <TrendingUp className="w-4 h-4 mr-2 text-blue-500" />
                            Transaction growth:
                        </span>
                        <span className={`font-medium ${growthRate && growthRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {growthRate && !isNaN(growthRate)
                                ? `${(growthRate * 100).toFixed(1)}%`
                                : 'Not available'}
                        </span>
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="space-y-8 w-full">
            <h2 className="text-xl font-semibold">Instant Valuation</h2>
            <p className="text-gray-600">
                Based on your business type, we&apos;ve calculated an estimated valuation range.
                Adjust the financial details below to refine your valuation. You can always update these later.
            </p>

            {/* Always show Enigma data section */}
            <EnigmaDataSection />

            {/* Price Range Slider */}
            <PriceRangeSlider
                metrics={industryMetrics || {
                    reported_sales: 0,
                    medianPrice: 0,
                    medianAskingPrice: 0,
                    averageSalesRatio: 0,
                    medianRevenueMultiple: 0,
                    medianCashflowMultiple: 0,
                    medianDaysOnMarket: 0
                }}
                onPriceChange={handlePriceChange}
                verifiedDataUsed={priceBasedOnVerifiedData}
                annualRevenue={annualRevenue}
            />

            {/* --- Asking Price Input Card --- */}
            <div className="w-full bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <label htmlFor="askingPriceInput" className="block text-lg font-semibold mb-3 flex items-center">
                    <DollarSign size={20} className="mr-2 text-gray-600" />
                    Asking Price
                </label>
                <div className="relative flex-grow">
                    <span className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500">
                        $
                    </span>
                    <input
                        type="text" // Use text for flexible input, handle parsing
                        id="askingPriceInput"
                        name="price"
                        className="w-full border border-gray-300 rounded-md pl-7 pr-4 py-2 text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter asking price"
                        // Format the value for display using formatNumber (no symbol)
                        value={formData.price && !isNaN(parseFloat(formData.price)) ? formatNumber(formData.price) : ''}
                        onChange={(e) => {
                            const rawValue = e.target.value;
                            // Remove non-numeric characters (allow digits only)
                            const numericValue = rawValue.replace(/[^0-9]/g, '');
                            const parsedValue = parseInt(numericValue, 10);

                            // Call handlePriceChange with the parsed number or 0 if NaN
                            handlePriceChange(!isNaN(parsedValue) ? parsedValue : 0);
                        }}
                    />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                    Enter your desired asking price. You can always update this later.
                </p>
            </div>
            {/* --- End Asking Price Input Card --- */}

            {/* Grouped Accordions */}
            <div className="w-full bg-white rounded-lg shadow-sm border border-gray-100 divide-y divide-gray-100">
                {/* Valuation Factors Section */}
                {valuationCalculated && (
                    <div>
                        <button
                            onClick={() => toggleAccordion('valuation')}
                            className="flex w-full items-center justify-between text-lg font-semibold p-6"
                        >
                            <span>Valuation Factors</span>
                            {activeAccordion === 'valuation' ? (
                                <ChevronUp className="w-5 h-5" />
                            ) : (
                                <ChevronDown className="w-5 h-5" />
                            )}
                        </button>

                        {activeAccordion === 'valuation' && (
                            <div className="px-6 pb-6">
                                <ul className="text-sm space-y-2">
                                    <li className="flex justify-between">
                                        <span>Revenue Multiple:</span>
                                        <span className="font-medium">{industryMetrics?.medianRevenueMultiple || 'N/A'}</span>
                                    </li>
                                    <li className="flex justify-between">
                                        <span>Profit Multiple:</span>
                                        <span className="font-medium">{industryMetrics?.medianCashflowMultiple || 'N/A'}</span>
                                    </li>
                                    <li className="flex justify-between">
                                        <span>Typical Valuation Range:</span>
                                        <span className="font-medium">
                                            {industryMetrics?.medianPrice ?
                                                `${formatCurrency(industryMetrics.medianPrice * 0.8)} - ${formatCurrency(industryMetrics.medianPrice * 1.2)}`
                                                : 'N/A'}
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        )}
                    </div>
                )}

                {/* Industry Metrics Section */}
                {industryMetrics && (
                    <div>
                        <button
                            onClick={() => toggleAccordion('metrics')}
                            className="flex w-full items-center justify-between text-lg font-semibold p-6"
                        >
                            <span>Industry Metrics</span>
                            {activeAccordion === 'metrics' ? (
                                <ChevronUp className="w-5 h-5" />
                            ) : (
                                <ChevronDown className="w-5 h-5" />
                            )}
                        </button>

                        {activeAccordion === 'metrics' && (
                            <div className="px-6 pb-6">
                                <h3 className="text-lg font-medium mb-4 flex items-center">
                                    <Sparkles className="w-5 h-5 mr-2 text-neutral-600" />
                                    Industry Metrics for {selectedIndustryName} {selectedSubIndustryName ? `- ${selectedSubIndustryName}` : ''}
                                </h3>
                                <IndustryMetricsDisplay metrics={industryMetrics} />
                            </div>
                        )}
                    </div>
                )}

                {/* Financial Information Section */}
                <div>
                    <button
                        onClick={() => toggleAccordion('financials')}
                        className="flex w-full items-center justify-between text-lg font-semibold p-6"
                    >
                        <span>Financial Information</span>
                        {activeAccordion === 'financials' ? (
                            <ChevronUp className="w-5 h-5" />
                        ) : (
                            <ChevronDown className="w-5 h-5" />
                        )}
                    </button>

                    {activeAccordion === 'financials' && (
                        <div className="px-6 pb-6">
                            <div className="space-y-8">
                                {/* Annual Revenue TTM Slider */}
                                <div>
                                    <label className="block text-sm font-medium mb-2">
                                        Annual Revenue (TTM)
                                    </label>
                                    <RevenueRangeSlider
                                        initialValue={{
                                            min: formData.annual_revenue_ttm?.min || 0,
                                            max: formData.annual_revenue_ttm?.max || 0
                                        }}
                                        onRevenueChange={(range) => handleRevenueChange(range)}
                                    />
                                </div>

                                {/* Annual Net Profit TTM Slider */}
                                <div>
                                    <label className="block text-sm font-medium mb-2">
                                        Annual Net Profit (TTM)
                                    </label>
                                    <ProfitRangeSlider
                                        initialValue={{
                                            min: formData.annual_net_profit_ttm?.min || 0,
                                            max: formData.annual_net_profit_ttm?.max || 0
                                        }}
                                        onProfitChange={(range) => handleProfitChange(range)}
                                    />
                                </div>

                                {/* Last Month Revenue Slider */}
                                <div>
                                    <label className="block text-sm font-medium mb-2">
                                        Last Month Revenue
                                    </label>
                                    <LastMonthRevenueSlider
                                        initialValue={{
                                            min: formData.last_month_revenue?.min || 0,
                                            max: formData.last_month_revenue?.max || 0
                                        }}
                                        onRevenueChange={(range) => handleLastMonthRevenueChange(range)}
                                    />
                                </div>

                                {/* Last Month Profit Slider */}
                                <div>
                                    <label className="block text-sm font-medium mb-2">
                                        Last Month Profit
                                    </label>
                                    <LastMonthProfitSlider
                                        initialValue={{
                                            min: formData.last_month_profit?.min || 0,
                                            max: formData.last_month_profit?.max || 0
                                        }}
                                        onProfitChange={(range) => handleLastMonthProfitChange(range)}
                                    />
                                </div>

                                {/* Recurring Revenue Slider */}
                                <div>
                                    <label className="block text-sm font-medium mb-2">
                                        Recurring Revenue
                                    </label>
                                    <RecurringRevenueSlider
                                        initialValue={{
                                            min: formData.recurring_revenue?.min || 0,
                                            max: formData.recurring_revenue?.max || 0
                                        }}
                                        onRevenueChange={(range) => handleRecurringRevenueChange(range)}
                                    />
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Add this new section before the submit button */}
            <div className="w-full bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold mb-2">Anonymized Listing Image</h3>
                <p className="text-sm text-gray-600 mb-4">
                    To protect your privacy during the initial listing phase, we can generate anonymous images. Your actual business image will only be shown to potential buyers after you have approved them.
                </p>

                <div className="space-y-4">
                    {isGeneratingImage && (
                        <div className="flex justify-center items-center py-10">
                            <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
                            <p className="ml-2 text-gray-600">Generating images...</p>
                        </div>
                    )}

                    {/* Display selected image in larger size */}
                    {aiImageUrl && (
                        <div className="relative w-full h-64 rounded-lg overflow-hidden mb-4">
                            <Image
                                src={aiImageUrl}
                                alt="Selected AI Generated Preview"
                                fill
                                className="object-cover"
                                sizes="(max-width: 768px) 100vw, 50vw"
                            />
                            <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                                Selected
                            </div>
                        </div>
                    )}

                    {/* Display all images in a grid for selection */}
                    {aiImageUrls && aiImageUrls.length > 0 && (
                        <div>
                            <h4 className="text-sm font-medium mb-2">Select an image:</h4>
                            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-3">
                                {aiImageUrls.map((imageUrl, index) => (
                                    <div
                                        key={index}
                                        className={`relative cursor-pointer rounded-md overflow-hidden h-24 border-2 hover:opacity-90 transition-all ${imageUrl === aiImageUrl ? 'border-blue-500 ring-2 ring-blue-300' : 'border-transparent'
                                            }`}
                                        onClick={() => onSelectAiImage && onSelectAiImage(imageUrl)}
                                    >
                                        <Image
                                            src={imageUrl}
                                            alt={`AI Generated Preview ${index + 1}`}
                                            fill
                                            className="object-cover"
                                            sizes="100px"
                                        />
                                        {imageUrl === aiImageUrl && (
                                            <div className="absolute bottom-1 right-1 bg-blue-500 rounded-full w-4 h-4 flex items-center justify-center">
                                                <span className="text-white text-xs">✓</span>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Listing Status Section */}
            <div className="w-full bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold mb-2 flex items-center">
                    <Store className="w-5 h-5 mr-2 text-gray-600" />
                    Listing Status
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                    Choose whether your listing goes live immediately or stays in draft mode for further editing.
                </p>

                <div className="space-y-4">
                    <div className="flex items-center gap-4">
                        <button
                            type="button"
                            onClick={() => handleStatusChange('live')}
                            className={`flex-1 px-4 py-3 rounded-lg border transition-all duration-200 flex items-center justify-center gap-3 ${formData.status === 'live'
                                    ? 'bg-green-50 border-green-200 text-green-800'
                                    : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
                                }`}
                        >
                            <div className={`w-3 h-3 rounded-full ${formData.status === 'live' ? 'bg-green-500' : 'bg-gray-300'
                                }`} />
                            <span className="font-medium">Go Live</span>
                        </button>
                        <button
                            type="button"
                            onClick={() => handleStatusChange('draft')}
                            className={`flex-1 px-4 py-3 rounded-lg border transition-all duration-200 flex items-center justify-center gap-3 ${formData.status === 'draft'
                                    ? 'bg-orange-50 border-orange-200 text-orange-800'
                                    : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
                                }`}
                        >
                            <div className={`w-3 h-3 rounded-full ${formData.status === 'draft' ? 'bg-orange-500' : 'bg-gray-300'
                                }`} />
                            <span className="font-medium">Save as Draft</span>
                        </button>
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <p className="text-sm text-blue-800">
                            {formData.status === 'draft'
                                ? "📝 Do you need more time to get ready and don't want your listing live on the marketplace? No worries, put it in draft and put it live when you want to. Your listing will be saved but won't be visible to potential buyers until you make it live."
                                : "🌐 Your listing will be published immediately and visible to all potential buyers on the marketplace. It can receive inquiries and be found in search results right away."
                            }
                        </p>
                    </div>
                </div>
            </div>

            <div className="flex justify-center w-full">
                <button
                    type="button"
                    onClick={handleSubmit}
                    disabled={loading}
                    className="inline-flex w-full items-center justify-between gap-2 px-6 py-3 bg-neutral-800 text-white rounded-md hover:bg-neutral-700 disabled:opacity-50 transition-colors"
                >
                    {loading ? (
                        <>
                            <Loader2 className="w-5 h-5 animate-spin" />
                            Creating Listing...
                        </>
                    ) : (
                        <>
                            List my business
                            <ArrowRight className="w-5 h-5" />
                        </>
                    )}
                </button>
            </div>
        </div>
    );
} 