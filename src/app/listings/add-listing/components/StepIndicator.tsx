'use client';

import clsx from 'clsx';
import { useEffect, useState } from 'react';

interface Step {
    id: number;
    title: string;
    icon: React.ElementType;
}

interface StepIndicatorProps {
    currentStep: number;
    steps: Step[];
}

export default function StepIndicator({ currentStep, steps }: StepIndicatorProps) {
    // Add animation state
    const [isPulsing, setIsPulsing] = useState(true);

    // Reset animation when step changes
    useEffect(() => {
        setIsPulsing(true);
        const timer = setTimeout(() => setIsPulsing(false), 2000);
        return () => clearTimeout(timer);
    }, [currentStep]);

    return (
        <div className="mb-8">
            <div className="flex justify-between items-center relative">
                {/* Connector lines */}
                <div className="absolute top-6 left-0 right-0 h-0.5 bg-gray-200" />
                <div
                    className="absolute top-6 left-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-300"
                    style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
                />

                {/* Step circles */}
                {steps.map((step) => (
                    <div key={step.id} className="flex flex-col items-center relative z-10">
                        <div
                            className={clsx(
                                'w-12 h-12 rounded-full flex items-center justify-center relative z-10',
                                currentStep === step.id
                                    ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md'
                                    : currentStep > step.id
                                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
                                        : 'bg-gray-200 text-gray-400'
                            )}
                        >
                            {/* Pulse animation for active step */}
                            {currentStep === step.id && isPulsing && (
                                <span className="absolute w-12 h-12 rounded-full bg-gradient-to-r from-blue-500/50 to-purple-600/50 animate-ping" />
                            )}
                            <step.icon className="w-6 h-6" />
                        </div>

                        {/* Step title */}
                        <span
                            className={clsx(
                                'mt-2 text-sm text-center',
                                currentStep === step.id ? 'font-bold text-neutral-800' : 'text-gray-500'
                            )}
                        >
                            {step.title}
                        </span>
                    </div>
                ))}
            </div>
        </div>
    );
} 