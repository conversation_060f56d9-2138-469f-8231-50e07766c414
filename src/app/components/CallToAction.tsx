'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Users, ArrowRight, Sparkles } from 'lucide-react';

export const CallToAction = () => {
    return (
        <div className="bg-gray-50 py-16">
            <div className="container mx-auto px-6 sm:px-8">
                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 overflow-hidden">
                    {/* Header Section */}
                    <div className="p-8 pb-0">
                        <div className="flex items-center space-x-3 mb-6">
                            <div className="p-2 bg-purple-50 rounded-lg">
                                <Users className="w-5 h-5 text-purple-600" />
                            </div>
                            <div>
                                <h2 className="text-2xl font-semibold text-gray-900">Join Our Community</h2>
                                <p className="text-gray-600 text-sm mt-1">
                                    Connect with trusted buyers and sellers
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Main CTA Section */}
                    <div className="relative p-8 bg-gradient-to-br from-gray-50 to-gray-100 border-t border-gray-100">
                        {/* Background image with overlay */}
                        <div className="absolute inset-0 overflow-hidden rounded-b-xl">
                            <div className="absolute right-0 top-1/2 -translate-y-[12%] w-[48rem] h-[48rem]">
                                <Image
                                    src="/images/trophy-cta-hero.jpeg"
                                    alt=""
                                    fill
                                    className="object-contain opacity-10 mix-blend-multiply contrast-125 grayscale scale-150"
                                    priority={false}
                                />
                            </div>
                            <div className="absolute inset-0 bg-gradient-to-br from-gray-600/20 to-gray-800/20" />
                        </div>

                        {/* Content */}
                        <div className="relative">
                            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                                <div className="lg:max-w-xl">
                                    <h3 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                                        Ready to Buy or Sell?
                                        <span className="flex items-center mt-2 text-2xl bg-gradient-to-r from-gray-800 to-gray-900 bg-clip-text text-transparent">
                                            <Sparkles className="w-6 h-6 text-purple-600 mr-2" />
                                            Join Your Trusted Marketplace
                                        </span>
                                    </h3>
                                    <p className="mt-4 text-lg leading-relaxed text-gray-600">
                                        Connect with trusted sellers and find great deals in your area. Create your account today and start buying or selling with confidence.
                                    </p>
                                </div>
                                <div className="mt-8 lg:mt-0 lg:ml-8 flex flex-col items-center">
                                    <Link
                                        href="/signup"
                                        className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-gray-800 to-gray-900 text-white rounded-lg hover:from-gray-700 hover:to-gray-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-900 transition-all duration-200 font-semibold group shadow-lg"
                                    >
                                        Get Started Today
                                        <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                                    </Link>
                                    <span className="mt-3 text-sm text-gray-500 text-center font-medium">
                                        Free signups during beta period
                                    </span>
                                </div>
                            </div>

                            {/* Stats Section */}
                            <div className="mt-12 pt-8 border-t border-gray-200/50">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                                    <div className="text-center">
                                        <div className="text-2xl font-bold text-gray-900">1,000+</div>
                                        <div className="text-sm text-gray-600 mt-1">Active Listings</div>
                                    </div>
                                    <div className="text-center">
                                        <div className="text-2xl font-bold text-gray-900">500+</div>
                                        <div className="text-sm text-gray-600 mt-1">Trusted Members</div>
                                    </div>
                                    <div className="text-center">
                                        <div className="text-2xl font-bold text-gray-900">$10M+</div>
                                        <div className="text-sm text-gray-600 mt-1">Total Deal Value</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};