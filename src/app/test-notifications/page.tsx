'use client'

import { useState } from 'react'

export default function TestNotifications() {
    const [isCreating, setIsCreating] = useState(false)
    const [isTriggering, setIsTriggering] = useState(false)
    const [isTesting, setIsTesting] = useState(false)
    const [message, setMessage] = useState('')

    const createTestListing = async () => {
        setIsCreating(true)
        setMessage('')

        try {
            const response = await fetch('/api/test-listing', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            })

            const result = await response.json()

            if (response.ok) {
                setMessage(`✅ Success: ${result.message}`)
            } else {
                setMessage(`❌ Error: ${result.error}`)
            }
        } catch (error) {
            setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
        } finally {
            setIsCreating(false)
        }
    }

    const triggerNotifications = async () => {
        setIsTriggering(true)
        setMessage('')

        try {
            const response = await fetch('/api/trigger-match-notifications', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            })

            const result = await response.json()

            if (response.ok) {
                setMessage(`✅ Success: ${result.message}. Processed: ${result.data?.processed || 0}, Emails sent: ${result.data?.emailsSent || 0}`)
            } else {
                setMessage(`❌ Error: ${result.error}`)
            }
        } catch (error) {
            setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
        } finally {
            setIsTriggering(false)
        }
    }

    const testResendApi = async () => {
        setIsTesting(true)
        setMessage('')

        try {
            const response = await fetch('/api/test-resend', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            })

            const result = await response.json()

            if (response.ok) {
                setMessage(`✅ Resend API Test Success: ${JSON.stringify(result)}`)
            } else {
                setMessage(`❌ Resend API Test Error: ${result.error}`)
            }
        } catch (error) {
            setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
        } finally {
            setIsTesting(false)
        }
    }

    return (
        <div className="max-w-4xl mx-auto p-6">
            <h1 className="text-3xl font-bold mb-8">🧪 Email Notifications Testing</h1>

            <div className="space-y-6">
                {/* Instructions */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h2 className="text-lg font-semibold text-blue-900 mb-3">📋 Testing Instructions</h2>
                    <ol className="list-decimal list-inside space-y-2 text-blue-800">
                        <li>First, test the Resend API connection</li>
                        <li>Create a test listing to trigger match notifications</li>
                        <li>Trigger the notification processing</li>
                        <li>Check your email and the Resend dashboard</li>
                    </ol>
                </div>

                {/* Test Buttons */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button
                        onClick={testResendApi}
                        disabled={isTesting}
                        className="bg-purple-600 hover:bg-purple-700 disabled:bg-purple-300 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                    >
                        {isTesting ? '🔄 Testing API...' : '🔧 Test Resend API'}
                    </button>

                    <button
                        onClick={createTestListing}
                        disabled={isCreating}
                        className="bg-green-600 hover:bg-green-700 disabled:bg-green-300 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                    >
                        {isCreating ? '🔄 Creating...' : '📝 Create Test Listing'}
                    </button>

                    <button
                        onClick={triggerNotifications}
                        disabled={isTriggering}
                        className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                    >
                        {isTriggering ? '🔄 Triggering...' : '🚀 Trigger Notifications'}
                    </button>
                </div>

                {/* Message Display */}
                {message && (
                    <div className={`p-4 rounded-lg ${message.startsWith('✅')
                            ? 'bg-green-50 border border-green-200 text-green-800'
                            : 'bg-red-50 border border-red-200 text-red-800'
                        }`}>
                        <pre className="whitespace-pre-wrap font-mono text-sm">{message}</pre>
                    </div>
                )}

                {/* System Overview */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                    <h2 className="text-lg font-semibold text-gray-900 mb-3">🔍 System Overview</h2>
                    <div className="space-y-2 text-sm text-gray-600">
                        <p><strong>Database Trigger:</strong> Automatically creates notifications when new listings match saved criteria</p>
                        <p><strong>Edge Function:</strong> Processes pending notifications and sends emails via Resend</p>
                        <p><strong>API Endpoint:</strong> Manually triggers the edge function for testing</p>
                        <p><strong>RLS Policies:</strong> Ensure users only see their own notifications</p>
                    </div>
                </div>

                {/* Current Configuration */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                    <h2 className="text-lg font-semibold text-yellow-900 mb-3">⚙️ Current Configuration</h2>
                    <div className="space-y-2 text-sm text-yellow-800">
                        <p><strong>Test User:</strong> <EMAIL></p>
                        <p><strong>Match Criteria:</strong> Tech businesses, $50K-$500K price range</p>
                        <p><strong>Test Listings:</strong> Created with matching criteria to trigger notifications</p>
                        <p><strong>Email Provider:</strong> Resend.com</p>
                    </div>
                </div>

                {/* Production Notes */}
                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                    <h2 className="text-lg font-semibold text-red-900 mb-3">🚨 Production Considerations</h2>
                    <ul className="list-disc list-inside space-y-1 text-sm text-red-800">
                        <li>Set up proper domain verification in Resend</li>
                        <li>Configure rate limiting for email sending</li>
                        <li>Add unsubscribe functionality</li>
                        <li>Implement email templates with better styling</li>
                        <li>Add user preferences for notification frequency</li>
                        <li>Set up monitoring and error alerts</li>
                        <li>Consider using a queue for high-volume scenarios</li>
                    </ul>
                </div>
            </div>
        </div>
    )
} 