'use client'

import { useState } from 'react'
import { createClient } from '@/utils/supabase/client'
import { Loader2, Mail, CheckCircle, AlertCircle } from 'lucide-react'

export default function TestNotificationsClient() {
    const [isLoading, setIsLoading] = useState(false)
    const [message, setMessage] = useState('')
    const [error, setError] = useState('')

    const supabase = createClient()

    const triggerNotifications = async () => {
        try {
            setIsLoading(true)
            setMessage('')
            setError('')

            const response = await fetch('/api/trigger-match-notifications', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            })

            const data = await response.json()

            if (!response.ok) {
                throw new Error(data.error || 'Failed to trigger notifications')
            }

            setMessage(data.message || 'Notifications triggered successfully!')
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred')
        } finally {
            setIsLoading(false)
        }
    }

    const createTestListing = async () => {
        try {
            setIsLoading(true)
            setMessage('')
            setError('')

            // Get current user
            const { data: { session } } = await supabase.auth.getSession()
            if (!session) {
                throw new Error('User not authenticated')
            }

            // Create a test listing that should match common criteria
            const { data, error } = await supabase
                .from('listings')
                .insert([
                    {
                        user_id: session.user.id,
                        title: `Test E-commerce Business ${new Date().getTime()}`,
                        description: 'A profitable online business selling digital products. Great for testing match notifications!',
                        price: 150000, // $150k - should match many criteria
                        industry_id: null, // Will match "Any Industry"
                        sub_industry_id: null,
                    }
                ])
                .select()

            if (error) {
                throw new Error(error.message)
            }

            setMessage(`Test listing created successfully! ID: ${data[0].id}. Check for email notifications.`)
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred')
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-semibold text-blue-900 mb-2">How it works:</h3>
                <ol className="list-decimal list-inside text-blue-800 space-y-1 text-sm">
                    <li>Create saved matches in the Match page with your preferred criteria</li>
                    <li>When new listings are created that match your criteria, notifications are queued</li>
                    <li>The email notification system sends you alerts about matching opportunities</li>
                    <li>Use the buttons below to test the system</li>
                </ol>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                        <Mail className="w-4 h-4 mr-2" />
                        Trigger Email Notifications
                    </h4>
                    <p className="text-gray-600 text-sm mb-4">
                        Process any pending match notifications and send emails.
                    </p>
                    <button
                        onClick={triggerNotifications}
                        disabled={isLoading}
                        className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 transition-colors font-medium text-sm"
                    >
                        {isLoading ? (
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                            <Mail className="w-4 h-4 mr-2" />
                        )}
                        Send Notifications
                    </button>
                </div>

                <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Create Test Listing
                    </h4>
                    <p className="text-gray-600 text-sm mb-4">
                        Create a test listing that should trigger match notifications.
                    </p>
                    <button
                        onClick={createTestListing}
                        disabled={isLoading}
                        className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors font-medium text-sm"
                    >
                        {isLoading ? (
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                            <CheckCircle className="w-4 h-4 mr-2" />
                        )}
                        Create Test Listing
                    </button>
                </div>
            </div>

            {message && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                    <p className="text-green-800">{message}</p>
                </div>
            )}

            {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start">
                    <AlertCircle className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
                    <p className="text-red-800">{error}</p>
                </div>
            )}

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-semibold text-green-900 mb-2">📧 Email Delivery:</h4>
                <p className="text-green-800 text-sm">
                    Emails are now sent to real user email addresses from their profile data.
                    Make sure users have valid email addresses in their profiles.
                </p>
            </div>
        </div>
    )
} 