'use client';

import { useState } from 'react';
import { createClient } from '@/utils/supabase/client';

interface Listing {
    id: string;
    title: string;
}

interface Profile {
    user_id: string;
    first_name: string | null;
    last_name: string | null;
    email: string | null;
    company: string | null;
}

interface TestLoiEmailProps {
    listings: Listing[];
    profiles: Profile[];
    currentUserId: string;
}

interface TestResult {
    success: boolean;
    message: string;
    messageId?: string;
    emailId?: string;
}

export default function TestLoiEmail({ listings, profiles, currentUserId }: TestLoiEmailProps) {
    const [selectedListing, setSelectedListing] = useState<string>('');
    const [selectedRecipient, setSelectedRecipient] = useState<string>('');
    const [fileName, setFileName] = useState<string>('Sample_LOI.pdf');
    const [fileSize, setFileSize] = useState<number>(45320); // ~44 KB
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [result, setResult] = useState<TestResult | null>(null);

    const supabase = createClient();

    const handleSendTestLoiEmail = async () => {
        if (!selectedListing || !selectedRecipient) {
            setResult({
                success: false,
                message: 'Please select both a listing and recipient'
            });
            return;
        }

        setIsLoading(true);
        setResult(null);

        try {
            // 1. Create a test message to simulate LOI sharing
            const messageContent = `${profiles.find(p => p.user_id === currentUserId)?.first_name || 'User'} shared a Letter of Intent`;

            const { data: messageData, error: messageError } = await supabase
                .from('messages')
                .insert([
                    {
                        content: messageContent,
                        sender_id: currentUserId,
                        recipient_id: selectedRecipient,
                        listing_id: selectedListing,
                    }
                ])
                .select()
                .single();

            if (messageError) {
                throw new Error(`Message creation failed: ${messageError.message}`);
            }

            // 2. Create a test attachment
            const { error: attachmentError } = await supabase
                .from('message_attachments')
                .insert([
                    {
                        message_id: messageData.id,
                        file_name: fileName,
                        file_url: `https://test-url.com/${fileName}`,
                        file_size: fileSize,
                        file_type: 'application/pdf',
                        attachment_type: 'letter_of_intent',
                        uploaded_by: currentUserId,
                    }
                ]);

            if (attachmentError) {
                throw new Error(`Attachment creation failed: ${attachmentError.message}`);
            }

            // 3. Send LOI email notification
            try {
                console.log('📧 Triggering LOI email notification for message:', messageData.id);
                const emailResponse = await fetch('/api/send-loi-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        messageId: messageData.id,
                        attachmentData: {
                            file_name: fileName,
                            file_size: fileSize,
                            file_type: 'application/pdf',
                            attachment_type: 'letter_of_intent'
                        }
                    })
                });

                const emailResult = await emailResponse.json();
                console.log('📧 LOI Email API response:', emailResult);

                if (emailResult.success) {
                    setResult({
                        success: true,
                        message: `LOI test successful! Email sent to ${emailResult.recipient_email}`,
                        messageId: messageData.id,
                        emailId: emailResult.email_id
                    });
                } else {
                    setResult({
                        success: false,
                        message: `Message created but LOI email failed: ${emailResult.error || 'Unknown error'}`
                    });
                }
            } catch (emailError) {
                console.log('📧 LOI email notification failed (non-critical):', emailError);
                setResult({
                    success: true,
                    message: `Message created successfully but LOI email notification failed: ${emailError}`,
                    messageId: messageData.id
                });
            }

            // Reset form
            setSelectedListing('');
            setSelectedRecipient('');
            setFileName('Sample_LOI.pdf');
            setFileSize(45320);

        } catch (error) {
            console.error('Error in LOI test:', error);
            setResult({
                success: false,
                message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
        } finally {
            setIsLoading(false);
        }
    };

    const selectedRecipientProfile = profiles.find(p => p.user_id === selectedRecipient);

    return (
        <div className="max-w-4xl mx-auto p-6">
            <div className="bg-white rounded-lg shadow-lg p-8">
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">🧪 LOI Email Testing</h1>
                    <p className="text-gray-600">
                        Test the Letter of Intent email notification system by simulating an LOI upload and email delivery.
                    </p>
                </div>

                <div className="space-y-6">
                    {/* Listing Selection */}
                    <div>
                        <label htmlFor="listing" className="block text-sm font-medium text-gray-700 mb-2">
                            Select Listing (LOI is for)
                        </label>
                        <select
                            id="listing"
                            value={selectedListing}
                            onChange={(e) => setSelectedListing(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">Select a listing...</option>
                            {listings.map((listing) => (
                                <option key={listing.id} value={listing.id}>
                                    {listing.title}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Recipient Selection */}
                    <div>
                        <label htmlFor="recipient" className="block text-sm font-medium text-gray-700 mb-2">
                            Select Listing Owner (Email Recipient)
                        </label>
                        <select
                            id="recipient"
                            value={selectedRecipient}
                            onChange={(e) => setSelectedRecipient(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">Select a recipient...</option>
                            {profiles.map((profile) => (
                                <option key={profile.user_id} value={profile.user_id}>
                                    {profile.first_name} {profile.last_name} {profile.company ? `(${profile.company})` : ''} - {profile.email}
                                </option>
                            ))}
                        </select>
                        {selectedRecipientProfile && (
                            <p className="mt-2 text-sm text-gray-600">
                                Email will be sent to: <strong>{selectedRecipientProfile.email}</strong>
                            </p>
                        )}
                    </div>

                    {/* File Details */}
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <label htmlFor="fileName" className="block text-sm font-medium text-gray-700 mb-2">
                                LOI File Name
                            </label>
                            <input
                                type="text"
                                id="fileName"
                                value={fileName}
                                onChange={(e) => setFileName(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label htmlFor="fileSize" className="block text-sm font-medium text-gray-700 mb-2">
                                File Size (bytes)
                            </label>
                            <input
                                type="number"
                                id="fileSize"
                                value={fileSize}
                                onChange={(e) => setFileSize(parseInt(e.target.value) || 0)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                            <p className="mt-1 text-xs text-gray-500">
                                {(fileSize / 1024).toFixed(1)} KB
                            </p>
                        </div>
                    </div>

                    {/* Send Button */}
                    <div>
                        <button
                            onClick={handleSendTestLoiEmail}
                            disabled={isLoading || !selectedListing || !selectedRecipient}
                            className="w-full px-6 py-3 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                        >
                            {isLoading ? 'Sending LOI Test...' : 'Send Test LOI & Trigger Email'}
                        </button>
                    </div>

                    {/* Result Display */}
                    {result && (
                        <div className={`p-4 rounded-md ${result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                            <div className={`font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                                {result.success ? '✅ Success!' : '❌ Error'}
                            </div>
                            <div className={`mt-1 text-sm ${result.success ? 'text-green-700' : 'text-red-700'}`}>
                                {result.message}
                            </div>
                            {result.messageId && (
                                <div className="mt-2 text-xs text-gray-600">
                                    Message ID: {result.messageId}
                                </div>
                            )}
                            {result.emailId && (
                                <div className="mt-1 text-xs text-gray-600">
                                    Email ID: {result.emailId}
                                </div>
                            )}
                        </div>
                    )}
                </div>

                <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-semibold text-blue-800 mb-2">How this LOI email system works:</h3>
                    <ol className="text-sm text-blue-700 list-decimal list-inside space-y-1">
                        <li>A buyer uploads a Letter of Intent as an attachment to a message</li>
                        <li>After successful message and attachment creation, the UI calls <code>/api/send-loi-email</code></li>
                        <li>The API route fetches message details, sender/recipient profiles, and listing information</li>
                        <li>A specialized LOI email is sent to the listing owner using Resend</li>
                        <li>The email features a professional design with LOI-specific content and CTAs</li>
                    </ol>
                </div>

                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 className="font-semibold text-green-800 mb-2">✅ LOI Email Features:</h3>
                    <ul className="text-sm text-green-700 list-disc list-inside space-y-1">
                        <li>Green-themed design to highlight the importance of receiving an LOI</li>
                        <li>File details display (name, sender, size)</li>
                        <li>Business-focused guidance and next steps</li>
                        <li>Professional advice about LOI handling and due diligence</li>
                        <li>Direct links to view/download the LOI and reply to the buyer</li>
                        <li>Security reminders specific to business transactions</li>
                    </ul>
                </div>

                <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h3 className="font-semibold text-yellow-800 mb-2">🧪 Test Environment:</h3>
                    <ul className="text-sm text-yellow-700 list-disc list-inside space-y-1">
                        <li>Emails are sent to test address: <code><EMAIL></code></li>
                        <li>Test messages and attachments are created in the database</li>
                        <li>Email delivery uses the verified domain: <code><EMAIL></code></li>
                        <li>Non-blocking design: Email failures don&apos;t prevent LOI uploads</li>
                    </ul>
                </div>
            </div>
        </div>
    );
} 