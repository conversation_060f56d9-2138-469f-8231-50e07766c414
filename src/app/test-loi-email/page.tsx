import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import TestLoiEmail from './TestLoiEmail';

export default async function TestLoiEmailPage() {
    const supabase = await createClient();

    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
        redirect('/auth/signin');
    }

    // Get some test listings
    const { data: listings } = await supabase
        .from('listings')
        .select('id, title')
        .limit(10);

    // Get some test profiles (excluding current user)
    const { data: profiles } = await supabase
        .from('profiles')
        .select('user_id, first_name, last_name, email, company')
        .neq('user_id', session.user.id)
        .limit(10);

    return (
        <div className="container mx-auto py-8">
            <TestLoiEmail
                listings={listings || []}
                profiles={profiles || []}
                currentUserId={session.user.id}
            />
        </div>
    );
} 