import { Users, Target, Award, TrendingUp, Lightbulb, Shield, Star, MessageCircle, ArrowRight } from 'lucide-react';
import { <PERSON><PERSON>, Footer } from '@/components';

export default function AboutPage() {
    return (
        <>
            <Header />
            <main className="min-h-screen bg-gray-50 py-8">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Header Card */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                        <div className="flex items-center space-x-3">
                            <div className="p-2 bg-purple-50 rounded-lg">
                                <Users className="w-5 h-5 text-purple-600" />
                            </div>
                            <div>
                                <h1 className="text-2xl font-semibold text-gray-900">About Our Company</h1>
                                <p className="text-gray-600 text-sm mt-1">
                                    Learn more about our mission, values, and team
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Hero Section */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                        <div className="max-w-4xl mx-auto text-center">
                            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-900">
                                Our Story Begins With a Vision
                            </h2>
                            <p className="text-lg text-gray-600 leading-relaxed">
                                We started with a simple idea: to make business transactions more transparent, efficient, and trustworthy. Our marketplace connects serious buyers and sellers, providing the tools and confidence needed for successful business acquisitions.
                            </p>
                        </div>
                    </div>

                    {/* Stats Section */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        {[
                            {
                                number: '500+',
                                label: 'Businesses Listed',
                                icon: TrendingUp,
                                color: 'blue'
                            },
                            {
                                number: '1,000+',
                                label: 'Registered Users',
                                icon: Users,
                                color: 'green'
                            },
                            {
                                number: '$50M+',
                                label: 'Total Deal Value',
                                icon: Award,
                                color: 'purple'
                            },
                        ].map((stat, index) => (
                            <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6">
                                <div className="flex items-center space-x-3 mb-4">
                                    <div className={`p-2 bg-${stat.color}-50 rounded-lg`}>
                                        <stat.icon className={`w-5 h-5 text-${stat.color}-600`} />
                                    </div>
                                    <div className="text-left">
                                        <h3 className="text-2xl font-bold text-gray-900">{stat.number}</h3>
                                        <p className="text-gray-600 text-sm font-medium">{stat.label}</p>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Mission Section */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                        <div className="flex items-center space-x-3 mb-6">
                            <div className="p-2 bg-green-50 rounded-lg">
                                <Target className="w-5 h-5 text-green-600" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Our Mission</h2>
                                <p className="text-gray-600 text-sm mt-1">
                                    What drives us every day
                                </p>
                            </div>
                        </div>
                        <div className="max-w-4xl">
                            <p className="text-lg text-gray-600 leading-relaxed">
                                We&apos;re dedicated to creating the most trusted and efficient marketplace for business transactions. Our platform empowers entrepreneurs to find the perfect business opportunities while providing sellers with access to qualified, serious buyers. Through innovative technology and exceptional service, we&apos;re transforming how businesses change hands.
                            </p>
                        </div>
                    </div>

                    {/* Team Section */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                        <div className="flex items-center space-x-3 mb-8">
                            <div className="p-2 bg-blue-50 rounded-lg">
                                <Users className="w-5 h-5 text-blue-600" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Our Leadership Team</h2>
                                <p className="text-gray-600 text-sm mt-1">
                                    Meet the people behind our success
                                </p>
                            </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {[
                                {
                                    name: 'John Smith',
                                    role: 'Chief Executive Officer',
                                    image: '/placeholder-ceo.jpg',
                                    description: 'Former investment banker with 15+ years in M&A transactions'
                                },
                                {
                                    name: 'Sarah Johnson',
                                    role: 'Chief Technology Officer',
                                    image: '/placeholder-cto.jpg',
                                    description: 'Tech veteran who built scalable platforms at major fintech companies'
                                },
                                {
                                    name: 'Michael Brown',
                                    role: 'Chief Operating Officer',
                                    image: '/placeholder-coo.jpg',
                                    description: 'Operations expert focused on creating seamless user experiences'
                                }
                            ].map((member, index) => (
                                <div key={index} className="text-center group">
                                    <div className="relative w-32 h-32 mx-auto mb-4 rounded-full overflow-hidden bg-gray-100">
                                        <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                            <Users className="w-12 h-12 text-gray-400" />
                                        </div>
                                    </div>
                                    <h3 className="text-lg font-semibold mb-1 text-gray-900">{member.name}</h3>
                                    <p className="text-blue-600 font-medium text-sm mb-3">{member.role}</p>
                                    <p className="text-gray-600 text-sm leading-relaxed">{member.description}</p>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Values Section */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                        <div className="flex items-center space-x-3 mb-8">
                            <div className="p-2 bg-orange-50 rounded-lg">
                                <Star className="w-5 h-5 text-orange-600" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Our Core Values</h2>
                                <p className="text-gray-600 text-sm mt-1">
                                    The principles that guide everything we do
                                </p>
                            </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {[
                                {
                                    title: 'Innovation',
                                    description: 'Constantly pushing boundaries and exploring new solutions to serve our users better',
                                    icon: Lightbulb,
                                    color: 'yellow'
                                },
                                {
                                    title: 'Integrity',
                                    description: 'Maintaining the highest standards of professional conduct and transparency',
                                    icon: Shield,
                                    color: 'green'
                                },
                                {
                                    title: 'Excellence',
                                    description: 'Delivering outstanding results and exceeding expectations in everything we do',
                                    icon: Award,
                                    color: 'purple'
                                },
                                {
                                    title: 'Collaboration',
                                    description: 'Working together with our community to achieve common goals and mutual success',
                                    icon: Users,
                                    color: 'blue'
                                }
                            ].map((value, index) => (
                                <div key={index} className="bg-gray-50 rounded-xl p-6 text-center hover:bg-gray-100 transition-colors">
                                    <div className={`p-3 bg-${value.color}-50 rounded-full inline-block mb-4`}>
                                        <value.icon className={`w-6 h-6 text-${value.color}-600`} />
                                    </div>
                                    <h3 className="text-lg font-semibold mb-3 text-gray-900">{value.title}</h3>
                                    <p className="text-gray-600 text-sm leading-relaxed">{value.description}</p>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Call to Action */}
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-200/60 p-8">
                        <div className="max-w-4xl mx-auto text-center">
                            <div className="flex items-center justify-center space-x-3 mb-6">
                                <div className="p-2 bg-blue-100 rounded-lg">
                                    <MessageCircle className="w-5 h-5 text-blue-600" />
                                </div>
                                <h2 className="text-2xl font-semibold text-gray-900">Join Us on Our Journey</h2>
                            </div>
                            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                                We&apos;re always looking for passionate individuals and partners who share our vision of transforming business transactions. Whether you&apos;re looking to buy, sell, or join our team, we&apos;d love to hear from you.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium group">
                                    <MessageCircle className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                                    Get in Touch
                                </button>
                                <button className="inline-flex items-center px-6 py-3 bg-white text-blue-600 border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors font-medium group">
                                    <TrendingUp className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                                    View Opportunities
                                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
            <Footer />
        </>
    );
} 