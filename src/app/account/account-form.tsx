'use client'
import { type User } from '@supabase/supabase-js'
import { useState } from 'react'
import { createClient } from '@/utils/supabase/client'
import { User as UserIcon, Building, Globe, Twitter, Facebook, Linkedin, Instagram, Zap } from 'lucide-react'

const supabase = createClient()

interface AccountFormProps {
    user: User;
    profile: {
        first_name: string;
        last_name: string;
        email: string;
        title?: string;
        website?: string;
        company?: string;
        twitter?: string;
        facebook?: string;
        linkedin?: string;
        instagram?: string;
        bluesky?: string;
    };
}

const AccountForm: React.FC<AccountFormProps> = ({ user, profile }) => {
    const [formData, setFormData] = useState(profile);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isSuccess, setIsSuccess] = useState(false);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
        // Reset success state when user starts editing
        setIsSuccess(false);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        setError(null);
        setIsSuccess(false);

        const { error } = await supabase
            .from('profiles')
            .update(formData)
            .eq('user_id', user.id);

        if (error) {
            setError(error.message);
            setIsSuccess(false);
        } else {
            const { data: updatedProfile, error: fetchError } = await supabase
                .from('profiles')
                .select('*')
                .eq('user_id', user.id)
                .single();

            if (fetchError) {
                setError(fetchError.message);
                setIsSuccess(false);
            } else {
                setFormData(updatedProfile);
                setIsSuccess(true);
            }
        }

        setIsLoading(false);
    };

    return (
        <form onSubmit={handleSubmit} className="max-w-2xl mx-auto space-y-8">
            {/* Basic Information Card */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                <div className="flex items-center space-x-3 mb-6">
                    <div className="p-2 bg-blue-50 rounded-lg">
                        <UserIcon className="w-5 h-5 text-blue-600" />
                    </div>
                    <h2 className="text-xl font-semibold text-gray-900">Basic Information</h2>
                </div>

                {error && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-red-700 text-sm">{error}</p>
                    </div>
                )}

                {isSuccess && (
                    <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <p className="text-green-700 text-sm">Profile updated successfully!</p>
                    </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label htmlFor="first_name" className="block text-sm font-medium text-gray-700 mb-2">
                            First Name
                        </label>
                        <input
                            name="first_name"
                            value={formData.first_name}
                            onChange={handleChange}
                            placeholder="Enter your first name"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        />
                    </div>

                    <div>
                        <label htmlFor="last_name" className="block text-sm font-medium text-gray-700 mb-2">
                            Last Name
                        </label>
                        <input
                            name="last_name"
                            value={formData.last_name}
                            onChange={handleChange}
                            placeholder="Enter your last name"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        />
                    </div>

                    <div className="md:col-span-2">
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <input
                            name="email"
                            value={formData.email}
                            onChange={handleChange}
                            placeholder="Enter your email"
                            type="email"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        />
                    </div>
                </div>
            </div>

            {/* Professional Information Card */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                <div className="flex items-center space-x-3 mb-6">
                    <div className="p-2 bg-green-50 rounded-lg">
                        <Building className="w-5 h-5 text-green-600" />
                    </div>
                    <h2 className="text-xl font-semibold text-gray-900">Professional Information</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                            Job Title
                        </label>
                        <input
                            name="title"
                            value={formData.title || ''}
                            onChange={handleChange}
                            placeholder="e.g. CEO, Entrepreneur, Investor"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        />
                    </div>

                    <div>
                        <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
                            Company
                        </label>
                        <input
                            name="company"
                            value={formData.company || ''}
                            onChange={handleChange}
                            placeholder="Enter your company name"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        />
                    </div>

                    <div className="md:col-span-2">
                        <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
                            Website
                        </label>
                        <div className="relative">
                            <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                                name="website"
                                value={formData.website || ''}
                                onChange={handleChange}
                                placeholder="https://yourwebsite.com"
                                type="url"
                                className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* Social Media Card */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                <div className="flex items-center space-x-3 mb-6">
                    <div className="p-2 bg-purple-50 rounded-lg">
                        <Zap className="w-5 h-5 text-purple-600" />
                    </div>
                    <h2 className="text-xl font-semibold text-gray-900">Social Media</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label htmlFor="twitter" className="block text-sm font-medium text-gray-700 mb-2">
                            Twitter/X
                        </label>
                        <div className="relative">
                            <Twitter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                                name="twitter"
                                value={formData.twitter || ''}
                                onChange={handleChange}
                                placeholder="@username or full URL"
                                className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            />
                        </div>
                    </div>

                    <div>
                        <label htmlFor="linkedin" className="block text-sm font-medium text-gray-700 mb-2">
                            LinkedIn
                        </label>
                        <div className="relative">
                            <Linkedin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                                name="linkedin"
                                value={formData.linkedin || ''}
                                onChange={handleChange}
                                placeholder="LinkedIn profile URL"
                                className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            />
                        </div>
                    </div>

                    <div>
                        <label htmlFor="facebook" className="block text-sm font-medium text-gray-700 mb-2">
                            Facebook
                        </label>
                        <div className="relative">
                            <Facebook className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                                name="facebook"
                                value={formData.facebook || ''}
                                onChange={handleChange}
                                placeholder="Facebook profile URL"
                                className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            />
                        </div>
                    </div>

                    <div>
                        <label htmlFor="instagram" className="block text-sm font-medium text-gray-700 mb-2">
                            Instagram
                        </label>
                        <div className="relative">
                            <Instagram className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                                name="instagram"
                                value={formData.instagram || ''}
                                onChange={handleChange}
                                placeholder="@username or full URL"
                                className="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            />
                        </div>
                    </div>

                    <div className="md:col-span-2">
                        <label htmlFor="bluesky" className="block text-sm font-medium text-gray-700 mb-2">
                            Bluesky
                        </label>
                        <input
                            name="bluesky"
                            value={formData.bluesky || ''}
                            onChange={handleChange}
                            placeholder="@username.bsky.social or full URL"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        />
                    </div>
                </div>
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
                <button
                    type="submit"
                    className={`px-8 py-3 rounded-lg font-medium transition-all duration-200 ${isSuccess
                            ? 'bg-green-600 text-white hover:bg-green-700'
                            : 'bg-neutral-800 text-white hover:bg-neutral-700 hover:shadow-lg'
                        } disabled:opacity-50 disabled:cursor-not-allowed`}
                    disabled={isLoading}
                >
                    {isLoading ? 'Updating...' : isSuccess ? 'Saved!' : 'Update Profile'}
                </button>
            </div>
        </form>
    );
};

export default AccountForm;