'use client'

import { useState } from 'react'
import { DeleteModal } from '@/components/ui/DeleteModal'
import { removeSavedListing } from '@/app/account/actions'

interface SavedListingRemoveButtonProps {
    listingId: string
}

export function SavedListingRemoveButton({ listingId }: SavedListingRemoveButtonProps) {
    const [showDeleteModal, setShowDeleteModal] = useState(false)
    const [isDeleting, setIsDeleting] = useState(false)

    const handleRemove = async () => {
        setIsDeleting(true)
        const { error } = await removeSavedListing(listingId)

        if (!error) {
            setShowDeleteModal(false)
        }
        setIsDeleting(false)
    }

    return (
        <>
            <button
                onClick={() => setShowDeleteModal(true)}
                className="text-red-600 hover:underline"
            >
                Remove
            </button>

            <DeleteModal
                isOpen={showDeleteModal}
                onClose={() => setShowDeleteModal(false)}
                onConfirm={handleRemove}
                isLoading={isDeleting}
                title="Remove from Saved"
                description="Are you sure you want to remove this listing from your saved items?"
            />
        </>
    )
} 