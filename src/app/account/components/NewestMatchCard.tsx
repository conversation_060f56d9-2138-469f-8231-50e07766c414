'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/utils/supabase/client'
import Link from 'next/link'
import Image from 'next/image'
import { Target, Clock, TrendingUp, MapPin, DollarSign, Calendar, ChevronRight, Eye, Heart } from 'lucide-react'
import { PriceFormatter } from '@/components'

// Custom Favorite Button for Match Card that triggers refresh
function MatchFavoriteButton({
    listingId,
    listingUserId,
    onSaveChange
}: {
    listingId: string
    listingUserId: string
    onSaveChange?: () => void
}) {
    const [isSaved, setIsSaved] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const [showConfirmModal, setShowConfirmModal] = useState(false)
    const [isOwnListing, setIsOwnListing] = useState(false)
    const supabase = createClient()

    useEffect(() => {
        const checkOwnershipAndFavorited = async () => {
            try {
                const { data: { session } } = await supabase.auth.getSession()

                if (!session?.user) {
                    setIsOwnListing(false)
                    setIsSaved(false)
                    return
                }

                // Check if this is the user's own listing
                setIsOwnListing(listingUserId === session.user.id)

                // Only check saved status if it's not their own listing
                if (listingUserId !== session.user.id) {
                    const { data, error } = await supabase
                        .from('saved_listings')
                        .select('listing_id')
                        .eq('listing_id', listingId)
                        .eq('user_id', session.user.id)
                        .maybeSingle()

                    if (error) {
                        console.error('Error checking saved status:', error)
                        setIsSaved(false)
                        return
                    }

                    setIsSaved(!!data)
                }
            } catch (error) {
                console.error('Error checking favorite status:', error)
                setIsSaved(false)
                setIsOwnListing(false)
            }
        }

        if (listingId && listingUserId) {
            checkOwnershipAndFavorited()
        }
    }, [listingId, listingUserId, supabase])

    if (isOwnListing) {
        return null
    }

    const handleToggle = async () => {
        if (!listingId) return

        if (isSaved) {
            setShowConfirmModal(true)
            return
        }

        setIsLoading(true)

        try {
            const { data: { session } } = await supabase.auth.getSession()

            if (!session?.user) {
                window.location.href = '/login'
                return
            }

            const { error } = await supabase
                .from('saved_listings')
                .insert({
                    listing_id: listingId,
                    user_id: session.user.id
                })

            if (error) throw error
            setIsSaved(true)
            // Small delay to ensure state is updated before triggering refresh
            setTimeout(() => {
                onSaveChange?.()
            }, 100)
        } catch (error) {
            console.error('Error saving listing:', error)
        } finally {
            setIsLoading(false)
        }
    }

    const handleRemove = async () => {
        setIsLoading(true)
        try {
            const { data: { session } } = await supabase.auth.getSession()

            if (!session?.user) {
                setShowConfirmModal(false)
                setIsLoading(false)
                return
            }

            const { error } = await supabase
                .from('saved_listings')
                .delete()
                .match({
                    listing_id: listingId,
                    user_id: session.user.id
                })

            if (error) throw error

            setIsSaved(false)
            setShowConfirmModal(false)

            // Small delay to ensure modal is closed before triggering refresh
            setTimeout(() => {
                onSaveChange?.()
            }, 100)
        } catch (error) {
            console.error('Error removing listing:', error)
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <>
            <button
                onClick={handleToggle}
                disabled={isLoading}
                className="absolute top-4 right-4 p-2 rounded-full bg-white/80 backdrop-blur-sm hover:bg-white transition-colors shadow-sm z-10"
            >
                <Heart
                    className={`w-5 h-5 ${isSaved ? 'fill-red-500 stroke-red-500' : 'stroke-gray-600'}`}
                />
            </button>

            {/* Confirmation Modal */}
            {showConfirmModal && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-sm mx-4 w-full">
                        <h3 className="text-lg font-semibold mb-4">Remove from Saved</h3>
                        <p className="text-gray-600 mb-6">
                            Are you sure you want to remove this listing from your saved items?
                        </p>
                        <div className="flex gap-3">
                            <button
                                onClick={() => setShowConfirmModal(false)}
                                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleRemove}
                                disabled={isLoading}
                                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                            >
                                Remove
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    )
}

interface MatchNotification {
    id: string
    sent_at: string
    email_sent: boolean
    match_name: string
    preferences: {
        priceMin: number
        priceMax: number
        revenueMin: number
        revenueMax: number
        industryId?: string
        subIndustryId?: string
        location?: string
        locationCity?: string
        locationState?: string
        yearEstablishedMin?: number
    }
    listing_id: string
    listing_title: string
    listing_user_id: string
    price: string
    description: string
    image_url?: string
    industry_name?: string
    sub_industry_name?: string
}

interface NewestMatchCardProps {
    userId: string
    onMatchChange?: () => void
    refreshTrigger?: number
}

export function NewestMatchCard({ userId, onMatchChange, refreshTrigger }: NewestMatchCardProps) {
    const [newestMatch, setNewestMatch] = useState<MatchNotification | null>(null)
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [hasMatchCriteria, setHasMatchCriteria] = useState(false)

    const supabase = createClient()

    const checkMatchCriteria = useCallback(async () => {
        try {
            const { data, error } = await supabase
                .from('saved_matches')
                .select('id')
                .eq('user_id', userId)
                .limit(1)

            if (error) {
                console.error('Error checking match criteria:', error)
                setHasMatchCriteria(false)
                return
            }

            setHasMatchCriteria(data && data.length > 0)
        } catch (err) {
            console.error('Error checking match criteria:', err)
            setHasMatchCriteria(false)
        }
    }, [userId, supabase])

    const fetchNewestMatch = useCallback(async () => {
        try {
            setIsLoading(true)

            // First, get the saved listing IDs for this user
            const { data: savedListings } = await supabase
                .from('saved_listings')
                .select('listing_id')
                .eq('user_id', userId)

            const savedListingIds = savedListings?.map(saved => saved.listing_id) || []

            // Now fetch match notifications with the correct joins
            let query = supabase
                .from('match_notifications')
                .select(`
                    id,
                    sent_at,
                    email_sent,
                    saved_matches!inner (
                        name,
                        preferences
                    ),
                    listings!inner (
                        id,
                        title,
                        price,
                        description,
                        image_url,
                        user_id,
                        industries (name),
                        sub_industries (name)
                    )
                `)
                .eq('user_id', userId)
                .order('sent_at', { ascending: false })

            // Only add the filter if there are saved listings to exclude
            if (savedListingIds.length > 0) {
                query = query.not('listing_id', 'in', `(${savedListingIds.join(',')})`)
            }

            const { data, error } = await query.limit(1).single()

            if (error) {
                if (error.code === 'PGRST116') {
                    // No matches found
                    setNewestMatch(null)
                } else {
                    throw error
                }
            } else {
                // Transform the data to match our interface
                const savedMatch = Array.isArray(data.saved_matches) ? data.saved_matches[0] : data.saved_matches
                const listing = Array.isArray(data.listings) ? data.listings[0] : data.listings

                // Helper function to safely get industry name
                const getIndustryName = (industries: unknown): string | undefined => {
                    if (Array.isArray(industries) && industries.length > 0) {
                        return industries[0]?.name
                    }
                    if (industries && typeof industries === 'object' && 'name' in industries) {
                        return (industries as { name: string }).name
                    }
                    return undefined
                }

                const transformedData: MatchNotification = {
                    id: data.id,
                    sent_at: data.sent_at,
                    email_sent: data.email_sent,
                    match_name: savedMatch?.name || 'Unknown Match',
                    preferences: {
                        priceMin: getPreferenceValue(savedMatch?.preferences?.priceMin, 0),
                        priceMax: getPreferenceValue(savedMatch?.preferences?.priceMax, 1000000),
                        revenueMin: getPreferenceValue(savedMatch?.preferences?.revenueMin, 0),
                        revenueMax: getPreferenceValue(savedMatch?.preferences?.revenueMax, 1000000),
                        industryId: savedMatch?.preferences?.industryId as string,
                        subIndustryId: savedMatch?.preferences?.subIndustryId as string,
                        location: savedMatch?.preferences?.location as string,
                        locationCity: savedMatch?.preferences?.locationCity as string,
                        locationState: savedMatch?.preferences?.locationState as string,
                        yearEstablishedMin: getPreferenceValue(savedMatch?.preferences?.yearEstablishedMin)
                    },
                    listing_id: listing?.id || '',
                    listing_title: listing?.title || 'Untitled Listing',
                    listing_user_id: listing?.user_id || '',
                    price: listing?.price || '0',
                    description: listing?.description || 'No description available',
                    image_url: listing?.image_url,
                    industry_name: getIndustryName(listing?.industries),
                    sub_industry_name: getIndustryName(listing?.sub_industries)
                }

                setNewestMatch(transformedData)
            }
        } catch (err) {
            console.error('Error fetching newest match:', err)
            setError(err instanceof Error ? err.message : 'Failed to fetch match')
        } finally {
            setIsLoading(false)
        }
    }, [userId, supabase])

    useEffect(() => {
        if (userId) {
            checkMatchCriteria()
            fetchNewestMatch()
        }
    }, [userId, checkMatchCriteria, fetchNewestMatch])

    // Watch for external refresh triggers (like when saved listings are removed)
    useEffect(() => {
        if (refreshTrigger && refreshTrigger > 0) {
            console.log('External refresh trigger received, refreshing match data')
            checkMatchCriteria()
            fetchNewestMatch()
        }
    }, [refreshTrigger, checkMatchCriteria, fetchNewestMatch])

    const handleMatchChange = () => {
        // Refetch both the match data and criteria when save state changes
        checkMatchCriteria()
        fetchNewestMatch()
        // Notify parent component
        onMatchChange?.()
    }

    const formatCurrency = (value: number | string | undefined | null) => {
        // Convert to number and provide fallback
        const numValue = typeof value === 'string' ? parseFloat(value) : (value || 0)
        if (isNaN(numValue) || numValue === 0) {
            return '$0'
        }

        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            notation: numValue >= 1000000 ? 'compact' : 'standard',
            maximumFractionDigits: 0
        }).format(numValue)
    }

    const formatDate = (dateString: string) => {
        const date = new Date(dateString)
        return new Intl.DateTimeFormat('en-US', {
            month: 'long',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        }).format(date)
    }

    // Helper function to safely get preference values
    const getPreferenceValue = (value: unknown, fallback: number = 0): number => {
        if (typeof value === 'number' && !isNaN(value)) {
            return value
        }
        if (typeof value === 'string') {
            const parsed = parseFloat(value)
            return isNaN(parsed) ? fallback : parsed
        }
        return fallback
    }

    if (isLoading) {
        return (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                <div className="flex items-center space-x-3 mb-6">
                    <div className="p-2 bg-orange-50 rounded-lg">
                        <Target className="w-5 h-5 text-orange-600" />
                    </div>
                    <h2 className="text-xl font-semibold text-gray-900">Newest Match</h2>
                </div>
                <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                    <div className="h-20 bg-gray-200 rounded"></div>
                </div>
            </div>
        )
    }

    if (error) {
        return (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                <div className="flex items-center space-x-3 mb-6">
                    <div className="p-2 bg-red-50 rounded-lg">
                        <Target className="w-5 h-5 text-red-600" />
                    </div>
                    <h2 className="text-xl font-semibold text-gray-900">Newest Match</h2>
                </div>
                <p className="text-red-600">Failed to load match data</p>
            </div>
        )
    }

    if (!newestMatch) {
        return (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                <div className="flex items-center space-x-3 mb-6">
                    <div className="p-2 bg-orange-50 rounded-lg">
                        <Target className="w-5 h-5 text-orange-600" />
                    </div>
                    <h2 className="text-xl font-semibold text-gray-900">Newest Match</h2>
                </div>

                <div className="text-center py-8">
                    <div className="p-4 bg-gray-100 rounded-full mb-6 inline-block">
                        <Target className="w-12 h-12 text-gray-400" />
                    </div>
                    {hasMatchCriteria ? (
                        // User has match criteria but no new matches
                        <>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">We&apos;re still searching!</h3>
                            <p className="text-gray-600 mb-6">
                                No new matches found yet, but we&apos;re actively looking for listings that match your saved criteria. Check back soon!
                            </p>
                            <Link
                                href="/match"
                                className="inline-flex items-center px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium group"
                            >
                                <Target className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                                Manage Your Match Criteria
                            </Link>
                        </>
                    ) : (
                        // User has no match criteria yet
                        <>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No matches yet</h3>
                            <p className="text-gray-600 mb-6">
                                Create your first match criteria to get notified when listings match your preferences.
                            </p>
                            <Link
                                href="/match"
                                className="inline-flex items-center px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium group"
                            >
                                <Target className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                                Create Your First Match
                            </Link>
                        </>
                    )}
                </div>
            </div>
        )
    }

    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
            <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-orange-50 rounded-lg">
                    <Target className="w-5 h-5 text-orange-600" />
                </div>
                <div className="flex-1">
                    <h2 className="text-xl font-semibold text-gray-900">Newest Match</h2>
                    <div className="flex items-center text-sm text-gray-500 mt-1">
                        <Clock className="w-4 h-4 mr-1" />
                        Match found on {formatDate(newestMatch.sent_at)}
                    </div>
                </div>
            </div>

            {/* Matched Listing */}
            <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg border border-orange-200 p-6 relative">
                {/* Heart Save Button - positioned like in listings cards */}
                <MatchFavoriteButton
                    listingId={newestMatch.listing_id}
                    listingUserId={newestMatch.listing_user_id}
                    onSaveChange={handleMatchChange}
                />

                <div className="flex items-start gap-4 mb-4">
                    <div className="flex-shrink-0 w-20 h-20 relative rounded-lg overflow-hidden ring-2 ring-orange-300">
                        <Image
                            src={newestMatch.image_url || '/images/placeholder-listing-image.jpg'}
                            alt={newestMatch.listing_title || 'Business listing'}
                            fill
                            className="object-cover"
                        />
                    </div>
                    <div className="flex-grow">
                        <h3 className="font-semibold text-gray-900 text-lg mb-2">
                            {newestMatch.listing_title || 'Untitled Listing'}
                        </h3>
                        <div className="flex items-center gap-4 mb-3">
                            <PriceFormatter
                                price={parseFloat(newestMatch.price) || 0}
                                className="text-lg font-bold text-orange-700"
                            />
                            <span className="text-sm text-orange-800 bg-orange-100 px-3 py-1 rounded-full font-medium">
                                {newestMatch.match_name || 'Match'}
                            </span>
                        </div>
                        <p className="text-gray-700 text-sm line-clamp-2">
                            {newestMatch.description || 'No description available'}
                        </p>
                    </div>
                </div>

                {/* Match Criteria - Now inside the card */}
                <div className="border-t border-orange-200 pt-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
                        <div className="flex items-center space-x-2 text-gray-800 bg-white/70 px-3 py-2 rounded-lg border border-green-200">
                            <ChevronRight className="w-3 h-3 text-green-700" />
                            <DollarSign className="w-3 h-3 text-green-700" />
                            <span className="font-medium">
                                {formatCurrency(getPreferenceValue(newestMatch.preferences.priceMin))} - {formatCurrency(getPreferenceValue(newestMatch.preferences.priceMax))}
                            </span>
                        </div>

                        <div className="flex items-center space-x-2 text-gray-800 bg-white/70 px-3 py-2 rounded-lg border border-blue-200">
                            <ChevronRight className="w-3 h-3 text-blue-700" />
                            <TrendingUp className="w-3 h-3 text-blue-700" />
                            <span className="font-medium">
                                Revenue: {formatCurrency(getPreferenceValue(newestMatch.preferences.revenueMin))} - {formatCurrency(getPreferenceValue(newestMatch.preferences.revenueMax))}
                            </span>
                        </div>

                        {newestMatch.preferences.location && (
                            <div className="flex items-center space-x-2 text-gray-800 bg-white/70 px-3 py-2 rounded-lg border border-red-200">
                                <ChevronRight className="w-3 h-3 text-red-700" />
                                <MapPin className="w-3 h-3 text-red-700" />
                                <span className="truncate font-medium">{newestMatch.preferences.location}</span>
                            </div>
                        )}

                        {newestMatch.preferences.yearEstablishedMin != null &&
                            newestMatch.preferences.yearEstablishedMin !== undefined &&
                            getPreferenceValue(newestMatch.preferences.yearEstablishedMin) > 0 && (
                                <div className="flex items-center space-x-2 text-gray-800 bg-white/70 px-3 py-2 rounded-lg border border-purple-200">
                                    <ChevronRight className="w-3 h-3 text-purple-700" />
                                    <Calendar className="w-3 h-3 text-purple-700" />
                                    <span className="font-medium">Est. after {getPreferenceValue(newestMatch.preferences.yearEstablishedMin)}</span>
                                </div>
                            )}
                    </div>
                </div>
            </div>

            {/* Bottom Actions */}
            <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
                <Link
                    href={`/listings/${newestMatch.listing_id}`}
                    className="inline-flex items-center text-orange-700 hover:text-orange-800 font-semibold text-sm group"
                >
                    <Eye className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                    View Full Listing
                    <ChevronRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </Link>

                <Link
                    href="/match"
                    className="inline-flex items-center text-gray-600 hover:text-gray-700 font-semibold text-sm group"
                >
                    <Target className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                    Manage Your Matches
                </Link>
            </div>
        </div>
    )
} 