'use client'

import { useState } from 'react'
import { Eye, X } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'
import { PriceFormatter } from '@/components'
import { DeleteModal } from '@/components/ui/DeleteModal'
import { createClient } from '@/utils/supabase/client'

interface SavedListingItemProps {
    listing: {
        listing_id: string
        listings: {
            id: string
            title: string
            price: number
            image_url: string | null
        }
    }
    onRemove?: () => void
}

export function SavedListingItem({ listing, onRemove }: SavedListingItemProps) {
    const [showDeleteModal, setShowDeleteModal] = useState(false)
    const [isDeleting, setIsDeleting] = useState(false)
    const supabase = createClient()

    const handleRemove = async () => {
        setIsDeleting(true)

        try {
            const { data: { session } } = await supabase.auth.getSession()

            if (!session?.user) {
                console.error('User not authenticated')
                setIsDeleting(false)
                return
            }

            const { error } = await supabase
                .from('saved_listings')
                .delete()
                .match({
                    listing_id: listing.listing_id,
                    user_id: session.user.id
                })

            if (error) {
                console.error('Error removing saved listing:', error)
            } else {
                setShowDeleteModal(false)
                // Trigger the refresh callback
                onRemove?.()
            }
        } catch (error) {
            console.error('Error removing saved listing:', error)
        } finally {
            setIsDeleting(false)
        }
    }

    return (
        <>
            <div className="flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex-shrink-0 w-20 h-20 relative rounded-md overflow-hidden">
                    <Image
                        src={listing.listings.image_url || '/images/placeholder.png'}
                        alt={listing.listings.title}
                        fill
                        className="object-cover"
                    />
                </div>
                <div className="flex-grow">
                    <h3 className="font-medium">{listing.listings.title}</h3>
                    <PriceFormatter price={listing.listings.price} className="text-sm text-gray-600" />
                </div>
                <div className="flex gap-2">
                    <Link
                        href={`/listings/${listing.listing_id}`}
                        className="flex items-center px-2.5 py-1.5 bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100 hover:border-gray-300 rounded-md transition-colors text-sm"
                        title="View Listing"
                    >
                        <Eye className="w-4 h-4 mr-1.5" />
                        View
                    </Link>
                    <button
                        onClick={() => setShowDeleteModal(true)}
                        className="flex items-center px-2.5 py-1.5 bg-red-50 text-red-600 border border-red-200 hover:bg-red-100 hover:border-red-300 rounded-md transition-colors text-sm"
                        title="Remove from Saved"
                    >
                        <X className="w-4 h-4 mr-1.5" />
                        Remove
                    </button>
                </div>
            </div>

            <DeleteModal
                isOpen={showDeleteModal}
                onClose={() => setShowDeleteModal(false)}
                onConfirm={handleRemove}
                isLoading={isDeleting}
                title="Remove from Saved"
                description="Are you sure you want to remove this listing from your saved items?"
            />
        </>
    )
} 