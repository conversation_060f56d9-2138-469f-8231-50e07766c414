'use server'

import { createClient } from '@/utils/supabase/server'
import { revalidatePath } from 'next/cache'

export async function removeSavedListing(listingId: string) {
    const supabase = await createClient()

    const { error } = await supabase
        .from('saved_listings')
        .delete()
        .eq('listing_id', listingId)

    if (!error) {
        revalidatePath('/account')
    }

    return { error }
} 