'use server'

import { createClient } from '@/utils/supabase/server'
import { revalidatePath } from 'next/cache'

export async function changePassword(formData: FormData): Promise<{ error?: string; success?: boolean }> {
    const supabase = await createClient()

    const currentPassword = formData.get('currentPassword') as string
    const newPassword = formData.get('newPassword') as string
    const confirmPassword = formData.get('confirmPassword') as string

    // Validate inputs
    if (!currentPassword || !newPassword || !confirmPassword) {
        return { error: 'All fields are required' }
    }

    if (newPassword !== confirmPassword) {
        return { error: 'New passwords do not match' }
    }

    if (newPassword.length < 8) {
        return { error: 'New password must be at least 8 characters long' }
    }

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
        return { error: 'You must be logged in to change your password' }
    }

    // Verify current password by attempting to sign in
    const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email!,
        password: currentPassword
    })

    if (signInError) {
        return { error: 'Current password is incorrect' }
    }

    // Update the password
    const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword
    })

    if (updateError) {
        return { error: updateError.message }
    }

    revalidatePath('/account/security')
    return { success: true }
} 