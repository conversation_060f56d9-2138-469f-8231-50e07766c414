import { createClient } from '@/utils/supabase/server'
import { redirect } from 'next/navigation'
import { ChangePasswordForm } from '@/components'
import Link from 'next/link'
import { User, Shield, ChevronRight } from 'lucide-react'

export default async function SecurityPage() {
    const supabase = await createClient()

    const { data: { user }, error } = await supabase.auth.getUser()

    if (error || !user) {
        redirect('/login')
    }

    return (
        <main className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Breadcrumb Navigation */}
                <div className="mb-8">
                    <nav className="flex items-center space-x-2 text-sm">
                        <Link
                            href="/account"
                            className="flex items-center space-x-1 text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <User className="w-4 h-4" />
                            <span>Account</span>
                        </Link>
                        <ChevronRight className="w-4 h-4 text-gray-400" />
                        <div className="flex items-center space-x-1 text-gray-900">
                            <Shield className="w-4 h-4" />
                            <span>Security Settings</span>
                        </div>
                    </nav>
                </div>

                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                    <div className="flex items-center space-x-3 mb-6">
                        <div className="p-2 bg-blue-50 rounded-lg">
                            <Shield className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                            <h1 className="text-xl font-semibold text-gray-900">Change Password</h1>
                            <p className="text-sm text-gray-600">
                                Update your password to keep your account secure
                            </p>
                        </div>
                    </div>

                    <ChangePasswordForm />
                </div>
            </div>
        </main>
    )
} 