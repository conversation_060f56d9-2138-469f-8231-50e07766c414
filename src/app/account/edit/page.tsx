import { createClient } from '@/utils/supabase/server'
import AccountImageSection from '@/components/profile/AccountImageSection'
import PersonalInfoSection from '@/components/profile/PersonalInfoSection'
import SocialLinksSection from '@/components/profile/SocialLinksSection'
import Link from 'next/link'
import { User, Edit, ChevronRight } from 'lucide-react'
import { Suspense } from 'react'

export default async function AccountEditPage() {
    const supabase = await createClient()

    const {
        data: { user },
        error: userError
    } = await supabase.auth.getUser()

    if (userError || !user) {
        return (
            <main className="min-h-screen flex items-center justify-center">
                <div>No user found</div>
            </main>
        )
    }

    const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('profile_photo')
        .eq('user_id', user.id)
        .single()

    if (profileError) {
        console.error('Error loading profile data:', profileError)
        return (
            <main className="min-h-screen flex items-center justify-center">
                <div>Error loading user data</div>
            </main>
        )
    }

    return (
        <main className="min-h-screen py-8 bg-gray-50">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Breadcrumb Navigation */}
                <div className="mb-8">
                    <nav className="flex items-center space-x-2 text-sm">
                        <Link
                            href="/account"
                            className="flex items-center space-x-1 text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <User className="w-4 h-4" />
                            <span>Account</span>
                        </Link>
                        <ChevronRight className="w-4 h-4 text-gray-400" />
                        <div className="flex items-center space-x-1 text-gray-900">
                            <Edit className="w-4 h-4" />
                            <span>Edit Profile</span>
                        </div>
                    </nav>
                </div>

                {/* Page Header with Profile Image */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <div className="p-2 bg-blue-50 rounded-lg">
                                <Edit className="w-5 h-5 text-blue-600" />
                            </div>
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">Edit Profile</h1>
                                <p className="mt-2 text-gray-600">
                                    Update your profile information and preferences.
                                </p>
                            </div>
                        </div>
                        <div className="flex-shrink-0">
                            <AccountImageSection
                                user={user}
                                initialProfilePhotoUrl={profileData?.profile_photo}
                            />
                        </div>
                    </div>
                </div>

                {/* Profile Information Sections */}
                <div className="space-y-6">
                    <Suspense fallback={
                        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                            <div className="animate-pulse">
                                <div className="h-6 bg-gray-200 rounded mb-4 w-1/3"></div>
                                <div className="space-y-3">
                                    <div className="h-4 bg-gray-200 rounded w-full"></div>
                                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                                </div>
                            </div>
                        </div>
                    }>
                        <PersonalInfoSection user={user} />
                    </Suspense>

                    <Suspense fallback={
                        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                            <div className="animate-pulse">
                                <div className="h-6 bg-gray-200 rounded mb-4 w-1/3"></div>
                                <div className="space-y-3">
                                    <div className="h-4 bg-gray-200 rounded w-full"></div>
                                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                                </div>
                            </div>
                        </div>
                    }>
                        <SocialLinksSection user={user} />
                    </Suspense>
                </div>
            </div>
        </main>
    )
} 