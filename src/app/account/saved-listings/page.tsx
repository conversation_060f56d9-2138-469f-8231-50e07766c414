import { Metadata } from 'next'
import { createClient } from '@/utils/supabase/server'
import { redirect } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { PriceFormatter } from '@/components'
import { BookmarkPlus, Heart, Search } from 'lucide-react'

export const metadata: Metadata = {
    title: 'Saved Businesses | Business Marketplace',
    description: 'View your saved businesses',
}

type SavedListing = {
    listing_id: string;
    listings: {
        id: string;
        title: string;
        description: string;
        price: number;
        image_url: string | null;
        created_at: string;
    };
}

export default async function SavedListingsPage() {
    const supabase = await createClient()

    const { data: { session } } = await supabase.auth.getSession()

    if (!session?.user) {
        redirect('/login')
    }

    const { data: savedListings, error } = await supabase
        .from('saved_listings')
        .select(`
            listing_id,
            listings (
                id,
                title,
                description,
                price,
                image_url,
                created_at
            )
        `)
        .eq('user_id', session.user.id)
        .returns<SavedListing[]>();

    if (error) {
        console.error('Error fetching saved listings:', error)
        return <div>Error loading saved listings</div>
    }

    return (
        <main className="min-h-screen py-8 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header Card */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <div className="p-2 bg-red-50 rounded-lg">
                                <Heart className="w-5 h-5 text-red-600" />
                            </div>
                            <div>
                                <h1 className="text-2xl font-semibold text-gray-900">Saved Businesses</h1>
                                <p className="text-gray-600 text-sm mt-1">
                                    {savedListings.length} {savedListings.length === 1 ? 'business' : 'businesses'} saved
                                </p>
                            </div>
                        </div>
                        <Link
                            href="/listings"
                            className="inline-flex items-center px-4 py-2 bg-neutral-800 text-white rounded-lg hover:bg-neutral-700 transition-colors font-medium group"
                        >
                            <Search className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                            Browse More
                        </Link>
                    </div>
                </div>

                {savedListings.length === 0 ? (
                    /* Empty State Card */
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-12">
                        <div className="flex flex-col items-center max-w-md mx-auto text-center">
                            <div className="p-4 bg-red-50 rounded-full mb-6">
                                <BookmarkPlus className="w-12 h-12 text-red-600" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-3">No Saved Businesses Yet</h3>
                            <p className="text-gray-600 mb-8 leading-relaxed">
                                Start saving interesting businesses you&apos;d like to track.
                                Browse our marketplace to find your next opportunity.
                            </p>
                            <Link
                                href="/listings"
                                className="inline-flex items-center px-6 py-3 bg-neutral-800 text-white rounded-lg hover:bg-neutral-700 transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-500 font-medium group"
                            >
                                <Search className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                                Browse Businesses
                            </Link>
                        </div>
                    </div>
                ) : (
                    /* Listings Grid */
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {savedListings.map((saved) => (
                            <Link
                                href={`/listings/${saved.listing_id}`}
                                key={saved.listing_id}
                                className="bg-white rounded-xl shadow-sm border border-gray-200/60 overflow-hidden hover:shadow-md transition-shadow group block"
                            >
                                {/* Image Section */}
                                <div className="relative h-48">
                                    <Image
                                        src={saved.listings.image_url || '/images/placeholder-listing-image.jpg'}
                                        alt={saved.listings.title}
                                        fill
                                        className="object-cover group-hover:scale-105 transition-transform duration-200"
                                    />
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
                                    <div className="absolute top-3 right-3">
                                        <div className="p-2 bg-red-50/90 backdrop-blur-sm rounded-full">
                                            <Heart className="w-4 h-4 text-red-600 fill-current" />
                                        </div>
                                    </div>
                                </div>

                                {/* Content Section */}
                                <div className="p-6">
                                    <h2 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                                        {saved.listings.title}
                                    </h2>
                                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                                        {saved.listings.description}
                                    </p>
                                    <div className="flex items-center justify-between">
                                        <PriceFormatter
                                            price={saved.listings.price}
                                            className="text-lg font-semibold text-gray-900"
                                        />
                                        <span className="text-xs text-gray-500">
                                            Saved {new Date(saved.listings.created_at).toLocaleDateString()}
                                        </span>
                                    </div>
                                </div>
                            </Link>
                        ))}
                    </div>
                )}
            </div>
        </main>
    )
} 