import { Metadata } from 'next'
import MyListingsClient from './MyListingsClient'
import { createClient } from '@/utils/supabase/server'
import { redirect } from 'next/navigation'

export const metadata: Metadata = {
    title: 'My Businesses | Business Marketplace',
    description: 'Manage your businesses for sale',
}

export default async function MyListingsPage() {
    const supabase = await createClient()

    const { data: { session } } = await supabase.auth.getSession()

    if (!session?.user) {
        redirect('/login')
    }

    const { data: listings, error } = await supabase
        .from('listings')
        .select(`
            id,
            title,
            description,
            price,
            image_url,
            website,
            created_at,
            status,
            industry_id,
            industries!left (
                id,
                name
            )
        `)
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false })

    if (error) {
        console.error('Error fetching listings:', error)
        return <div>Error loading listings</div>
    }

    // Transform the data to match the expected interface
    const transformedListings = listings?.map(listing => ({
        ...listing,
        industries: Array.isArray(listing.industries) && listing.industries.length > 0
            ? listing.industries[0]
            : null
    })) || []

    return (
        <main className="min-h-screen py-8 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <MyListingsClient initialListings={transformedListings} />
            </div>
        </main>
    )
}
