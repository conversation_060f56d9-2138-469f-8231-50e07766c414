import { createClient } from '@/utils/supabase/server'
import { redirect } from 'next/navigation'
import EditListingForm from './EditListingForm'
import { Edit3, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { ViewModeProvider } from '@/contexts/ViewModeContext'

type PageProps = {
    params: Promise<{ id: string }>
}

export default async function EditListingPage({ params }: PageProps) {
    const resolvedParams = await params
    const supabase = await createClient()

    // Get current user to verify ownership
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
        redirect('/auth/login')
    }

    const { data: listing, error } = await supabase
        .from('listings')
        .select(`
            *,
            industries (
                id,
                name
            ),
            sub_industries (
                id,
                name
            ),
            listing_details (
                *
            )
        `)
        .eq('id', resolvedParams.id)
        .eq('user_id', user.id) // Ensure user owns this listing
        .single()

    if (error || !listing) {
        redirect('/account/my-listings')
    }

    // Fetch anonymized details
    const { data: anonymizedDetails } = await supabase
        .from('listing_anonymized_details')
        .select('*')
        .eq('listing_id', resolvedParams.id)
        .single()

    return (
        <ViewModeProvider>
            <main className="min-h-screen bg-gray-50 py-8">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Header Card */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <div className="p-2 bg-blue-50 rounded-lg">
                                    <Edit3 className="w-5 h-5 text-blue-600" />
                                </div>
                                <div>
                                    <h1 className="text-2xl font-semibold text-gray-900">Edit Listing</h1>
                                    <p className="text-gray-600 text-sm mt-1">
                                        Update your business listing information
                                    </p>
                                </div>
                            </div>
                            <Link
                                href="/account/my-listings"
                                className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium group"
                            >
                                <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
                                Back to Listings
                            </Link>
                        </div>
                    </div>

                    {/* Edit Form Card */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                        <EditListingForm
                            initialListing={listing}
                            initialAnonymizedDetails={anonymizedDetails}
                        />
                    </div>
                </div>
            </main>
        </ViewModeProvider>
    )
}