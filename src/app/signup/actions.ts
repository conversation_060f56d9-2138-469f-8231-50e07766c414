'use server'

import { revalidatePath } from 'next/cache'
import { createClient } from '@/utils/supabase/server'
import type { UserRole } from '@/types/supabase'

export async function signup(formData: FormData): Promise<{ error?: string; success?: string }> {
    const supabase = await createClient()

    const data = {
        email: formData.get('email') as string,
        password: formData.get('password') as string,
        password_confirmation: formData.get('password_confirmation') as string,
        user_role: formData.get('user_role') as UserRole,
    }

    // Validate passwords match
    if (data.password !== data.password_confirmation) {
        return { error: 'Passwords do not match' }
    }

    // Validate password strength
    if (data.password.length < 8) {
        return { error: 'Password must be at least 8 characters' }
    }

    // Validate user role is provided
    if (!data.user_role || !['seller', 'buyer', 'seller_buyer'].includes(data.user_role)) {
        return { error: 'Please select how you plan to use the platform' }
    }

    const { data: authData, error: signUpError } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
            emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
        }
    })

    if (signUpError) {
        // Handle common auth errors with user-friendly messages
        if (signUpError.message.includes('already registered')) {
            return { error: 'An account with this email already exists. Please try logging in instead.' }
        }
        return { error: signUpError.message }
    }

    if (authData.user) {
        // Check if profile already exists
        const { data: existingProfile } = await supabase
            .from('profiles')
            .select('user_id')
            .eq('user_id', authData.user.id)
            .single()

        // Only create profile if it doesn't exist
        if (!existingProfile) {
            const { error: profileError } = await supabase
                .from('profiles')
                .insert([
                    {
                        user_id: authData.user.id,
                        email: authData.user.email,
                        user_role: data.user_role,
                    }
                ])

            if (profileError) {
                console.error('Error creating profile:', profileError)

                // Handle specific constraint violation gracefully
                if (profileError.code === '23505') {
                    // Profile already exists, which is fine - user probably already signed up
                    console.log('Profile already exists for user:', authData.user.id)
                } else {
                    return { error: 'Failed to create user profile. Please try again.' }
                }
            }
        }
    }

    revalidatePath('/', 'layout')

    // Return success message
    return { success: 'Please check your email to verify your account' }
} 