/* eslint-disable */
declare global {
    var enigmaUsage: Record<string, number>;
}
/* eslint-enable */

import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    // Get the current date string for daily limits
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

    // Get limit from environment or use default
    const dailyLimit = parseInt(process.env.ENIGMA_DAILY_LIMIT || '50', 10);

    // Check current usage - in production you'd store this in Redis or a database
    // This is a simplified example using global variables (not recommended for production)
    global.enigmaUsage = global.enigmaUsage || {};
    global.enigmaUsage[today] = global.enigmaUsage[today] || 0;

    // Check if over limit
    if (global.enigmaUsage[today] >= dailyLimit) {
        console.warn(`Enigma API daily limit (${dailyLimit}) reached for ${today}`);
        return NextResponse.json({
            error: 'Daily API limit reached. Please try again tomorrow.',
            is_rate_limited: true
        }, { status: 429 });
    }

    // Increment usage counter before making API call
    global.enigmaUsage[today]++;

    try {
        if (!process.env.ENIGMA_API_KEY) {
            return NextResponse.json({
                error: 'Enigma API key is not configured'
            }, { status: 500 });
        }

        const body = await request.json();
        const { businessName, website, postalCode } = body;

        if (!businessName && !website) {
            return NextResponse.json({
                error: 'Either business name or website is required'
            }, { status: 400 });
        }

        // Sanitize business name by removing special characters
        const sanitizedName = businessName?.replace(/[']/g, '');
        console.log('Attempting to match business:', { originalName: businessName, sanitizedName, website });

        // Step 1: Match the business
        const url = new URL('https://api.enigma.com/businesses/match');
        url.searchParams.append('business_entity_type', 'business_location');
        url.searchParams.append('match_threshold', '0.2');
        url.searchParams.append('show_non_matches', '1');
        url.searchParams.append('prioritization', 'MTX');

        const matchResponse = await fetch(url.toString(), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': process.env.ENIGMA_API_KEY
            },
            body: JSON.stringify({
                name: sanitizedName,
                website: website,
                address: {
                    postal_code: postalCode || "18901"
                }
            })
        });

        const matchText = await matchResponse.text();
        console.log('Match response:', matchResponse.status, matchText);

        if (!matchResponse.ok) {
            return NextResponse.json({
                error: `Failed to match business: ${matchResponse.status} ${matchText}`
            }, { status: matchResponse.status });
        }

        const matchData = JSON.parse(matchText);

        if (!matchData.businesses?.[0]?.business_enigma_id) {
            return NextResponse.json({
                error: 'No matching business found'
            }, { status: 404 });
        }

        // Step 2: Get business details - REQUEST MINIMAL DATA
        const businessId = matchData.businesses[0].business_enigma_id;
        console.log('Found business ID:', businessId);

        // OPTIMIZATION: Request only card_revenue and card_revenue_growth, which is what we need
        // Using the original URL format but with fewer attributes
        const detailsResponse = await fetch(
            `https://api.enigma.com/businesses/${businessId}?attrs=card_revenue,card_revenue_growth`,
            {
                method: 'GET',
                headers: {
                    'x-api-key': process.env.ENIGMA_API_KEY,
                    'Accept': 'application/json'
                }
            }
        );

        const detailsText = await detailsResponse.text();
        console.log('Details response:', detailsResponse.status, detailsText);

        if (!detailsResponse.ok) {
            return NextResponse.json({
                error: `Failed to get business details: ${detailsResponse.status} ${detailsText}`
            }, { status: detailsResponse.status });
        }

        const detailsData = JSON.parse(detailsText);

        // Extract only the specific data points needed
        const monthlyRevenue = detailsData.card_revenue?.[0]?.["12m"]?.average_monthly_amount;
        const growthRate = detailsData.card_revenue_growth?.[0]?.["12m"]?.rate;

        // Return simplified response with just what's needed
        return NextResponse.json({
            monthly_revenue: monthlyRevenue,
            annual_growth_rate: growthRate,
            // Include the original data for backward compatibility during transition
            card_revenue: detailsData.card_revenue,
            growth_rate: detailsData.card_revenue_growth
        });
    } catch (error: unknown) {
        console.error('Full error details:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return NextResponse.json({
            error: errorMessage,
            details: String(error)
        }, { status: 500 });
    }
} 