import { createClient } from '@/utils/supabase/server'
import { NextResponse } from 'next/server'

export async function POST() {
    try {
        console.log('🚀 Triggering match notifications via direct HTTP call...')

        // Get the function URL from environment
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
        const functionUrl = `${supabaseUrl}/functions/v1/send-match-notifications`
        const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

        console.log('📡 Calling function at:', functionUrl)
        console.log('🔑 Service key available:', serviceKey ? 'Yes' : 'No')

        if (!serviceKey) {
            console.error('❌ SUPABASE_SERVICE_ROLE_KEY not found in environment')
            return NextResponse.json({
                success: false,
                error: 'Missing service role key',
                details: 'SUPABASE_SERVICE_ROLE_KEY environment variable not set'
            }, { status: 500 })
        }

        const response = await fetch(functionUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${serviceKey}`,
                'Content-Type': 'application/json',
                'apikey': serviceKey
            },
            body: JSON.stringify({})
        })

        console.log(`📬 Function response status: ${response.status}`)

        if (!response.ok) {
            const errorText = await response.text()
            console.error(`❌ Function call failed: ${response.status} - ${errorText}`)

            return NextResponse.json({
                success: false,
                error: 'Failed to trigger notifications',
                status: response.status,
                details: errorText
            }, { status: 500 })
        }

        const result = await response.json()
        console.log('✅ Function call successful:', result)

        return NextResponse.json(result)

    } catch (error) {
        console.error('❌ Error triggering notifications:', error)
        return NextResponse.json({
            success: false,
            error: 'Internal server error',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 })
    }
}

export async function GET() {
    try {
        const supabase = await createClient()

        // Get the current user session if available
        const { data: { session } } = await supabase.auth.getSession()

        // If no session, still show some basic info for testing
        if (!session) {
            // Get basic notification counts without user filter for testing
            const { data: notificationsCount, error } = await supabase
                .from('match_notifications')
                .select('id, email_sent')
                .limit(100)

            if (error) {
                console.error('Error fetching notification counts:', error)
                return NextResponse.json({ error: error.message }, { status: 500 })
            }

            const pending = notificationsCount.filter(n => !n.email_sent).length
            const sent = notificationsCount.filter(n => n.email_sent).length

            // Also get recent messages for testing
            const { data: recentMessages } = await supabase
                .from('messages')
                .select('id, created_at, content, sender_id, recipient_id')
                .order('created_at', { ascending: false })
                .limit(5)

            return NextResponse.json({
                message: 'Not authenticated, showing basic stats',
                totalNotifications: notificationsCount.length,
                pendingNotifications: pending,
                sentNotifications: sent,
                recentMessages: recentMessages?.length || 0
            })
        }

        // Get notifications for the current user
        const { data: notifications, error } = await supabase
            .from('match_notifications')
            .select(`
                *,
                saved_matches (name),
                listings (title, price)
            `)
            .eq('user_id', session.user.id)
            .order('sent_at', { ascending: false })
            .limit(10)

        if (error) {
            console.error('Error fetching notifications:', error)
            return NextResponse.json({ error: error.message }, { status: 500 })
        }

        // Also get recent messages for the user
        const { data: userMessages } = await supabase
            .from('messages')
            .select('id, created_at, content, listing_id')
            .or(`sender_id.eq.${session.user.id},recipient_id.eq.${session.user.id}`)
            .order('created_at', { ascending: false })
            .limit(5)

        return NextResponse.json({
            notifications,
            recentMessages: userMessages || []
        })

    } catch (error) {
        console.error('Error fetching notifications:', error)
        return NextResponse.json({
            error: 'Internal Server Error',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 })
    }
} 