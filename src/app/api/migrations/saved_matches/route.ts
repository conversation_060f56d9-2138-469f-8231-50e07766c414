import { createClient } from '@/utils/supabase/server'
import { NextResponse } from 'next/server'

export async function POST() {
    try {
        const supabase = await createClient()

        // Create saved_matches table
        const { error } = await supabase.rpc('create_saved_matches_table')

        if (error) {
            console.error('Error creating saved_matches table:', error)
            return NextResponse.json({ error: error.message }, { status: 500 })
        }

        return NextResponse.json({ success: true }, { status: 200 })
    } catch (error) {
        console.error('Error in migration:', error)
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
    }
} 