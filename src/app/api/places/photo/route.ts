import { NextResponse } from 'next/server';

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url);
        const reference = searchParams.get('reference');
        const maxwidth = searchParams.get('maxwidth') || '800';

        if (!reference) {
            return NextResponse.json(
                { error: 'Photo reference is required' },
                { status: 400 }
            );
        }

        const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

        if (!apiKey) {
            return NextResponse.json(
                { error: 'Google API key not configured' },
                { status: 500 }
            );
        }

        // Call Google Places Photo API
        const photoUrl = new URL('https://maps.googleapis.com/maps/api/place/photo');
        photoUrl.searchParams.append('photoreference', reference);
        photoUrl.searchParams.append('maxwidth', maxwidth);
        photoUrl.searchParams.append('key', apiKey);

        const response = await fetch(photoUrl.toString());

        if (!response.ok) {
            throw new Error(`Google API error: ${response.status}`);
        }

        // Get the image data
        const imageData = await response.arrayBuffer();

        // Return the image with the correct content type
        return new NextResponse(imageData, {
            headers: {
                'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
                'Cache-Control': 'public, max-age=86400' // Cache for 24 hours
            }
        });
    } catch (error) {
        console.error('Error fetching photo:', error);
        return NextResponse.json(
            { error: 'Failed to fetch photo' },
            { status: 500 }
        );
    }
} 