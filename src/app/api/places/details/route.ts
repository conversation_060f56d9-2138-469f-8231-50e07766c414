import { NextResponse } from 'next/server';

interface AddressComponents {
    locality?: string;
    state?: string;
    postalCode?: string;
    streetNumber?: string;
    route?: string;
    streetAddress?: string;
    [key: string]: string | undefined;
}

export async function POST(request: Request) {
    try {
        const { placeId } = await request.json();

        if (!placeId) {
            return NextResponse.json(
                { error: 'Place ID is required' },
                { status: 400 }
            );
        }

        // Use the existing Google Maps API key
        const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

        if (!apiKey) {
            return NextResponse.json(
                { error: 'Google API key not configured' },
                { status: 500 }
            );
        }

        // Call Google Places Details API
        const detailsUrl = new URL('https://maps.googleapis.com/maps/api/place/details/json');
        detailsUrl.searchParams.append('place_id', placeId);
        detailsUrl.searchParams.append('fields', 'name,formatted_address,address_component,website,formatted_phone_number,opening_hours,types,photos,geometry');
        detailsUrl.searchParams.append('key', apiKey);
        console.log('Google Places Details API URL:', detailsUrl.toString());

        const response = await fetch(detailsUrl.toString());
        const responseText = await response.text();
        console.log('Google Places Details API Response Status:', response.status);
        console.log('Google Places Details API Response Text:', responseText);

        const data = JSON.parse(responseText);

        if (data.status !== 'OK') {
            throw new Error(`Google API error: ${data.status} - ${data.error_message || responseText}`);
        }

        const result = data.result;

        // Parse address components
        const addressComponents: AddressComponents = {};
        if (result.address_components) {
            for (const component of result.address_components) {
                if (component.types.includes('locality')) {
                    addressComponents.locality = component.long_name;
                } else if (component.types.includes('administrative_area_level_1')) {
                    addressComponents.state = component.short_name;
                } else if (component.types.includes('postal_code')) {
                    addressComponents.postalCode = component.long_name;
                } else if (component.types.includes('street_number')) {
                    addressComponents.streetNumber = component.long_name;
                } else if (component.types.includes('route')) {
                    addressComponents.route = component.long_name;
                }
            }

            // Combine street number and route for street address
            if (addressComponents.streetNumber && addressComponents.route) {
                addressComponents.streetAddress = `${addressComponents.streetNumber} ${addressComponents.route}`;
            }
        }

        // Get the first photo reference if available
        let photoReference = null;
        let photoUrl = null;

        if (result.photos && result.photos.length > 0) {
            photoReference = result.photos[0].photo_reference;
            // Create a URL to our own photo proxy endpoint
            photoUrl = `/api/places/photo?reference=${photoReference}&maxwidth=800`;
        }

        return NextResponse.json({
            name: result.name,
            formattedAddress: result.formatted_address,
            addressComponents,
            website: result.website,
            phoneNumber: result.formatted_phone_number,
            types: result.types,
            openingHours: result.opening_hours,
            photoReference,
            photoUrl,
            geometry: result.geometry
        });
    } catch (error) {
        console.error('Error fetching business details:', error);
        return NextResponse.json(
            { error: 'Failed to fetch business details' },
            { status: 500 }
        );
    }
} 