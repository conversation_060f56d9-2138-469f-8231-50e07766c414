import { NextResponse } from 'next/server';

export async function POST(request: Request) {
    try {
        const { query, postalCode } = await request.json();

        if (!query || !postalCode) {
            return NextResponse.json(
                { error: 'Business name and postal code are required' },
                { status: 400 }
            );
        }

        // Use the existing Google Maps API key
        const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

        if (!apiKey) {
            return NextResponse.json(
                { error: 'Google API key not configured' },
                { status: 500 }
            );
        }

        // Call Google Places API Text Search
        const searchUrl = new URL('https://maps.googleapis.com/maps/api/place/textsearch/json');
        searchUrl.searchParams.append('query', `${query} ${postalCode}`);
        searchUrl.searchParams.append('key', apiKey);
        console.log('Google Places Search API URL:', searchUrl.toString());

        const response = await fetch(searchUrl.toString());
        const responseText = await response.text();
        console.log('Google Places Search API Response Status:', response.status);
        console.log('Google Places Search API Response Text:', responseText);

        const data = JSON.parse(responseText);

        if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
            throw new Error(`Google API error: ${data.status} - ${data.error_message || responseText}`);
        }

        // Transform the results
        interface GooglePlaceResult {
            name: string;
            formatted_address: string;
            business_status: string;
            place_id: string;
        }

        const results = (data.results || []).map((place: GooglePlaceResult) => ({
            name: place.name,
            formattedAddress: place.formatted_address,
            businessStatus: place.business_status,
            placeId: place.place_id,
        }));

        return NextResponse.json({ results });
    } catch (error) {
        console.error('Error searching for business:', error);
        return NextResponse.json(
            { error: 'Failed to search for business' },
            { status: 500 }
        );
    }
}
