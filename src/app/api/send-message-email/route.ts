import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

const RESEND_API_KEY = process.env.RESEND_API_KEY;

export async function POST(request: NextRequest) {
  try {
    console.log('🔥 EMAIL API CALLED - Message notification request received');

    if (!RESEND_API_KEY) {
      console.error('❌ RESEND_API_KEY environment variable not set');
      return NextResponse.json({ error: 'RESEND_API_KEY not configured' }, { status: 500 });
    }

    const body = await request.json();
    console.log('📦 Request body:', JSON.stringify(body, null, 2));

    const messageData = body.record;

    // Skip if no recipient_id (prevents notifications for system messages)
    if (!messageData.recipient_id) {
      console.log('⏭️ Skipping - no recipient_id');
      return NextResponse.json({ success: true, skipped: 'no_recipient' });
    }

    console.log('👥 Processing message for recipient:', messageData.recipient_id);

    const supabase = await createClient();

    // Get recipient profile and check email preferences
    const { data: recipient, error: recipientError } = await supabase
      .from('profiles')
      .select('email, first_name, email_notifications')
      .eq('user_id', messageData.recipient_id)
      .single();

    console.log('👤 Recipient query result:', { recipient, recipientError });

    if (recipientError || !recipient) {
      console.error('❌ Failed to fetch recipient profile:', recipientError);
      return NextResponse.json({ error: 'Failed to fetch recipient profile' }, { status: 400 });
    }

    // Check if user has email notifications disabled
    if (recipient.email_notifications === false) {
      console.log('🔕 User has email notifications disabled');
      return NextResponse.json({ success: true, skipped: 'notifications_disabled' });
    }

    // Get sender profile
    const { data: sender } = await supabase
      .from('profiles')
      .select('first_name, last_name')
      .eq('user_id', messageData.sender_id)
      .single();

    console.log('👤 Sender profile:', sender);

    // Get listing details
    const { data: listing } = await supabase
      .from('listings')
      .select('title, price')
      .eq('id', messageData.listing_id)
      .single();

    console.log('🏠 Listing details:', listing);

    // Create email content
    const senderName = sender?.first_name && sender?.last_name
      ? `${sender.first_name} ${sender.last_name}`
      : sender?.first_name || 'Someone';

    const listingTitle = listing?.title || 'a listing';
    const listingPrice = listing?.price ? `$${listing.price.toLocaleString()}` : '';

    // For testing purposes, send to your email
    const testEmail = '<EMAIL>';

    console.log(`📧 Sending email to test address: ${testEmail} (original: ${recipient.email})`);

    console.log('📧 About to send email to Resend API...');

    // Send email using Resend
    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        from: 'BuySell Messages <<EMAIL>>',
        to: testEmail, // Send to test email for debugging
        subject: `💬 New message from ${senderName} about ${listingTitle}`,
        html: `
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>New Message</title>
            </head>
            <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #111827; background-color: #f9fafb;">
              
              <!-- Main Container -->
              <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
                
                <!-- Header Section -->
                <div style="background: #111827; padding: 40px 32px; text-align: center; border-radius: 0;">
                  <h1 style="margin: 0; font-size: 32px; font-weight: 700; color: #ffffff; letter-spacing: -0.025em;">New Message</h1>
                  <p style="margin: 12px 0 0 0; color: #d1d5db; font-size: 16px; font-weight: 400;">You have a new message about your listing</p>
                </div>
                
                <!-- Content Section -->
                <div style="padding: 32px;">
                  <h2 style="color: #111827; margin: 0 0 16px 0; font-size: 24px; font-weight: 600;">Hello ${recipient.first_name || 'there'}!</h2>
                  
                  <p style="margin: 0 0 24px 0; color: #374151; font-size: 16px; line-height: 1.625;">You've received a new message from <strong>${senderName}</strong> regarding your listing:</p>
                  
                  <!-- Listing Card -->
                  <div style="background-color: #ffffff; border-radius: 12px; padding: 24px; margin: 24px 0; border: 1px solid #e5e7eb; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);">
                    <h3 style="margin: 0 0 16px 0; color: #111827; font-size: 20px; font-weight: 600;">${listingTitle}</h3>
                    ${listingPrice ? `<p style="margin: 0 0 16px 0; color: #059669; font-size: 18px; font-weight: 600;">${listingPrice}</p>` : ''}
                  </div>

                  <!-- Message Content -->
                  <div style="background-color: #f3f4f6; border-radius: 12px; padding: 24px; margin: 24px 0; border-left: 4px solid #3b82f6;">
                    <h4 style="margin: 0 0 12px 0; color: #374151; font-size: 16px; font-weight: 600;">Message from ${senderName}:</h4>
                    <p style="margin: 0; color: #111827; font-size: 15px; line-height: 1.6; white-space: pre-wrap;">${messageData.content}</p>
                  </div>

                  <!-- CTA Button -->
                  <div style="text-align: center; margin: 32px 0;">
                    <a href="https://app.evermark.ai/account/inbox" 
                       style="display: inline-block; background-color: #111827; color: #ffffff; padding: 14px 28px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: background-color 0.2s;">
                      Reply to Message →
                    </a>
                  </div>

                  <!-- Security Note -->
                  <div style="background-color: #fef3c7; border-radius: 8px; padding: 16px; margin: 24px 0; border-left: 4px solid #f59e0b;">
                    <p style="margin: 0; color: #92400e; font-size: 14px;">
                      <strong>🛡️ Security Reminder:</strong> Always communicate through our platform for safety. Be cautious of requests for personal information or external payments.
                    </p>
                  </div>

                  <!-- Footer -->
                  <div style="margin-top: 40px; padding-top: 24px; border-top: 1px solid #e5e7eb;">
                    <p style="color: #6b7280; font-size: 14px; text-align: center; margin: 0 0 16px 0; line-height: 1.5;">
                      You received this email because you have an active listing on BuySell.<br>
                      <a href="https://app.evermark.ai/profile" style="color: #2563eb; text-decoration: none;">Manage your notification preferences</a>
                    </p>

                    <p style="color: #6b7280; font-size: 13px; text-align: center; margin: 0;">
                      Best regards,<br>
                      <strong style="color: #111827;">The BuySell Team</strong>
                    </p>
                  </div>
                </div>
              </div>
            </body>
          </html>
        `
      })
    });

    console.log('📧 Resend API response status:', emailResponse.status);
    console.log('📧 Resend API response headers:', Object.fromEntries(emailResponse.headers.entries()));

    if (!emailResponse.ok) {
      const emailError = await emailResponse.json();
      console.error('❌ Failed to send email - Resend API error:', emailError);
      console.error('❌ Response status:', emailResponse.status);
      console.error('❌ Response status text:', emailResponse.statusText);
      return NextResponse.json({
        error: 'Failed to send email',
        details: emailError,
        status: emailResponse.status
      }, { status: 500 });
    }

    const emailResult = await emailResponse.json();
    console.log('✅ Email sent successfully:', emailResult);

    return NextResponse.json({
      success: true,
      email_id: emailResult.id,
      recipient_email: testEmail, // Show test email in response
      original_recipient: recipient.email
    });

  } catch (error) {
    console.error('❌ Message email notification error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 