import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST() {
    try {
        console.log('🧪 Creating test listing...')

        // Use service role key to bypass RLS for testing
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
        const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

        const supabase = createClient(supabaseUrl, serviceKey, {
            auth: {
                autoRefreshToken: false,
                persistSession: false
            }
        })

        // Create a test listing
        const testListing = {
            user_id: 'c1347121-6e59-463e-85ee-352b04169f9a', // Valid user from profiles table
            title: 'Test Pizza Restaurant - Match Trigger',
            description: 'Established pizza restaurant in prime location. Great for testing email notifications.',
            price: 250000,
            website: 'https://test-pizza.com',
            industry_id: '6775b3d1-a7f4-42ad-a7cc-325b3cf8a119', // Restaurant industry
            sub_industry_id: '44d6c14f-ce71-479a-aed4-ff6b54898c40', // Pizza Restaurants
            utm_source: 'test-system'
        }

        const { data: listing, error: listingError } = await supabase
            .from('listings')
            .insert(testListing)
            .select()
            .single()

        if (listingError) {
            console.error('❌ Error creating test listing:', listingError)
            return NextResponse.json({
                success: false,
                error: 'Failed to create test listing',
                details: listingError.message
            }, { status: 500 })
        }

        console.log('✅ Test listing created:', listing.id)

        // Also create listing details
        const testListingDetails = {
            listing_id: listing.id,
            annual_revenue_ttm_min: 180000,
            annual_revenue_ttm_max: 220000,
            annual_net_profit_ttm_min: 45000,
            annual_net_profit_ttm_max: 55000,
            year_established: 2018,
            team_size: 8,
            location: 'San Francisco, California',
            city: 'San Francisco',
            state_id: 'ca'
        }

        const { error: detailsError } = await supabase
            .from('listing_details')
            .insert(testListingDetails)

        if (detailsError) {
            console.error('⚠️ Error creating listing details:', detailsError)
            // Continue anyway, the main listing was created
        } else {
            console.log('✅ Test listing details created')
        }

        return NextResponse.json({
            success: true,
            message: 'Test listing created successfully',
            listingId: listing.id,
            title: listing.title,
            price: listing.price,
            industry: 'Pizza/Italian Food',
            note: 'Database trigger should create notifications automatically. Check notifications in 5 minutes.'
        })

    } catch (error) {
        console.error('❌ Error in test-listing endpoint:', error)
        return NextResponse.json({
            success: false,
            error: 'Internal server error'
        }, { status: 500 })
    }
} 