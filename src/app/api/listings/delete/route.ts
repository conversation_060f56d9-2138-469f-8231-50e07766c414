import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { createClient as createClientSide } from '@/utils/supabase/server'

export async function DELETE(request: NextRequest) {
    try {
        const { listingId } = await request.json()

        if (!listingId) {
            return NextResponse.json(
                { error: 'Listing ID is required' },
                { status: 400 }
            )
        }

        // Verify user is authenticated and owns the listing
        const supabaseClient = await createClientSide()
        const { data: { user }, error: authError } = await supabaseClient.auth.getUser()

        if (authError || !user) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            )
        }

        // Verify user owns this listing
        const { data: listing, error: listingError } = await supabaseClient
            .from('listings')
            .select('user_id')
            .eq('id', listingId)
            .single()

        if (listingError || !listing) {
            return NextResponse.json(
                { error: 'Listing not found' },
                { status: 404 }
            )
        }

        if (listing.user_id !== user.id) {
            return NextResponse.json(
                { error: 'You can only delete your own listings' },
                { status: 403 }
            )
        }

        // Use service role key to bypass RLS for deletion
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
        const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

        if (!serviceKey) {
            return NextResponse.json(
                { error: 'Server configuration error' },
                { status: 500 }
            )
        }

        const supabaseAdmin = createClient(supabaseUrl, serviceKey, {
            auth: {
                autoRefreshToken: false,
                persistSession: false
            }
        })

        // Delete in the correct order to avoid foreign key constraint violations
        const tablesToClean = [
            'saved_listings',      // Must be first - has foreign key to listings
            'messages',
            'conversations',
            'data_room_access',
            'data_room_files',
            'listing_anonymized_details',
            'listing_socials',
            'listing_details'
        ]

        // Delete all related records first
        for (const tableName of tablesToClean) {
            const { error } = await supabaseAdmin
                .from(tableName)
                .delete()
                .eq('listing_id', listingId)

            if (error) {
                console.log(`Warning: Could not delete from ${tableName}:`, error.message)
                // Continue anyway - some tables might not have records
            }
        }

        // Finally delete the listing itself
        const { error: finalError } = await supabaseAdmin
            .from('listings')
            .delete()
            .eq('id', listingId)

        if (finalError) {
            console.error('Failed to delete listing:', finalError)
            return NextResponse.json(
                { error: `Failed to delete listing: ${finalError.message}` },
                { status: 500 }
            )
        }

        return NextResponse.json({
            success: true,
            message: 'Listing deleted successfully'
        })

    } catch (error) {
        console.error('Delete listing error:', error)
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        )
    }
} 