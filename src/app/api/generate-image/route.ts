import { NextResponse } from 'next/server';
import { OpenAI } from 'openai';

// Define interface for Lummi API response item
interface LummiImageItem {
    url: string;
    id: string;
    title?: string;
    // Add other properties as needed
}

export async function POST(request: Request) {
    try {
        if (!process.env.OPENAI_API_KEY || !process.env.LUMMI_API_KEY) {
            return NextResponse.json(
                { error: 'API keys not configured' },
                { status: 500 }
            );
        }

        const { description, count = 5 } = await request.json();

        if (!description) {
            return NextResponse.json(
                { error: 'Description is required' },
                { status: 400 }
            );
        }

        // First, use OpenAI to extract key business terms
        const openai = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY,
        });

        const completion = await openai.chat.completions.create({
            model: "gpt-3.5-turbo",
            messages: [
                {
                    role: "system",
                    content: "Extract 3-4 key business-related search terms from the description. Focus on: 1) Business type/industry 2) Physical location type 3) Key selling points. Format as comma-separated list. Example: 'modern restaurant interior, italian cuisine, outdoor seating'"
                },
                {
                    role: "user",
                    content: description
                }
            ],
            temperature: 0.3,
            max_tokens: 50,
        });

        const searchTerms = completion.choices[0].message.content?.trim();
        console.log('Generated search terms:', searchTerms);

        // Then use these terms to search Lummi
        const response = await fetch(`https://api.lummi.ai/v1/images/search?query=${encodeURIComponent(searchTerms || '')}&imageType=photo&limit=${count}`, {
            headers: {
                'Authorization': `Bearer ${process.env.LUMMI_API_KEY}`,
                'Content-Type': 'application/json',
            },
        });

        const data = await response.json();

        if (!response.ok) {
            console.error('Lummi API error:', data);
            return NextResponse.json(
                { error: data.error || 'Failed to generate image' },
                { status: response.status }
            );
        }

        // Get multiple image URLs from the results
        const imageUrls = data.data?.map((item: LummiImageItem) => item.url).filter(Boolean) || [];

        if (imageUrls.length === 0) {
            return NextResponse.json(
                { error: 'No suitable images found' },
                { status: 404 }
            );
        }

        // Limit to exactly the requested count
        const limitedImageUrls = imageUrls.slice(0, count);

        // Return both formats for backward compatibility
        return NextResponse.json({
            imageUrl: limitedImageUrls[0], // For backward compatibility
            imageUrls: limitedImageUrls,
            searchTerms
        });

    } catch (error) {
        console.error('Error in generate-image API:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
            { status: 500 }
        );
    }
} 