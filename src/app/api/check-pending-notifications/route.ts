import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET() {
    try {
        const supabase = await createClient();

        const { data: notifications, error } = await supabase
            .from('match_notifications')
            .select('*')
            .eq('email_sent', false);

        if (error) {
            console.error('Error fetching notifications:', error);
            return NextResponse.json({ error: 'Failed to fetch notifications' }, { status: 500 });
        }

        return NextResponse.json({
            success: true,
            pendingCount: notifications?.length || 0,
            notifications: notifications || []
        });

    } catch (error) {
        console.error('Error in check-pending-notifications:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
} 