import { OpenAI } from 'openai';
import { NextResponse } from 'next/server';

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
});

export async function POST(req: Request) {
    try {
        const { description } = await req.json();

        const prompt = `Please rewrite the following business description to remove any identifying information (names, locations, specific details) while maintaining the essence of the business. Make it generic but informative:

${description}

Guidelines:
- Remove business names, personal names, and specific locations
- Keep industry-specific details and business model information
- Maintain the general scope and scale of the business
- Keep the tone professional and informative
- Focus on value propositions and business strengths`;

        const completion = await openai.chat.completions.create({
            model: "gpt-4",
            messages: [
                {
                    role: "system",
                    content: "You are a professional business analyst who specializes in creating anonymous business listings and descriptions."
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.7,
            max_tokens: 500
        });

        return NextResponse.json({ description: completion.choices[0].message.content });
    } catch (error) {
        console.error('Error:', error);
        return NextResponse.json({ error: 'Failed to generate anonymized description' }, { status: 500 });
    }
} 