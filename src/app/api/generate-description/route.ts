import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: Request) {
    try {
        // Check API key first
        if (!process.env.OPENAI_API_KEY) {
            return NextResponse.json(
                { error: 'OpenAI API key is not configured' },
                { status: 500 }
            );
        }

        const { url } = await request.json();

        if (!url) {
            return NextResponse.json(
                { error: 'URL is required' },
                { status: 400 }
            );
        }

        // Add error handling for fetch
        const fetchResponse = await fetch(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        if (!fetchResponse.ok) {
            console.error(`Failed to fetch URL: ${fetchResponse.status} ${fetchResponse.statusText}`);
            return NextResponse.json(
                { error: `Failed to fetch URL: ${fetchResponse.statusText}` },
                { status: 422 }
            );
        }

        const html = await fetchResponse.text();

        // Basic text extraction from HTML
        const textContent = html
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
            .replace(/<[^>]+>/g, ' ')
            .replace(/\s+/g, ' ')
            .trim()
            .slice(0, 1500);

        if (!textContent) {
            return NextResponse.json(
                { error: 'No content could be extracted from the URL' },
                { status: 422 }
            );
        }

        const completion = await openai.chat.completions.create({
            model: "gpt-3.5-turbo", // Using 3.5-turbo for cost efficiency, change to "gpt-4" if needed
            messages: [
                {
                    role: "system",
                    content: "You are a helpful assistant that generates concise business descriptions."
                },
                {
                    role: "user",
                    content: `Based on this website content, generate a natural, concise business description in exactly 100 words. Focus on the main business purpose, value proposition, and any unique selling points. Content: ${textContent}`
                }
            ],
            max_tokens: 200,
            temperature: 0.7,
        });

        const generatedDescription = completion.choices[0].message.content?.trim();

        if (!generatedDescription) {
            return NextResponse.json(
                { error: 'Failed to generate description' },
                { status: 500 }
            );
        }

        return NextResponse.json({ description: generatedDescription });

    } catch (error) {
        console.error('API Error:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
            { status: 500 }
        );
    }
} 