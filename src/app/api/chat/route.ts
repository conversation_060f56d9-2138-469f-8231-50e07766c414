import OpenAI from "openai";
import { NextResponse } from "next/server";

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
});

export async function POST() {
    try {
        const completion = await openai.chat.completions.create({
            messages: [{ role: "system", content: "You are a helpful assistant." }],
            model: "gpt-4",
            temperature: 0.7,
        });

        return NextResponse.json({
            message: completion.choices[0].message.content
        });
    } catch (error: unknown) {
        console.error('Chat API error:', error);
        return NextResponse.json(
            { error: "Failed to fetch response" },
            { status: 500 }
        );
    }
} 