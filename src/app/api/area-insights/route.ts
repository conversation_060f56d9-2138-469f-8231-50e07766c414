import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import zipToCounty from '@/utils/zip-to-county.json';
import { CensusService } from '@/services/census.service';
import { WalkScoreService } from '@/services/walkscore.service';
import { EconomicCensusService } from '@/services/economic-census.service';

// Types
interface AreaInsightsData {
    city: string;
    state: string;
    population: number;
    isPopulationMock: boolean;
    medianIncome: number;
    isMedianIncomeMock: boolean;
    unemploymentRate: number;
    isUnemploymentRateMock: boolean;
    numBusinesses: number;
    isNumBusinessesMock: boolean;
    crimeRate: string;
    isCrimeStatsMock: boolean;
    schools: number;
    isSchoolCountMock: boolean;
    populationCAGR: number;
    isPopulationCAGRMock: boolean;
    medianAge: number;
    isMedianAgeMock: boolean;
    educationBachelorPlusPercent: number;
    isEducationBachelorPlusPercentMock: boolean;
}

// Input validation schema
const AreaInsightsRequestSchema = z.object({
    postalCode: z.string()
        .regex(/^\d{5}(-\d{4})?$/, 'Invalid postal code format. Must be 5 digits or 5+4 format.')
        .optional(),
    fipsCode: z.string()
        .regex(/^\d{5}$/, 'Invalid FIPS code format. Must be exactly 5 digits.')
        .optional(),
    naicsCode: z.string()
        .regex(/^\d{2,6}$/, 'Invalid NAICS code format. Must be 2-6 digits.')
        .optional(),
}).refine(
    (data) => data.postalCode || data.fipsCode,
    {
        message: "At least one of postalCode or fipsCode must be provided",
        path: ["postalCode", "fipsCode"],
    }
);

// Rate limiting helper (simple in-memory implementation)
const requestCounts = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10; // 10 requests per minute per IP

function checkRateLimit(ip: string): boolean {
    const now = Date.now();
    const record = requestCounts.get(ip);

    if (!record || now > record.resetTime) {
        requestCounts.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
        return true;
    }

    if (record.count >= RATE_LIMIT_MAX_REQUESTS) {
        return false;
    }

    record.count += 1;

    // Cleanup old entries periodically
    if (Math.random() < 0.01) { // 1% chance to cleanup
        const cutoff = now - RATE_LIMIT_WINDOW;
        for (const [key, value] of requestCounts.entries()) {
            if (value.resetTime < cutoff) {
                requestCounts.delete(key);
            }
        }
    }

    return true;
}

// Timeout wrapper function
function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
        promise,
        new Promise<T>((_, reject) =>
            setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs)
        ),
    ]);
}

// Helper function to map ZIP code to county and state FIPS using the local JSON
async function getCountyAndStateFipsFromZip(zip: string): Promise<{ countyFips: string, stateFips: string } | null> {
    const mapping = (zipToCounty as Record<string, { countyFips: string; stateFips: string }>)[zip];
    if (mapping) {
        return {
            countyFips: mapping.stateFips + mapping.countyFips, // Combine to full FIPS
            stateFips: mapping.stateFips
        };
    }
    console.warn(`ZIP ${zip} not found in local mapping.`);
    return null;
}

// Main API route implementation using services
async function getAreaInsightsDataInternal(
    postalCode: string | null | undefined,
    directFipsCode: string | null | undefined
): Promise<AreaInsightsData> {
    const apiKey = process.env.CENSUS_GOV_API!;

    // Determine FIPS for county-level data (prioritize directFipsCode)
    let countyDataSourceFips: { stateFips: string; countyFips: string } | null = null;

    if (directFipsCode && directFipsCode.length === 5) {
        countyDataSourceFips = {
            stateFips: directFipsCode.substring(0, 2),
            countyFips: directFipsCode.substring(2, 5),
        };
    } else if (postalCode) {
        const mapping = await getCountyAndStateFipsFromZip(postalCode);
        if (mapping) {
            countyDataSourceFips = {
                stateFips: mapping.stateFips,
                countyFips: mapping.countyFips.substring(2, 5), // Extract county part
            };
        }
    }

    // Use the new CensusService to get all data in parallel
    const censusData = await CensusService.getAllCensusData(
        countyDataSourceFips,
        postalCode || null,
        apiKey
    );

    return {
        city: 'Sample City',
        state: 'Sample State',
        ...censusData,
        // Mock data for fields not handled by CensusService yet
        crimeRate: ['Low', 'Moderate', 'High'][Math.floor(Math.random() * 3)],
        isCrimeStatsMock: true,
        schools: Math.floor(Math.random() * 100) + 50,
        isSchoolCountMock: true,
    };
}

export async function POST(request: NextRequest): Promise<NextResponse> {
    try {
        // Rate limiting check
        const ip = request.headers.get('x-forwarded-for') ||
            request.headers.get('x-real-ip') ||
            'unknown';

        if (!checkRateLimit(ip)) {
            return NextResponse.json(
                { error: 'Rate limit exceeded. Please try again later.' },
                { status: 429 }
            );
        }

        // Parse and validate request body with proper error handling
        let body: unknown;
        try {
            const rawBody = await request.text();
            if (!rawBody.trim()) {
                return NextResponse.json(
                    { error: 'Request body is required' },
                    { status: 400 }
                );
            }
            body = JSON.parse(rawBody);
        } catch {
            return NextResponse.json(
                { error: 'Invalid JSON in request body' },
                { status: 400 }
            );
        }

        // Enhanced validation using Zod schema
        const validationResult = AreaInsightsRequestSchema.safeParse(body);

        if (!validationResult.success) {
            return NextResponse.json(
                {
                    error: 'Invalid input parameters',
                    details: validationResult.error.format()
                },
                { status: 400 }
            );
        }

        const { postalCode, fipsCode, naicsCode } = validationResult.data;

        console.log('Area insights API called with validated inputs:', { postalCode, fipsCode, naicsCode });

        // Use services to fetch all data in parallel with improved error handling
        const results = await withTimeout(
            Promise.allSettled([
                // Area insights (Census data)
                getAreaInsightsDataInternal(postalCode, fipsCode),

                // Walkability data
                WalkScoreService.getWalkabilityData({
                    address: '',
                    city: 'Sample City',
                    state: 'Sample State',
                    postalCode: postalCode || '12345',
                    latitude: 40.7128,
                    longitude: -74.0060,
                }),

                // Similar companies data (only if both naicsCode and fipsCode provided)
                naicsCode && fipsCode ?
                    EconomicCensusService.getSimilarCompaniesData(fipsCode, naicsCode, process.env.CENSUS_GOV_API!) :
                    Promise.resolve({
                        numEstablishments: Math.floor(Math.random() * 100) + 50,
                        totalAnnualPayroll: Math.floor(Math.random() * 50000) + 25000,
                        totalEmployment: Math.floor(Math.random() * 1000) + 500,
                        averageAnnualWage: Math.floor(Math.random() * 30000) + 45000,
                        countyName: 'Sample County',
                        naicsDescription: 'Sample Industry',
                        numEstablishmentsPreviousYear: Math.floor(Math.random() * 80) + 40,
                        establishmentGrowthRate: parseFloat(((Math.random() * 20) - 5).toFixed(1)),
                        totalSalesReceipts: Math.floor(Math.random() * 10000000) + 5000000,
                        avgSalesPerEstablishment: Math.floor(Math.random() * 500000) + 200000,
                        isSalesDataMock: true,
                    })
            ]),
            15000
        );

        console.log('API results:', results);

        // Extract results with fallbacks to mock data
        const areaInsights = results[0].status === 'fulfilled' ?
            results[0].value :
            {
                city: 'Sample City',
                state: 'Sample State',
                population: Math.floor(Math.random() * 700000) + 300000,
                isPopulationMock: true,
                medianIncome: Math.floor(Math.random() * 50000) + 70000,
                isMedianIncomeMock: true,
                unemploymentRate: parseFloat((Math.random() * 5 + 2).toFixed(1)),
                isUnemploymentRateMock: true,
                numBusinesses: Math.floor(Math.random() * 10000) + 5000,
                isNumBusinessesMock: true,
                crimeRate: ['Low', 'Moderate', 'High'][Math.floor(Math.random() * 3)],
                isCrimeStatsMock: true,
                schools: Math.floor(Math.random() * 100) + 50,
                isSchoolCountMock: true,
                populationCAGR: parseFloat(((Math.random() * 4) - 1).toFixed(2)),
                isPopulationCAGRMock: true,
                medianAge: Math.floor(Math.random() * 10) + 35,
                isMedianAgeMock: true,
                educationBachelorPlusPercent: Math.floor(Math.random() * 30) + 20,
                isEducationBachelorPlusPercentMock: true,
            };

        const walkability = results[1].status === 'fulfilled' ?
            results[1].value :
            {
                walkScore: Math.floor(Math.random() * 40) + 60,
                isWalkScoreMock: true,
                walkDescription: "Somewhat Walkable (demo data)",
                transitScore: Math.floor(Math.random() * 50) + 40,
                isTransitScoreMock: true,
                transitDescription: "Some Transit (demo data)",
                bikeScore: Math.floor(Math.random() * 40) + 60,
                isBikeScoreMock: true,
                bikeDescription: "Bikeable (demo data)",
                wsLink: null,
            };

        const similarCompanies = results[2].status === 'fulfilled' ?
            results[2].value :
            {
                numEstablishments: Math.floor(Math.random() * 500) + 100,
                totalAnnualPayroll: Math.floor(Math.random() * 50000) + 10000,
                totalEmployment: Math.floor(Math.random() * 2000) + 500,
                averageAnnualWage: Math.floor(Math.random() * 30000) + 40000,
                countyName: "Demo County",
                naicsDescription: "Demo Industry",
                numEstablishmentsPreviousYear: Math.floor(Math.random() * 480) + 95,
                establishmentGrowthRate: parseFloat((Math.random() * 10 - 2).toFixed(1)),
                totalSalesReceipts: Math.floor(Math.random() * 1000000) + 500000,
                avgSalesPerEstablishment: Math.floor(Math.random() * 500000) + 100000,
                isSalesDataMock: true,
            };

        return NextResponse.json({
            areaInsights,
            walkability,
            similarCompanies,
        });
    } catch (error) {
        console.error('Error in area insights API:', error);

        // Log error details for monitoring
        if (error instanceof Error) {
            console.error('Error details:', {
                message: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString()
            });
        }

        // Return mock data as fallback with error indication
        return NextResponse.json({
            areaInsights: {
                city: 'Sample City',
                state: 'Sample State',
                population: Math.floor(Math.random() * 700000) + 300000,
                isPopulationMock: true,
                medianIncome: Math.floor(Math.random() * 50000) + 70000,
                isMedianIncomeMock: true,
                unemploymentRate: parseFloat((Math.random() * 5 + 2).toFixed(1)),
                isUnemploymentRateMock: true,
                numBusinesses: Math.floor(Math.random() * 10000) + 5000,
                isNumBusinessesMock: true,
                crimeRate: ['Low', 'Moderate', 'High'][Math.floor(Math.random() * 3)],
                isCrimeStatsMock: true,
                schools: Math.floor(Math.random() * 100) + 50,
                isSchoolCountMock: true,
                populationCAGR: parseFloat(((Math.random() * 4) - 1).toFixed(2)),
                isPopulationCAGRMock: true,
                medianAge: Math.floor(Math.random() * 10) + 35,
                isMedianAgeMock: true,
                educationBachelorPlusPercent: Math.floor(Math.random() * 30) + 20,
                isEducationBachelorPlusPercentMock: true,
            },
            walkability: {
                walkScore: Math.floor(Math.random() * 40) + 60,
                isWalkScoreMock: true,
                walkDescription: "Somewhat Walkable (demo data)",
                transitScore: Math.floor(Math.random() * 50) + 40,
                isTransitScoreMock: true,
                transitDescription: "Some Transit (demo data)",
                bikeScore: Math.floor(Math.random() * 40) + 60,
                isBikeScoreMock: true,
                bikeDescription: "Bikeable (demo data)",
                wsLink: null,
            },
            similarCompanies: {
                numEstablishments: Math.floor(Math.random() * 500) + 100,
                totalAnnualPayroll: Math.floor(Math.random() * 50000) + 10000,
                totalEmployment: Math.floor(Math.random() * 2000) + 500,
                averageAnnualWage: Math.floor(Math.random() * 30000) + 40000,
                countyName: "Demo County",
                naicsDescription: "Demo Industry",
                numEstablishmentsPreviousYear: Math.floor(Math.random() * 480) + 95,
                establishmentGrowthRate: parseFloat((Math.random() * 10 - 2).toFixed(1)),
                totalSalesReceipts: Math.floor(Math.random() * 1000000) + 500000,
                avgSalesPerEstablishment: Math.floor(Math.random() * 500000) + 100000,
                isSalesDataMock: true,
            },
            error: 'Failed to fetch real data, showing mock data instead'
        }, { status: 500 });
    }
} 