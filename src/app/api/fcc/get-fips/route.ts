import { createClient } from '@/utils/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    const { listing_id, latitude, longitude } = await request.json();

    if (!listing_id || typeof latitude !== 'number' || typeof longitude !== 'number') {
        return NextResponse.json({ error: 'Missing listing_id, latitude, or longitude' }, { status: 400 });
    }

    const supabase = await createClient();

    try {
        // Fetch FIPS code from FCC API
        const fccApiUrl = `https://geo.fcc.gov/api/census/block/find?latitude=${latitude}&longitude=${longitude}&format=json`;
        const fccResponse = await fetch(fccApiUrl);

        if (!fccResponse.ok) {
            const errorText = await fccResponse.text();
            console.error('FCC API error:', fccResponse.status, errorText);
            return NextResponse.json({ error: `FCC API request failed: ${fccResponse.status} ${errorText}` }, { status: fccResponse.status });
        }

        const fccData = await fccResponse.json();
        const rawFipsFromApi = fccData?.Block?.FIPS; // This is the full 15-digit FIPS

        if (!rawFipsFromApi || typeof rawFipsFromApi !== 'string' || rawFipsFromApi.length !== 15) {
            console.warn('Valid FIPS code (15 char string) not found in FCC API response for:', { latitude, longitude, fccData });
            return NextResponse.json({ message: 'Listing processed, but a valid 15-character FIPS code was not found in FCC response.', listing_id }, { status: 200 });
        }

        const fips_full = rawFipsFromApi;
        const fips_code = rawFipsFromApi.substring(0, 5);    // State (2) + County (3)
        const fips_tract = rawFipsFromApi.substring(5, 11);   // Tract (6)
        const fips_block = rawFipsFromApi.substring(11, 15); // Block (4)

        // Update listing_details with the FIPS code
        // We need to update where listing_id matches in the listing_details table.
        // Assuming 'listing_id' is the foreign key in 'listing_details' that links to 'listings' table.
        const { error: updateError } = await supabase
            .from('listing_details')
            .update({
                fips_full: fips_full,
                fips_code: fips_code,
                fips_tract: fips_tract,
                fips_block: fips_block
            })
            .eq('listing_id', listing_id); // Ensure this targets the correct record

        if (updateError) {
            console.error('Supabase update error:', updateError);
            return NextResponse.json({ error: 'Failed to update listing with FIPS codes', details: updateError.message }, { status: 500 });
        }

        return NextResponse.json({
            message: 'FIPS codes updated successfully',
            listing_id,
            fips_full,
            fips_code,
            fips_tract,
            fips_block
        }, { status: 200 });

    } catch (error) {
        let errorMessage = 'An unknown error occurred';
        if (error instanceof Error) {
            errorMessage = error.message;
        }
        console.error('Error processing FIPS code:', error);
        return NextResponse.json({ error: 'Internal server error', details: errorMessage }, { status: 500 });
    }
} 