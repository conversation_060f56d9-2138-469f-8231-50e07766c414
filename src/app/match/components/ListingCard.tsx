'use client'

import { IndustryIcon, Industry } from '@/config/industries'
import Link from 'next/link'
import Image from 'next/image'
import { MapPin, Calendar, TrendingUp } from 'lucide-react'

interface Listing {
    id: string
    title: string
    description: string | null
    price: number
    image_url: string | null
    slug: string | null
    industries: { id: string; name: string } | null
    sub_industries: { id: string; name: string; industry_id: string } | null
    listing_details: {
        year_established: number | null
        location: string | null
        annual_revenue_ttm_min: number | null
        annual_revenue_ttm_max: number | null
    } | null
}

export default function ListingCard({ listing }: { listing: Listing }) {
    // Format price to display as currency
    const formattedPrice = () => {
        // Make sure price is a valid number
        const price = typeof listing.price === 'number' && !isNaN(listing.price)
            ? listing.price
            : 0;

        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            maximumFractionDigits: 0
        }).format(price);
    }

    // Format revenue range for display
    const formatRevenue = () => {
        const min = listing.listing_details?.annual_revenue_ttm_min
        const max = listing.listing_details?.annual_revenue_ttm_max

        if (!min && !max) return 'Not specified'

        const formatValue = (val: number | null) => {
            if (val === null || isNaN(Number(val))) return '$0';

            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                notation: 'compact',
                maximumFractionDigits: 1
            }).format(Number(val));
        }

        if (min && max) return `${formatValue(min)} - ${formatValue(max)}`
        if (min) return `${formatValue(min)}+`
        if (max) return `Up to ${formatValue(max)}`

        return 'Not specified'
    }

    // Extract industry name for display
    const industryName = listing.industries?.name || 'General'
    const subIndustryName = listing.sub_industries?.name

    // Build the URL for the listing detail page
    const listingUrl = listing.slug
        ? `/listings/${listing.slug}`
        : `/listings/${listing.id}`

    return (
        <Link href={listingUrl}>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 overflow-hidden hover:shadow-md transition-all duration-200 group">
                {/* Listing Image */}
                <div className="relative h-40">
                    {listing.image_url ? (
                        <Image
                            src={listing.image_url}
                            alt={listing.title}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-200"
                        />
                    ) : (
                        <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                            <span className="text-gray-400 font-medium">No Image Available</span>
                        </div>
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>

                    {/* Industry Badge */}
                    <div className="absolute top-3 left-3">
                        <div className="flex items-center bg-white/90 backdrop-blur-sm text-gray-700 rounded-full px-2 py-1 text-xs font-medium">
                            <IndustryIcon
                                industry={listing.industries?.name?.toLowerCase().replace(/ & /g, '_').replace(/ /g, '_') as Industry}
                                size={12}
                                className="mr-1"
                            />
                            <span>{industryName}</span>
                        </div>
                    </div>

                    {/* Sub-Industry Badge */}
                    {subIndustryName && (
                        <div className="absolute top-12 left-3">
                            <div className="bg-blue-500/90 backdrop-blur-sm text-white rounded-full px-2 py-1 text-xs font-medium">
                                <span>{subIndustryName}</span>
                            </div>
                        </div>
                    )}
                </div>

                {/* Listing Content */}
                <div className="p-5">
                    <div className="flex justify-between items-start mb-3">
                        <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors flex-1 mr-3">
                            {listing.title}
                        </h3>
                        <span className="font-semibold text-gray-900 whitespace-nowrap">
                            {formattedPrice()}
                        </span>
                    </div>

                    {/* Description */}
                    {listing.description && (
                        <p className="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">
                            {listing.description}
                        </p>
                    )}

                    {/* Location */}
                    {listing.listing_details?.location && (
                        <div className="flex items-center text-sm text-gray-500 mb-4">
                            <MapPin className="w-4 h-4 mr-1" />
                            <span>{listing.listing_details.location}</span>
                        </div>
                    )}

                    {/* Details Grid */}
                    <div className="space-y-3 pt-4 border-t border-gray-100">
                        {/* Industry and Sub-Industry */}
                        <div className="flex items-center justify-between text-sm">
                            <div className="flex items-center text-gray-500">
                                <span>Industry</span>
                            </div>
                            <span className="font-medium text-gray-900">
                                {industryName}
                                {subIndustryName && (
                                    <span className="text-blue-600"> • {subIndustryName}</span>
                                )}
                            </span>
                        </div>

                        {listing.listing_details?.year_established && (
                            <div className="flex items-center justify-between text-sm">
                                <div className="flex items-center text-gray-500">
                                    <Calendar className="w-4 h-4 mr-1" />
                                    <span>Established</span>
                                </div>
                                <span className="font-medium text-gray-900">{listing.listing_details.year_established}</span>
                            </div>
                        )}

                        <div className="flex items-center justify-between text-sm">
                            <div className="flex items-center text-gray-500">
                                <TrendingUp className="w-4 h-4 mr-1" />
                                <span>Annual Revenue</span>
                            </div>
                            <span className="font-medium text-gray-900">{formatRevenue()}</span>
                        </div>
                    </div>
                </div>
            </div>
        </Link>
    )
} 