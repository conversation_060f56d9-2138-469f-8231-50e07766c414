import { createClient } from '@/utils/supabase/server'
import { MatchClient } from './MatchClient'
import { Metadata } from 'next'

export const metadata: Metadata = {
    title: 'Find Matching Listings - Business Marketplace',
    description: 'Set your preferences and find businesses that match your criteria.',
}

export default async function MatchPage() {
    const supabase = await createClient()

    // Fetch all industries for the form
    const { data: industries } = await supabase
        .from('industries')
        .select('id, name')
        .order('name')

    // For initial load, we don't need to fetch listings yet
    // They will be fetched based on user preferences through the client component

    return (
        <div className="container mx-auto px-4 py-8">
            <h1 className="text-3xl font-bold mb-6">Find Your Perfect Match</h1>
            <p className="text-gray-600 mb-8">
                Select your preferences below to find business listings that match your criteria.
            </p>

            <MatchClient industries={industries || []} />
        </div>
    )
} 