'use client';

import { useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';

interface TestMessageEmailProps {
    listings: Array<{
        id: string;
        title: string;
        user_id: string;
    }>;
    profiles: Array<{
        user_id: string;
        first_name: string | null;
        last_name: string | null;
        email: string;
    }>;
    currentUserId: string;
}

export default function TestMessageEmail({ listings, profiles, currentUserId }: TestMessageEmailProps) {
    const [selectedListing, setSelectedListing] = useState<string>('');
    const [selectedRecipient, setSelectedRecipient] = useState<string>('');
    const [message, setMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [result, setResult] = useState<{
        success: boolean;
        message: string;
        messageId?: string;
        emailId?: string;
    } | null>(null);

    const supabase = createClient();

    const handleSendTestMessage = async () => {
        if (!selectedListing || !selectedRecipient || !message.trim()) {
            alert('Please fill in all fields');
            return;
        }

        setIsLoading(true);
        setResult(null);

        try {
            console.log('Sending test message...');

            // Create the message in the database (this will trigger the email notification)
            const { data: messageData, error: messageError } = await supabase
                .from('messages')
                .insert({
                    listing_id: selectedListing,
                    sender_id: currentUserId,
                    recipient_id: selectedRecipient,
                    content: `[TEST MESSAGE] ${message}`,
                    read: false
                })
                .select()
                .single();

            if (messageError) {
                throw messageError;
            }

            console.log('Message created:', messageData);

            // Trigger email notification (non-blocking) - just like the real components do
            if (messageData) {
                try {
                    console.log('📧 Triggering email notification for message:', messageData.id);
                    console.log('📦 Message data being sent to API:', JSON.stringify(messageData, null, 2));
                    const emailResponse = await fetch('/api/send-message-email', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            record: messageData
                        })
                    });

                    const emailResult = await emailResponse.json();
                    console.log('📧 Email API response:', emailResult);

                    if (emailResult.success) {
                        setResult({
                            success: true,
                            message: `Test message sent successfully! Email sent to ${emailResult.recipient_email}`,
                            messageId: messageData.id,
                            emailId: emailResult.email_id
                        });
                    } else {
                        setResult({
                            success: false,
                            message: `Message sent but email failed: ${emailResult.error || 'Unknown error'}`
                        });
                    }
                } catch (emailError) {
                    console.log('📧 Email notification failed (non-critical):', emailError);
                    setResult({
                        success: true,
                        message: `Message sent successfully but email notification failed: ${emailError}`,
                        messageId: messageData.id
                    });
                }
            } else {
                setResult({
                    success: true,
                    message: 'Test message sent successfully! (No email notification - no message data)',
                    messageId: 'unknown'
                });
            }

            // Reset form
            setSelectedListing('');
            setSelectedRecipient('');
            setMessage('');

        } catch (error) {
            console.error('Error sending test message:', error);
            setResult({
                success: false,
                message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
        } finally {
            setIsLoading(false);
        }
    };

    const selectedRecipientProfile = profiles.find(p => p.user_id === selectedRecipient);

    return (
        <div className="bg-white shadow-lg rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">Send Test Message</h2>
            </div>
            <div className="p-6 space-y-4">
                <div>
                    <label htmlFor="listing" className="block text-sm font-medium text-gray-700 mb-2">
                        Select Listing
                    </label>
                    <select
                        id="listing"
                        value={selectedListing}
                        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setSelectedListing(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                        <option value="">Choose a listing...</option>
                        {listings.map(listing => (
                            <option key={listing.id} value={listing.id}>
                                {listing.title}
                            </option>
                        ))}
                    </select>
                </div>

                <div>
                    <label htmlFor="recipient" className="block text-sm font-medium text-gray-700 mb-2">
                        Select Message Recipient
                    </label>
                    <select
                        id="recipient"
                        value={selectedRecipient}
                        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setSelectedRecipient(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                        <option value="">Choose a recipient...</option>
                        {profiles.map(profile => (
                            <option key={profile.user_id} value={profile.user_id}>
                                {profile.first_name} {profile.last_name} ({profile.email})
                            </option>
                        ))}
                    </select>

                    {selectedRecipientProfile && (
                        <p className="text-sm text-gray-600 mt-1">
                            Email will be sent to test address (<EMAIL>) instead of {selectedRecipientProfile.email}
                        </p>
                    )}
                </div>

                <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                        Message Content
                    </label>
                    <textarea
                        id="message"
                        value={message}
                        onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setMessage(e.target.value)}
                        placeholder="Enter your test message..."
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                </div>

                <Button
                    onClick={handleSendTestMessage}
                    disabled={isLoading || !selectedListing || !selectedRecipient || !message.trim()}
                    className="w-full"
                >
                    {isLoading ? 'Sending...' : 'Send Test Message & Trigger Email'}
                </Button>

                {result && (
                    <div className={`p-4 rounded-lg ${result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                        <p className={`text-sm ${result.success ? 'text-green-700' : 'text-red-700'}`}>
                            {result.message}
                        </p>
                        {result.messageId && (
                            <p className="text-xs text-gray-500 mt-1">
                                Message ID: {result.messageId}
                            </p>
                        )}
                    </div>
                )}

                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-semibold text-blue-800 mb-2">How this works:</h3>
                    <ol className="text-sm text-blue-700 list-decimal list-inside space-y-1">
                        <li>A message is inserted into the messages table</li>
                        <li>After successful message creation, the UI calls <code>/api/send-message-email</code></li>
                        <li>The API route checks user email preferences and sends email via Resend</li>
                        <li>Email is delivered using the verified domain <code><EMAIL></code></li>
                        <li>This follows the same pattern as the match notification system</li>
                    </ol>
                </div>

                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 className="font-semibold text-green-800 mb-2">✅ What&apos;s working:</h3>
                    <ul className="text-sm text-green-700 list-disc list-inside space-y-1">
                        <li>Message creation in inbox/conversation views</li>
                        <li>New message modal from listing pages</li>
                        <li>Email notifications to actual user emails</li>
                        <li>Respect for user email notification preferences</li>
                        <li>Non-blocking email sending (failures don&apos;t break messaging)</li>
                    </ul>
                </div>
            </div>
        </div>
    );
} 