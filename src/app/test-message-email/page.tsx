import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import TestMessageEmail from './TestMessageEmail';

export default async function TestMessageEmailPage() {
    const supabase = await createClient();

    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
        redirect('/auth/signin');
    }

    // Get some test data for the dropdowns
    const { data: listings } = await supabase
        .from('listings')
        .select('id, title, user_id')
        .limit(10);

    const { data: profiles } = await supabase
        .from('profiles')
        .select('user_id, first_name, last_name, email')
        .neq('user_id', user.id)
        .limit(10);

    return (
        <div className="container mx-auto py-8 px-4">
            <div className="max-w-2xl mx-auto">
                <h1 className="text-3xl font-bold mb-8">Test Message Email Notifications</h1>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <h2 className="text-lg font-semibold text-yellow-800 mb-2">⚠️ Test Environment</h2>
                    <p className="text-yellow-700 text-sm">
                        This page allows you to test the message email notification system. When you send a test message,
                        it will trigger the email notification to be sent to the test email address (<EMAIL>).
                    </p>
                </div>

                <TestMessageEmail
                    listings={listings || []}
                    profiles={profiles || []}
                    currentUserId={user.id}
                />
            </div>
        </div>
    );
} 