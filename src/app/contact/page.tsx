import { ContactForm } from '@/components';
import { <PERSON><PERSON>, Footer } from '@/components';
import { MessageCircle, Phone, MapPin, Mail, Clock } from 'lucide-react';

export default function ContactPage() {
    return (
        <>
            <Header />
            <main className="min-h-screen bg-gray-50 py-8">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Header Card */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 mb-8">
                        <div className="flex items-center space-x-3">
                            <div className="p-2 bg-blue-50 rounded-lg">
                                <MessageCircle className="w-5 h-5 text-blue-600" />
                            </div>
                            <div>
                                <h1 className="text-2xl font-semibold text-gray-900">Contact Us</h1>
                                <p className="text-gray-600 text-sm mt-1">
                                    Get in touch with our team - we&apos;d love to hear from you
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Contact Form - Takes up 2 columns */}
                        <div className="lg:col-span-2">
                            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="p-2 bg-green-50 rounded-lg">
                                        <Mail className="w-5 h-5 text-green-600" />
                                    </div>
                                    <div>
                                        <h2 className="text-xl font-semibold text-gray-900">Send us a Message</h2>
                                        <p className="text-gray-600 text-sm mt-1">
                                            Fill out the form below and we&apos;ll get back to you soon
                                        </p>
                                    </div>
                                </div>
                                <ContactForm />
                            </div>
                        </div>

                        {/* Contact Information - Takes up 1 column */}
                        <div className="space-y-6">
                            {/* Contact Info Card */}
                            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="p-2 bg-purple-50 rounded-lg">
                                        <Phone className="w-5 h-5 text-purple-600" />
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-semibold text-gray-900">Contact Information</h3>
                                        <p className="text-gray-600 text-sm mt-1">
                                            Multiple ways to reach us
                                        </p>
                                    </div>
                                </div>

                                <div className="space-y-4">
                                    <div className="flex items-start space-x-3">
                                        <div className="p-2 bg-gray-50 rounded-lg">
                                            <Phone className="w-4 h-4 text-gray-600" />
                                        </div>
                                        <div>
                                            <p className="font-medium text-gray-900">Phone</p>
                                            <p className="text-gray-600 text-sm">+****************</p>
                                        </div>
                                    </div>

                                    <div className="flex items-start space-x-3">
                                        <div className="p-2 bg-gray-50 rounded-lg">
                                            <Mail className="w-4 h-4 text-gray-600" />
                                        </div>
                                        <div>
                                            <p className="font-medium text-gray-900">Email</p>
                                            <p className="text-gray-600 text-sm"><EMAIL></p>
                                        </div>
                                    </div>

                                    <div className="flex items-start space-x-3">
                                        <div className="p-2 bg-gray-50 rounded-lg">
                                            <MapPin className="w-4 h-4 text-gray-600" />
                                        </div>
                                        <div>
                                            <p className="font-medium text-gray-900">Address</p>
                                            <p className="text-gray-600 text-sm">
                                                123 Business Ave<br />
                                                Suite 100<br />
                                                New York, NY 10001
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Business Hours Card */}
                            <div className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="p-2 bg-orange-50 rounded-lg">
                                        <Clock className="w-5 h-5 text-orange-600" />
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-semibold text-gray-900">Business Hours</h3>
                                        <p className="text-gray-600 text-sm mt-1">
                                            When you can reach us
                                        </p>
                                    </div>
                                </div>

                                <div className="space-y-3">
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Monday - Friday</span>
                                        <span className="font-medium text-gray-900">9:00 AM - 6:00 PM</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Saturday</span>
                                        <span className="font-medium text-gray-900">10:00 AM - 4:00 PM</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Sunday</span>
                                        <span className="font-medium text-gray-900">Closed</span>
                                    </div>
                                </div>

                                <div className="mt-6 pt-6 border-t border-gray-100">
                                    <p className="text-sm text-gray-600">
                                        <strong className="text-gray-900">Response Time:</strong> We typically respond to inquiries within 24 hours during business days.
                                    </p>
                                </div>
                            </div>

                            {/* Quick Support Card */}
                            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-200/60 p-6">
                                <div className="text-center">
                                    <div className="p-3 bg-blue-100 rounded-full inline-block mb-4">
                                        <MessageCircle className="w-6 h-6 text-blue-600" />
                                    </div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Need Quick Help?</h3>
                                    <p className="text-gray-600 text-sm mb-4">
                                        Check out our FAQ section for immediate answers to common questions.
                                    </p>
                                    <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                                        Visit FAQ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
            <Footer />
        </>
    );
} 