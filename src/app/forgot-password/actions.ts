'use server'

import { createClient } from '@/utils/supabase/server'

export async function forgotPassword(formData: FormData): Promise<{ error?: string; success?: boolean }> {
    const supabase = await createClient()

    const email = formData.get('email') as string

    if (!email) {
        return { error: 'Email is required' }
    }

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/reset-password`,
    })

    if (error) {
        return { error: error.message }
    }

    return { success: true }
} 