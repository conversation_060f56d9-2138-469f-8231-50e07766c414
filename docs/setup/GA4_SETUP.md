# GA4 Analytics Setup Guide

## 🎯 **Automatically Tracked Parameters**

These parameters are **automatically indexed** by GA4 and will show up in reports without any configuration:

- ✅ `search_term` - Shows up in GA4 search reports
- ✅ `item_id`, `item_name`, `item_category` - E-commerce tracking
- ✅ `content_type`, `content_id` - Content tracking
- ✅ `value`, `currency` - Conversion tracking

## 🔧 **Custom Dimensions Setup**

For our custom business listing parameters, you need to set up custom dimensions in GA4:

### **Step 1: Access GA4 Admin**
1. Go to GA4 → Admin → Data display → Custom definitions
2. Click "Create custom dimensions"

### **Step 2: Create These Custom Dimensions**

| **Dimension Name** | **Parameter Name** | **Scope** | **Description** |
|-------------------|-------------------|-----------|-----------------|
| Location Filter | `location` | Event | State/location searched |
| Listing Type | `listing_type` | Event | Type of business search |
| Has Query | `has_query` | Event | Whether search had text |
| Has Location Filter | `has_location_filter` | Event | Whether location was filtered |

### **Step 3: Configure Each Dimension**

For example, for "Location Filter":
```
Dimension name: Location Filter
Parameter name: location
Scope: Event
Description: State or location used in business search
```

## 📊 **Event Tracking Implementation**

### **Current Events Being Tracked:**

1. **Search Event**
   ```typescript
   trackSearch("restaurant", "California")
   ```
   Sends:
   ```json
   {
     "event": "search",
     "search_term": "restaurant",        // ✅ Auto-indexed
     "location": "California",           // ⚙️ Custom dimension
     "listing_type": "business_for_sale", // ⚙️ Custom dimension
     "has_query": true,                  // ⚙️ Custom dimension
     "has_location_filter": true         // ⚙️ Custom dimension
   }
   ```

2. **Listing View Event**
   ```typescript
   trackListingView("123", "Joe's Pizza", "restaurant")
   ```
   Sends:
   ```json
   {
     "event": "view_item",
     "item_id": "123",                   // ✅ Auto-indexed
     "item_name": "Joe's Pizza",         // ✅ Auto-indexed
     "item_category": "restaurant",      // ✅ Auto-indexed
     "content_type": "business_listing"  // ⚙️ Custom dimension
   }
   ```

## 🧪 **Testing Your Analytics**

### **1. Real-time Testing**
1. Go to GA4 → Reports → Realtime
2. Perform a search on your site
3. Check "Events by Event name" - you should see `search` events

### **2. Debug View**
1. Install Google Analytics Debugger Chrome extension
2. Enable debug mode
3. Open browser console
4. Perform actions - you'll see detailed event data

### **3. Console Logging (Development)**
Our implementation logs all events in development:
```
✅ Analytics (Sent): search {search_term: "pizza", location: "New York", ...}
```

## 📈 **Creating Reports**

### **Custom Exploration Report**
1. Go to GA4 → Explore → Create new exploration
2. Add dimensions:
   - `Event name`
   - `Location Filter` (custom)
   - `search_term` (auto)
3. Add metrics:
   - `Event count`
   - `Active users`

### **Search Analysis Report**
Create a report showing:
- Most searched terms
- Most searched locations
- Search conversion rates

## 🚨 **Common Issues Fixed**

### **❌ Before (Bad)**
```typescript
// Duplicate parameters
event('search', {
  search_term: "pizza",
  term: "pizza",           // ❌ Duplicate!
  state_name: "California", // ❌ Not semantic
  search_type: "business"   // ❌ Not specific
});
```

### **✅ After (Good)**
```typescript
// Clean, semantic parameters
trackSearch("pizza", "California");
// Sends clean, GA4-optimized data
```

## 💡 **Best Practices Implemented**

1. **Standard Event Names**: Using `search`, `view_item` instead of custom names
2. **Semantic Parameters**: `location` instead of `state_name`
3. **No Duplicates**: Single source of truth for each data point
4. **Auto-indexed Focus**: Prioritizing parameters GA4 indexes automatically
5. **Custom Dimensions**: Only for business-specific data that adds value

## 🔄 **Next Steps**

1. **Set up the custom dimensions** in GA4 admin
2. **Wait 24-48 hours** for data to populate
3. **Create custom reports** using the new dimensions
4. **Set up conversion goals** based on search → view → contact flow

This setup will give you much better visibility into user behavior and search patterns on your business marketplace! 