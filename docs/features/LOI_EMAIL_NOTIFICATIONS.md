# Letter of Intent Email Notifications

This document describes the email notification system that sends emails to listing owners when they receive Letters of Intent (LOI) from potential buyers.

## Overview

The system automatically sends specialized email notifications to listing owners when buyers upload Letters of Intent as attachments to messages. This ensures listing owners are immediately notified about formal offers and can respond promptly to serious buyer interest.

## How It Works

### 1. Database Structure

- **`messages`** table: Contains individual messages with `sender_id`, `recipient_id`, `listing_id`, and `content`
- **`message_attachments`** table: Stores file attachments with `attachment_type` field to identify LOIs
- **No database triggers**: Uses direct API calls from the UI after LOI upload

### 2. Components

#### UI Integration Point
- **`InboxContent.tsx`**: File upload handler - calls LOI email API after successful LOI attachment insertion

#### API Route
- **Endpoint**: `/api/send-loi-email`
- **Purpose**: Processes LOI data and sends specialized email via Resend
- **Features**: 
  - Fetches message details and attachment information
  - Gets sender (buyer) and recipient (listing owner) profiles
  - Retrieves listing details for context
  - Respects user email notification preferences
  - Uses verified domain: `<EMAIL>`

#### Email Content
The LOI email includes:
- **Professional green-themed design** to highlight the importance of receiving an LOI
- **Personalized greeting** with listing owner's name
- **Sender information** (buyer's name and company)
- **Listing details** (title, price) with special LOI badge
- **LOI file details** (filename, sender, file size)
- **Business guidance** about LOI handling and next steps
- **Dual CTAs**: "View & Download LOI" and "Reply to Buyer"
- **Professional advice** about due diligence and business sale process
- **Security reminders** specific to business transactions

### 3. Implementation Pattern

This follows the same pattern as the existing message notification system:

1. **LOI Upload**: User uploads letter_of_intent attachment via UI
2. **Message & Attachment Creation**: Data is inserted into database
3. **API Call**: After successful insertion, UI calls `/api/send-loi-email`
4. **Email Processing**: API route validates, fetches data, and sends email
5. **Non-blocking**: Email failures don't affect LOI upload process

### 4. Safety Features

- **User Preferences**: Respects `email_notifications` setting in profiles
- **Real Email Delivery**: Sends to actual listing owner emails
- **Error Handling**: Graceful failure without breaking LOI upload
- **Attachment Type Filter**: Only triggers for `letter_of_intent` attachments
- **Verified Domain**: Uses properly configured sending domain

## Email Design Features

### Visual Design
- **Green gradient header** (professional, money/business-focused)
- **Document icon** in header for LOI recognition
- **Gradient listing card** with special LOI badge
- **File details card** with sender information
- **Professional color scheme** (greens, blues, appropriate warnings)

### Content Strategy
- **Business-focused language** appropriate for formal offers
- **Next steps guidance** for listing owners
- **Due diligence reminders** for safe transactions
- **Professional advice** about LOI handling
- **Clear CTAs** for viewing LOI and responding

## Setup Instructions

### 1. UI Integration ✅ Complete
- File: `/src/app/messages/components/InboxContent.tsx`
- LOI upload handler calls email API after successful attachment creation

### 2. API Route ✅ Complete
- File: `/src/app/api/send-loi-email/route.ts`
- Handles LOI-specific email sending logic
- Uses Resend API with verified domain

### 3. Email Template ✅ Complete  
- Professional HTML template with green theme
- LOI-specific content and guidance
- Responsive and accessible design

## Testing

### Test Page ✅ Available
Visit `/test-loi-email` (when logged in) to:
- Simulate LOI uploads that trigger email notifications
- See the full flow in action
- Verify email delivery and content

### Test Flow
1. Select a listing and recipient (listing owner) from dropdowns
2. Configure LOI file details (name, size)
3. Click "Send Test LOI & Trigger Email"
4. Test message and attachment are created in database
5. LOI email API is called immediately after
6. Specialized LOI email is sent to listing owner
7. Check email inbox for LOI notification

## Production Setup ✅ Ready

### 1. Email Configuration ✅ Complete
- Uses verified domain: `<EMAIL>`
- Sends to actual listing owner emails
- Professional LOI-specific email template

### 2. User Preferences ✅ Implemented
- Respects `email_notifications` column in profiles table
- Non-intrusive (emails are skipped if disabled)

### 3. Error Handling ✅ Robust
- Email failures logged but don't break LOI uploads
- Graceful degradation
- Non-blocking architecture

## Architecture Benefits

1. **Business-Focused**: Designed specifically for formal business offers
2. **Professional Communication**: Appropriate tone and guidance for business sales
3. **Non-Breaking**: Email failures don't affect LOI upload process
4. **Follows Existing Pattern**: Uses same approach as message notifications
5. **Secure**: Doesn't expose sensitive data
6. **Testable**: Easy to test with dedicated test page
7. **Scalable**: Can be extended with more business-specific notifications

## Files Created/Modified

- ✅ `/src/app/api/send-loi-email/route.ts` - LOI email API endpoint
- ✅ `/src/app/messages/components/InboxContent.tsx` - Added LOI email trigger
- ✅ `/src/app/test-loi-email/page.tsx` - Test page
- ✅ `/src/app/test-loi-email/TestLoiEmail.tsx` - Test component
- ✅ `LOI_EMAIL_NOTIFICATIONS.md` - This documentation

## Key Differences from Regular Message Emails

| Feature | Regular Messages | LOI Notifications |
|---------|-----------------|-------------------|
| **Design Theme** | Blue/Gray | Green (business/money) |
| **Urgency Level** | Standard | High (formal offer) |
| **Content Focus** | General communication | Business sale process |
| **Guidance** | Basic security | Due diligence & legal advice |
| **CTAs** | Reply to message | View LOI + Reply to buyer |
| **File Handling** | Generic attachment | Specialized LOI details |
| **Business Context** | General inquiry | Formal business offer |

## Next Steps

The system is now **production ready**! Optional enhancements:

1. **LOI Analytics**: Track LOI open/download rates
2. **Follow-up Sequences**: Automated follow-up emails for LOI responses
3. **Integration**: Connect with CRM systems for business sale tracking
4. **LOI Templates**: Provide standardized LOI templates for buyers
5. **Legal Integration**: Connect with legal services for due diligence

## Monitoring

- Monitor LOI email delivery via Resend dashboard
- Track LOI upload patterns and email success rates
- Watch for user engagement with LOI notifications
- Monitor business conversion rates from LOI to sale

## Security Considerations

- **Due Diligence**: Email includes reminders about verifying buyer credentials
- **NDA Requirements**: Guidance about sharing sensitive business information
- **Platform Safety**: Encourages communication through the platform
- **Professional Advice**: Recommends consulting brokers/attorneys

This system significantly enhances the business sale experience by ensuring listing owners never miss formal offers and have the guidance they need to handle them professionally. 