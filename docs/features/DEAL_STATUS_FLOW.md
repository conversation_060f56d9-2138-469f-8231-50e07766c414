# Deal Status Flow: Letter of Intent Implementation

## 🎯 Overview
This document explains how the Letter of Intent (LOI) functionality works to automatically update deal statuses and move cards between columns in the deals dashboard.

## ✅ Flow Summary

### For Buyers (Non-listing owners)
1. **User uploads LOI** in a conversation via `/messages`
2. **Status updated to `initial_offer`** in `deal_statuses` table
3. **Card moves** from "Active Conversations" to "Initial Offer" column in buyer's deals dashboard

### For Sellers (Listing owners)
1. **Buyer uploads LOI** to their listing conversation
2. **Buyer's profile moves** from "Interested Buyers" to "Offers Received" column in seller's deals dashboard
3. **Real-time updates** via Supabase subscriptions

## 🔧 Implementation Details

### Database Schema
- **Table**: `deal_statuses`
- **Key fields**: `listing_id`, `buyer_id`, `seller_id`, `status`, `status_changed_at`
- **Status values**: `saved`, `active_conversation`, `meeting_scheduled`, `initial_offer`, `letter_of_intent`, `due_diligence`, `final_negotiation`, `closed`

### Code Flow

#### 1. LOI Upload (`src/app/messages/components/InboxContent.tsx`)
```typescript
// When buyer uploads letter_of_intent
if (attachmentType === 'letter_of_intent') {
    // Get listing owner
    const { data: listing } = await supabase
        .from('listings')
        .select('user_id')
        .eq('id', selectedListingId)
        .single();

    // Update deal status to initial_offer
    await supabase
        .from('deal_statuses')
        .upsert({
            listing_id: selectedListingId,
            buyer_id: currentUser.id,
            seller_id: listing.user_id,
            status: 'initial_offer',
            status_changed_at: new Date().toISOString()
        });
}
```

#### 2. Dashboard Updates (`src/app/deals-dashboard/components/EnhancedDealsDashboardClient.tsx`)

**Buyer View Columns:**
- **Active Conversations**: `dealStatus === 'active_conversation'`
- **Initial Offer**: `dealStatus === 'initial_offer'` ← LOI moves cards here
- **Meeting Scheduled**: `dealStatus === 'meeting_scheduled'`
- **Acquired**: `dealStatus === 'closed'`

**Seller View Columns:**
- **Interested Buyers**: `dealStatus === 'interested'` or `'saved'` or `'active_conversation'`
- **Offers Received**: `dealStatus === 'initial_offer'` ← LOI moves buyers here
- **Under Negotiation**: `dealStatus === 'letter_of_intent'` or `'due_diligence'` or `'final_negotiation'`

#### 3. Real-time Updates
```typescript
// Subscription to deal_statuses table changes
const dealStatusSubscription = supabase
    .channel('deal_status_changes')
    .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'deal_statuses',
        filter: `listing_id=eq.${selectedListingId}`,
    }, (payload) => {
        // Refresh dashboard data
        fetchInterestedBuyers(selectedListingId);
        fetchBuyerDeals(currentUserId);
    })
    .subscribe();
```

## 📁 File Locations

### Core Files
- **LOI Upload Logic**: `src/app/messages/components/InboxContent.tsx` (lines 850-916)
- **Dashboard Logic**: `src/app/deals-dashboard/components/EnhancedDealsDashboardClient.tsx`
- **Server Actions**: `src/app/deals-dashboard/actions.ts`
- **Debug Component**: `src/app/deals-dashboard/components/DealStatusDebugger.tsx`
- **Main Page**: `src/app/deals-dashboard/page.tsx`
- **Types**: `src/types/inbox.ts`

### Key Functions
- `handleFileUpload()` - Handles LOI upload and status update
- `fetchBuyerDeals()` - Fetches and categorizes buyer deals
- `fetchInterestedBuyers()` - Fetches and categorizes buyers for sellers
- `updateDealStatusToInitialOffer()` - Server action for status updates

## 🧪 Testing the Flow

### As a Buyer:
1. Go to a business listing you're interested in
2. Start a conversation with the seller
3. Upload a Letter of Intent file
4. Check `/deals-dashboard` - the business should move from "Active Conversations" to "Initial Offer"

### As a Seller:
1. Go to `/deals-dashboard` and view one of your listings
2. Have a potential buyer upload an LOI in your conversation
3. The buyer should move from "Interested Buyers" to "Offers Received" column
4. Changes should appear in real-time (or after page refresh)

### Debug Mode:
- In development mode, a debug component appears at the bottom of `/deals-dashboard`
- Shows all LOI uploads and their current deal status
- Provides "Fix Status" buttons to manually trigger status updates
- Displays expected flow and current state

## 📊 Status Values Mapping

| Database Status | Buyer Column | Seller Column |
|----------------|--------------|---------------|
| `saved` | Saved Listing | Interested Buyers |
| `active_conversation` | Active Conversations | Interested Buyers |
| `meeting_scheduled` | Buyer/Seller Meeting | Scheduled Meetings |
| `initial_offer` | **Initial Offer** | **Offers Received** |
| `letter_of_intent` | Initial Offer | Under Negotiation |
| `due_diligence` | Initial Offer | Under Negotiation |
| `final_negotiation` | Initial Offer | Under Negotiation |
| `closed` | Acquired | Completed Sales |

## 🚀 Features Implemented

### ✅ Core Features
- [x] LOI upload detection in messages
- [x] Automatic deal status update to `initial_offer`
- [x] Buyer dashboard card movement (Active Conversations → Initial Offer)
- [x] Seller dashboard buyer movement (Interested Buyers → Offers Received)
- [x] Real-time subscription updates
- [x] Server actions for reliable status updates
- [x] Debug component for testing and troubleshooting

### ✅ Technical Features
- [x] Proper error handling
- [x] Database integrity with buyer_id, seller_id, listing_id relationships
- [x] TypeScript interfaces and type safety
- [x] Console logging for debugging
- [x] Development-only debug tools

## 📝 Notes
- The term "informal_offer" mentioned in requirements maps to `initial_offer` in the database
- The system uses `letter_of_intent` attachment type to trigger the status change
- Real-time updates ensure both parties see changes immediately
- The system maintains referential integrity with buyer_id, seller_id, and listing_id
- Debug component is only visible in development mode 