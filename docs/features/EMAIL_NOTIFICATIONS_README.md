# Email Notifications for Match Categories

This document describes the email notification system that sends users alerts when new listings match their saved search criteria.

## Overview

The system automatically sends email notifications to users when new business listings are created that match their saved "match" criteria. This helps users stay updated on relevant opportunities without having to manually check the platform.

## How It Works

### 1. Database Structure

- **`saved_matches`** table: Stores user's saved search criteria with preferences (price range, revenue, industry, location, etc.)
- **`match_notifications`** table: Tracks which notifications have been sent to prevent duplicates
- **Database trigger**: Automatically checks new listings against all saved matches when listings are created

### 2. Components

#### Database Trigger Function
- `check_new_listing_matches()`: PostgreSQL function that runs when new listings are inserted
- Compares new listing attributes against all saved matches
- Creates notification records for matching criteria
- Prevents duplicate notifications with unique constraints

#### Edge Function
- `send-match-notifications`: Supabase Edge Function that processes pending notifications
- Fetches unsent notifications from the database
- Sends personalized emails using Resend API
- Marks notifications as sent to prevent duplicates

#### API Route
- `/api/trigger-match-notifications`: Next.js API route to manually trigger notification processing
- Can be called as a webhook or scheduled job
- Provides endpoints to trigger emails and view notification history

### 3. Email Content

The email includes:
- Personalized greeting using user's first name
- Details about the matching listing (title, price, description)
- Summary of the user's match criteria that triggered the notification
- Direct link to view the full listing
- Link to manage saved matches

## Setup Instructions

### 1. Database Setup
The database migration creates:
- `match_notifications` table with proper RLS policies
- Database trigger function `check_new_listing_matches()`
- Trigger that executes the function on new listing inserts

### 2. Edge Function Deployment
The `send-match-notifications` function is deployed to Supabase Edge Functions and handles:
- Processing pending notifications
- Sending emails via Resend API
- Updating notification status

### 3. Environment Variables
Required environment variables:
- `SUPABASE_SERVICE_ROLE_KEY`: For admin database access
- Resend API key is currently hardcoded but should be moved to environment variables

## Testing

### Test Page
Visit `/test-notifications` (when logged in) to:
- Create test listings that trigger notifications
- Manually trigger email processing
- View the system in action

### Test Flow
1. Create a saved match in `/match` with specific criteria
2. Create a test listing via the test page that matches those criteria
3. Trigger email notifications manually
4. Check email for notifications (currently sent to test email)

## Production Considerations

### 1. Email Configuration
- Update the edge function to use actual user emails instead of test email
- Move Resend API key to environment variables
- Configure proper domain for email sending

### 2. Scheduling
- Set up automated scheduling to process notifications (e.g., every 15 minutes)
- Consider rate limiting to prevent spam
- Add user preferences for email frequency

### 3. Monitoring
- Add logging for email delivery success/failure
- Monitor notification queue size
- Track user engagement with notification emails

### 4. User Control
- Add user settings to enable/disable notifications per saved match
- Allow users to unsubscribe from notifications
- Provide notification frequency controls

## Usage Example

1. User creates a saved match: "SaaS businesses under $200k in California"
2. New SaaS listing gets created for $150k in San Francisco
3. Database trigger detects the match and creates notification record
4. Edge function processes notification and sends email
5. User receives personalized email about the matching opportunity

## Files Modified/Created

- Database migration: Creates tables and trigger function
- `/supabase/functions/send-match-notifications/index.ts`: Edge function for email processing
- `/src/app/api/trigger-match-notifications/route.ts`: API endpoints
- `/src/app/test-notifications/`: Test interface
- Database triggers on `listings` table

## Next Steps

1. Test the system thoroughly with various match criteria
2. Configure proper email domain and move API keys to environment variables
3. Set up automated scheduling for notification processing
4. Add user preference controls for notification settings
5. Monitor email delivery rates and user engagement 