# Message Email Notifications

This document describes the email notification system that sends emails to users when they receive new messages.

## Overview

The system automatically sends email notifications to users when they receive new messages about their listings. This ensures users are promptly notified about potential inquiries and can respond quickly.

## How It Works

### 1. Database Structure

- **`messages`** table: Contains individual messages with `sender_id`, `recipient_id`, `listing_id`, and `content`
- **`conversations`** table: Groups messages between users for a specific listing
- **No database triggers**: Uses direct API calls from the UI after message creation

### 2. Components

#### UI Integration Points
- **`MessageList.tsx`**: Inbox conversation view - calls email API after message insertion
- **`NewMessageModal.tsx`**: Modal for new messages from listings - calls email API after message insertion  
- **`ListingMessages.tsx`**: Listing-specific messaging - calls email API after message insertion

#### API Route
- **Endpoint**: `/api/send-message-email`
- **Purpose**: Processes message data and sends email via Resend
- **Features**: 
  - Fetches recipient profile and email preferences
  - Gets sender profile for personalization
  - Retrieves listing details for context
  - Respects user email notification preferences
  - Uses verified domain: `<EMAIL>`

#### Email Content
The email includes:
- Personalized greeting with recipient's name
- Sender information (name)
- Listing details (title, price)
- Full message content in a styled card
- Direct link to inbox for reply
- Security reminder about safe transactions
- Professional styling matching the match notification emails

### 3. Implementation Pattern

This follows the same pattern as the existing match notification system:

1. **Message Creation**: UI inserts message into database
2. **API Call**: After successful insertion, UI calls `/api/send-message-email`
3. **Email Processing**: API route validates, fetches data, and sends email
4. **Non-blocking**: Email failures don't affect message creation

### 4. Safety Features

- **User Preferences**: Respects `email_notifications` setting in profiles
- **Real Email Delivery**: Sends to actual user emails (not test addresses)
- **Error Handling**: Graceful failure without breaking message creation
- **System Message Filter**: Only sends emails for messages with `recipient_id`
- **Verified Domain**: Uses properly configured sending domain

## Setup Instructions

### 1. UI Integration ✅ Complete
All message creation points now trigger email notifications:
- Inbox conversations (`MessageList.tsx`)
- New message modal (`NewMessageModal.tsx`) 
- Listing messages (`ListingMessages.tsx`)

### 2. API Route ✅ Complete
- File: `/src/app/api/send-message-email/route.ts`
- Handles email sending logic
- Uses Resend API with verified domain

### 3. Email Template ✅ Complete  
- Professional HTML template
- Matches design of match notification emails
- Responsive and accessible

## Testing

### Test Page ✅ Available
Visit `/test-message-email` (when logged in) to:
- Send test messages that trigger email notifications
- See the full flow in action
- Verify email delivery

### Test Flow
1. Select a listing and recipient from dropdowns
2. Enter test message content
3. Click "Send Test Message & Trigger Email"
4. Message is created in database
5. Email API is called immediately after
6. Email is sent to actual recipient email
7. Check email inbox for notification

## Production Setup ✅ Ready

### 1. Email Configuration ✅ Complete
- Uses verified domain: `<EMAIL>`
- Sends to actual user emails
- Professional email template

### 2. User Preferences ✅ Implemented
- Respects `email_notifications` column in profiles table
- Non-intrusive (emails are skipped if disabled)

### 3. Error Handling ✅ Robust
- Email failures logged but don't break messaging
- Graceful degradation
- Non-blocking architecture

## Architecture Benefits

1. **Simple & Reliable**: No complex database triggers to debug
2. **Follows Existing Pattern**: Uses same approach as match notifications
3. **Non-Breaking**: Email failures don't affect message creation
4. **Respectful**: Honors user email preferences
5. **Secure**: Doesn't expose sensitive data
6. **Testable**: Easy to test with dedicated test page
7. **Scalable**: Can be extended with more notification types

## Files Created/Modified

- ✅ `/src/app/api/send-message-email/route.ts` - Email API endpoint
- ✅ `/src/components/inbox/MessageList.tsx` - Added email trigger
- ✅ `/src/components/NewMessageModal.tsx` - Added email trigger  
- ✅ `/src/components/listings/ListingMessages.tsx` - Added email trigger
- ✅ `/src/app/test-message-email/page.tsx` - Test page
- ✅ `/src/app/test-message-email/TestMessageEmail.tsx` - Test component
- ✅ `MESSAGE_EMAIL_NOTIFICATIONS.md` - This documentation

## Next Steps

The system is now **production ready**! Optional enhancements:

1. **Analytics**: Track email open/click rates
2. **Digest Emails**: Option for daily/weekly message summaries
3. **Push Notifications**: Add mobile push notifications
4. **Email Templates**: Create multiple template variations
5. **A/B Testing**: Test different email formats

## Monitoring

- Monitor email delivery via Resend dashboard
- Check application logs for email API call patterns
- Track user engagement with notification emails
- Watch for any user feedback about notification preferences 