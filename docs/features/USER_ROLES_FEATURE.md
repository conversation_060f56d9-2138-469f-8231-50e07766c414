# User Roles Feature

## Overview
This feature allows users to switch between different roles on the platform: **Seller**, **Buyer**, and **Seller+Buyer**.

## Implementation Details

### Database Changes
- Added `user_role` enum type with values: `seller`, `buyer`, `seller_buyer`
- Added `user_role` column to `profiles` table with default value `seller_buyer`
- All existing users automatically get the default `seller_buyer` role

### User Interface
- Added `UserRoleTabs` component with minimalist tab design
- Integrated into the account page in the left sidebar
- Shows role-specific descriptions and icons
- **Save Button**: Only appears when a different role is selected
- Changes are only saved to database when "Save Changes" button is clicked
- Loading states and error handling with automatic rollback

### Component Location
- **File**: `src/components/UserRoleTabs.tsx`
- **Integration**: `src/app/account/page.tsx`

### Role Definitions
1. **Seller**: Users can list and sell businesses
2. **Buyer**: Users can browse and buy businesses  
3. **Seller+Buyer**: Users can both list businesses for sale and browse to buy (default)

### User Experience
- **Tab Selection**: Users can click on different role tabs to preview their selection
- **Save Button**: A blue "Save Changes" button appears only when a different role is selected
- **Immediate Feedback**: Role descriptions update immediately when tabs are clicked
- **Confirmation**: Changes are only persisted when the Save button is clicked
- **Error Handling**: If save fails, the selection reverts to the original role

### Future Extensions
- **Broker**: Role reserved for future implementation
- Role-based UI features and permissions
- Role-specific dashboard views

## Usage
Users can switch roles by:
1. Going to their Account page
2. Using the User Role tabs in the left sidebar
3. Clicking on their preferred role (Seller, Buyer, or Seller+Buyer)
4. Clicking the "Save Changes" button that appears
5. The button disappears once changes are saved

The interface provides clear visual feedback and only saves changes when explicitly confirmed by the user. 