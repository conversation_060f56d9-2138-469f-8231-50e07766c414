# Progress


** For <PERSON><PERSON><PERSON> to remember each session **
* We're using TS over JS.
* We're using NextJS 14/15 with Supabase and we already have an authentication-method setup via src/utils/supabase/server.ts and src/utils/supabase/client.ts so always keep that in mind


## What's Done
- **Authentication**  
  - [x] Implemented user sign-up, login, and logout flows
  - [x] Added proper signup flow with validation
- **Account Page**  
  - [x] User profile with clean UI
  - [x] Profile photo upload
  - [x] Account fields updating



** January 4
[x] Add an overall green-ish styling that feels minimal and modern
[x] Update the account page with basic styling so that it looks minimal but also nice
[x] Make sure you can update the account fields on Account Profile


** January 7

[x] Have a hero thats modern and has a search
[x] Make sure there is a standard green-ish theme applied to all
[x] Make a homepage that explains the app
[x] Have a header that is re-useable on every page
[x] Have a footer that is re-usable on every page



** January 8

[x] Fix Account Profile page
[x] Add proper SEO metadata and titles

** January 15
[x] Add a /Listings/[id] page
[x] Reference it on /Listings


** January 20
[x] Make it possible to upload an image as a background image
[x] Reference the image on /Listings
[x] Edit a listing you have created

** January 21
[x] Make a new dump of database for context
[x] Implement ChatGPT API
[x] Implement app on a description field
[x] Fix Account Signup Proper Flow

** January 22
[x] Updated database schema with new tables and relationships
[x] Fix Bugs for Yarn Dev


** January 23
[x] Design a decent listing category (PCP)
[x] Make the Sort By work (PCP)
[x] Make the Filter work (PCP)
[x] Design a decent listing page layout (PDP)

[x] Add category filtering

** January 24
[x] Add favorites functionality
[x] Add search
[x] Restyle all cards
[x] Clean up design to be minimal/good

** February 10
[x] Complete messaging system implementation
  - [x] Real-time chat between users
  - [x] Conversation management
  - [x] Message history
  - [x] Unread message status
[x] Add notification system
  - [x] Real-time notifications
  - [x] Unread message count
  - [x] Notification bell in header
[x] Data Room Access Control
  - [x] Toggle access in chat
  - [x] Real-time access updates
  - [x] Access status indicators
[x] Inbox Page Features
  - [x] User list with latest messages
  - [x] Real-time message updates
  - [x] Message read status
  - [x] Conversation threading by listing

## Bugs
[ ] Mobile Menu doesn't work
[ ] Pages look ugly when page is not long (Footer should be stock to bottom)

## V0 (Pre-Nat) Checklist


0. **Cosmetics nd UX**
   - [x] Figure out a ok styling, that works with semi-corporate (Not too friendly/playful)
 

1. **User & Account Management**
   - [x] Enhance Profile Page: Provide ability for users to edit personal details.
   - [x] Update Supabase
   - [x] Add Header and Footer
   - [x] Add a method to add a photo
   - [x] Make header aware of you being logged in
   - [ ] Password Reset Functionality (if not already in place).
   - [ ] Account dropdown menu in Header


1.5 **SEO/Accessibility**
   - [ ] 404 pages
   - [x] Skeletons for loading
   - [ ] Clean up legibility for pages

2. **Listings**
   - [x] **Create Listing**: Allow sellers to add a product or service with a title, description, and price.
   - [x] **Edit & Delete Listings**: Ensure listing creator can modify or remove their listings.
   - [x] **Basic Listing Display**: Simple marketplace view with an image, title, and price.
   - [x] **Upload Background image** Be able to upload an image that serves as the background hero of each listing
   - [x] **Artwork displayed on Overview** Have the artwork displayed on the overview page
   - [x] **Artwork displayed on Cards** Have the listingsCard have a background image

3. **Search & Filter**
   - [x] **Basic Search**: Search by product title or category.
   - [x] **Basic Filters**: Filter by price or category.
   - [x] **Categories**: Implement category selection when creating/editing listings
   - [x] **Search Results**: Create a dedicated search results page

3.5 **Favoriting**
   - [x] **Favorite functionality**: Implement favorites table and API endpoints
   - [x] **Favorite UI**: Add favorite button to listing cards and detail pages
   - [x] **Favorites Page**: Create a Favorites page under the Account Menu
   - [ ] **Favorite Count**: Show number of favorites on listing cards

4. **Messaging System**
   - [x] Real-time chat
   - [x] Message notifications
   - [x] Unread indicators
   - [x] Conversation management
   - [x] Data room access controls

5. **Notifications**
   - [ ] **Message Notification by Email**: Email message upon receiving a message (a test)
   - [ ] **Message Notification in-app**: In-app message upon receiving a message (a test)
   - [ ] **Other Notifications**: Figure out what other notifications are needed (when you receive access to data room, etc)

6. **Design**
   - [ ] **Fonts, Icons, Colors**: Decided on the actual look and feel
   - [ ] **Layouts**: How to AirBnb/Facebook Marketplace-ify the platform (for UX/UI)
   

7. **Administrative / Moderation**
   - [ ] **Basic Admin Panel**: Oversee user accounts, remove problematic listings.
   - [ ] **Simple Metrics/Stats**: Track total listings, user count, etc.

8. **Testing & Deployment**
   - [ ] **Basic Automated Tests**: At least smoke tests for authentication and listing creation.
   - [ ] **Continuous Deployment**: Automatic deploy to staging for each new commit.

## Next Steps
- Tackle the above tasks in priority order (listings, checkout flow, account enhancements).
- After the MVP items are stable and tested, consider extra features (like advanced search or user feedback).

## Notes
- Keep designs minimal and consistent for now—focus on function over styling.
- Document any environment variables (API keys, tokens) for easy setup.


## Must-Have Features (March 2024)

### 1. Search & Filtering Improvements
- [ ] Implement "no zero results" policy for all searches
- [ ] Add fallback mechanism to show alternative results when search returns nothing
- [ ] Create notification system to inform users when their exact search has no results
- [ ] Ensure this works for text search, location search, and industry/type filtering
- [ ] Add "try again later" messaging with alternative results display

### 2. "Add Your Business" Flow Improvements
- [ ] Simplify business listing process
- [ ] Create streamlined form with business name + zip code as primary inputs
- [ ] Implement Google Places API integration for auto-filling business details
- [ ] Add OpenAI integration to generate business descriptions
- [ ] Allow users to edit AI-generated descriptions

- [ ] Implement quick valuation system
- [ ] Create valuation model based on BizBuySell data
- [ ] Display financial inputs using broad scale brackets for easier input
- [ ] Make detailed information optional but available for better valuation
- [ ] Hide complex valuation metrics by default (show on request only)

- [ ] Add listing preview functionality
- [ ] Show sellers exactly what verified vs. unverified users will see
- [ ] Integrate Lummi.ai for generating relevant but non-specific business images
- [ ] Use OpenAI to process inputs and generate non-revealing business previews

### 3. Data Room Enhancements
- [ ] Implement public/private mode for Data Room
- [ ] Create "Public" view accessible to all users
- [ ] Develop "Private" view requiring seller permission
- [ ] Build permission management system for private data access

### 4. Hermes Zestimate (Valuation System)
- [ ] Develop initial valuation model
- [ ] Integrate BizBuySell data as baseline
- [ ] Create algorithm for basic business valuation
- [ ] Implement valuation range display instead of fixed numbers
- [ ] Design clear UI for displaying valuation information

### 5. Seller Experience Improvements
- [ ] Create clear information hierarchy
- [ ] Distinguish between required vs. recommended information fields
- [ ] Distinguish between required vs. recommended documents
- [ ] Implement progressive disclosure pattern for optional information
- [ ] Build listing enrichment system
- [ ] Allow minimal information for initial listing
- [ ] Create process for gradually enhancing listings with more details
- [ ] Add completion percentage indicator for listings

### 6. Buyer Experience (Logged-Out Users)
- [ ] Implement information security system
- [ ] Display summarized/obfuscated business information for non-registered users
- [ ] Create secure information reveal process
- [ ] Build user verification system (information upload or NDA signing)
- [ ] Design clear UI showing what information requires registration



## March 21 Fixes
- [ ] Fix Onboarding Flow
- [ ] Update the priceslider
- [ ] Find replacement for Enigma
- [ ] Make sure Collin project is ready
- [ ] Make sure to have the onboarding landing page
