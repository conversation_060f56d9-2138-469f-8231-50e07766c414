#!/usr/bin/env node

/**
 * <PERSON>ript to check for pending email notifications and trigger them
 * This should be run every minute by a cron job:
 * * * * * * /path/to/node /path/to/scripts/check-notifications.js
 */

const https = require('https');
const http = require('http');

// Configuration
const API_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
const CHECK_ENDPOINT = '/api/check-pending-notifications';

function makeRequest() {
    const url = `${API_URL}${CHECK_ENDPOINT}`;

    console.log(`[${new Date().toISOString()}] Checking for pending notifications...`);

    const options = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
    };

    // Use http or https based on the URL
    const client = url.startsWith('https:') ? https : http;
    const req = client.request(url, options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
            data += chunk;
        });

        res.on('end', () => {
            try {
                const result = JSON.parse(data);

                if (result.processed > 0) {
                    console.log(`✅ Processed ${result.processed} notifications`);
                } else {
                    console.log(`ℹ️ No notifications to process`);
                }
            } catch (error) {
                console.error('❌ Error parsing response:', error.message);
            }
        });
    });

    req.on('error', (error) => {
        console.error(`❌ Request failed:`, error.message);
    });

    req.end();
}

// Run the check
makeRequest(); 