/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'nafjqguzctzxhwgshcch.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
    domains: [
      'assets.lummi.ai',
      'www.lummi.ai'
    ],
  },
  webpack: (config, { isServer }) => {
    // Exclude problematic dependencies and paths
    config.resolve.alias = {
      ...config.resolve.alias,
      '@supabase/functions-js': false,
    };

    // Ignore all files in supabase/functions directory
    config.module.rules.push({
      test: /supabase\/functions\/.*/,
      use: 'ignore-loader'
    });

    return config;
  },
}

module.exports = nextConfig